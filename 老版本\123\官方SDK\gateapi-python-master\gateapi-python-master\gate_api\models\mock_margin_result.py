# coding: utf-8

"""
    Gate API v4

    Welcome to Gate.io API  APIv4 provides spot, margin and futures trading operations. There are public APIs to retrieve the real-time market statistics, and private APIs which needs authentication to trade on user's behalf.  # noqa: E501

    Contact: <EMAIL>
    Generated by: https://openapi-generator.tech
"""


import pprint
import re  # noqa: F401

import six

from gate_api.configuration import Configuration


class MockMarginResult(object):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        'type': 'str',
        'profit_loss_ranges': 'list[ProfitLossRange]',
        'max_loss': 'ProfitLossRange',
        'mr1': 'str',
        'mr2': 'str',
        'mr3': 'str',
        'mr4': 'str'
    }

    attribute_map = {
        'type': 'type',
        'profit_loss_ranges': 'profit_loss_ranges',
        'max_loss': 'max_loss',
        'mr1': 'mr1',
        'mr2': 'mr2',
        'mr3': 'mr3',
        'mr4': 'mr4'
    }

    def __init__(self, type=None, profit_loss_ranges=None, max_loss=None, mr1=None, mr2=None, mr3=None, mr4=None, local_vars_configuration=None):  # noqa: E501
        # type: (str, list[ProfitLossRange], ProfitLossRange, str, str, str, str, Configuration) -> None
        """MockMarginResult - a model defined in OpenAPI"""  # noqa: E501
        if local_vars_configuration is None:
            local_vars_configuration = Configuration()
        self.local_vars_configuration = local_vars_configuration

        self._type = None
        self._profit_loss_ranges = None
        self._max_loss = None
        self._mr1 = None
        self._mr2 = None
        self._mr3 = None
        self._mr4 = None
        self.discriminator = None

        if type is not None:
            self.type = type
        if profit_loss_ranges is not None:
            self.profit_loss_ranges = profit_loss_ranges
        if max_loss is not None:
            self.max_loss = max_loss
        if mr1 is not None:
            self.mr1 = mr1
        if mr2 is not None:
            self.mr2 = mr2
        if mr3 is not None:
            self.mr3 = mr3
        if mr4 is not None:
            self.mr4 = mr4

    @property
    def type(self):
        """Gets the type of this MockMarginResult.  # noqa: E501

        Position combination type `original_position` - Original position `long_delta_original_position` - Positive delta + Original position `short_delta_original_position` - Negative delta + Original position  # noqa: E501

        :return: The type of this MockMarginResult.  # noqa: E501
        :rtype: str
        """
        return self._type

    @type.setter
    def type(self, type):
        """Sets the type of this MockMarginResult.

        Position combination type `original_position` - Original position `long_delta_original_position` - Positive delta + Original position `short_delta_original_position` - Negative delta + Original position  # noqa: E501

        :param type: The type of this MockMarginResult.  # noqa: E501
        :type: str
        """

        self._type = type

    @property
    def profit_loss_ranges(self):
        """Gets the profit_loss_ranges of this MockMarginResult.  # noqa: E501

        The results of 33 pressure scenarios for MR1  # noqa: E501

        :return: The profit_loss_ranges of this MockMarginResult.  # noqa: E501
        :rtype: list[ProfitLossRange]
        """
        return self._profit_loss_ranges

    @profit_loss_ranges.setter
    def profit_loss_ranges(self, profit_loss_ranges):
        """Sets the profit_loss_ranges of this MockMarginResult.

        The results of 33 pressure scenarios for MR1  # noqa: E501

        :param profit_loss_ranges: The profit_loss_ranges of this MockMarginResult.  # noqa: E501
        :type: list[ProfitLossRange]
        """

        self._profit_loss_ranges = profit_loss_ranges

    @property
    def max_loss(self):
        """Gets the max_loss of this MockMarginResult.  # noqa: E501

        最大损失  # noqa: E501

        :return: The max_loss of this MockMarginResult.  # noqa: E501
        :rtype: ProfitLossRange
        """
        return self._max_loss

    @max_loss.setter
    def max_loss(self, max_loss):
        """Sets the max_loss of this MockMarginResult.

        最大损失  # noqa: E501

        :param max_loss: The max_loss of this MockMarginResult.  # noqa: E501
        :type: ProfitLossRange
        """

        self._max_loss = max_loss

    @property
    def mr1(self):
        """Gets the mr1 of this MockMarginResult.  # noqa: E501

        Stress testing  # noqa: E501

        :return: The mr1 of this MockMarginResult.  # noqa: E501
        :rtype: str
        """
        return self._mr1

    @mr1.setter
    def mr1(self, mr1):
        """Sets the mr1 of this MockMarginResult.

        Stress testing  # noqa: E501

        :param mr1: The mr1 of this MockMarginResult.  # noqa: E501
        :type: str
        """

        self._mr1 = mr1

    @property
    def mr2(self):
        """Gets the mr2 of this MockMarginResult.  # noqa: E501

        Basis spread risk  # noqa: E501

        :return: The mr2 of this MockMarginResult.  # noqa: E501
        :rtype: str
        """
        return self._mr2

    @mr2.setter
    def mr2(self, mr2):
        """Sets the mr2 of this MockMarginResult.

        Basis spread risk  # noqa: E501

        :param mr2: The mr2 of this MockMarginResult.  # noqa: E501
        :type: str
        """

        self._mr2 = mr2

    @property
    def mr3(self):
        """Gets the mr3 of this MockMarginResult.  # noqa: E501

        Volatility spread risk  # noqa: E501

        :return: The mr3 of this MockMarginResult.  # noqa: E501
        :rtype: str
        """
        return self._mr3

    @mr3.setter
    def mr3(self, mr3):
        """Sets the mr3 of this MockMarginResult.

        Volatility spread risk  # noqa: E501

        :param mr3: The mr3 of this MockMarginResult.  # noqa: E501
        :type: str
        """

        self._mr3 = mr3

    @property
    def mr4(self):
        """Gets the mr4 of this MockMarginResult.  # noqa: E501

        Option short risk  # noqa: E501

        :return: The mr4 of this MockMarginResult.  # noqa: E501
        :rtype: str
        """
        return self._mr4

    @mr4.setter
    def mr4(self, mr4):
        """Sets the mr4 of this MockMarginResult.

        Option short risk  # noqa: E501

        :param mr4: The mr4 of this MockMarginResult.  # noqa: E501
        :type: str
        """

        self._mr4 = mr4

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.openapi_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, MockMarginResult):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, MockMarginResult):
            return True

        return self.to_dict() != other.to_dict()
