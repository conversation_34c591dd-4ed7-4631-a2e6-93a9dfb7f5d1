{"analysis_timestamp": 1752310736519.0425, "code_consistency": {"timestamp": 1752310736519.0425, "code_analysis": {"unified_calculator": {"file_exists": false, "key_methods": [], "calculation_logic": {}, "precision_handling": {}, "issues_found": []}, "opportunity_scanner": {"file_exists": false, "calculation_methods": [], "data_sources": [], "issues_found": []}, "execution_engine": {"file_exists": false, "validation_logic": {}, "context_support": {}, "issues_found": []}}, "consistency_issues": ["UnifiedOrderSpreadCalculator文件不存在", "UnifiedOrderSpreadCalculator缺少关键方法: calculate_order_based_spread, build_cumulative_table_30_levels", "ExecutionEngine不支持execution_context参数"], "recommendations": ["🔧 发现以下需要修复的问题:", "   - UnifiedOrderSpreadCalculator文件不存在", "   - UnifiedOrderSpreadCalculator缺少关键方法: calculate_order_based_spread, build_cumulative_table_30_levels", "   - ExecutionEngine不支持execution_context参数", "建议实施自动化测试以验证差价计算精度", "建议添加日志记录以跟踪差价计算过程", "建议定期对比实际交易结果与计算结果"]}, "log_consistency": {"timestamp": 1752310736519.0425, "log_analysis": {"error": "日志文件不存在"}, "pattern_analysis": {}, "consistency_check": {}}, "stuck_spread_diagnosis": {"timestamp": 1752310736519.0425, "potential_causes": [{"cause": "WebSocket连接中断", "description": "价格数据流中断导致差价计算停止", "check_method": "检查WebSocket连接状态和重连机制", "severity": "high"}, {"cause": "OpportunityScanner循环阻塞", "description": "扫描循环被某个操作阻塞，无法继续处理新数据", "check_method": "检查扫描循环的异常处理和超时机制", "severity": "high"}, {"cause": "ExecutionEngine状态未重置", "description": "执行引擎状态卡在执行中，拒绝新的机会", "check_method": "检查is_executing标志和状态重置逻辑", "severity": "medium"}, {"cause": "缓存数据过期", "description": "订单簿缓存数据过期但未刷新", "check_method": "检查缓存TTL和刷新机制", "severity": "medium"}, {"cause": "网络延迟异常", "description": "网络延迟导致数据同步问题", "check_method": "监控网络延迟指标", "severity": "low"}], "diagnostic_steps": ["1. 检查WebSocket连接健康状态", "2. 验证OpportunityScanner心跳", "3. 检查ExecutionEngine状态标志", "4. 验证订单簿数据新鲜度", "5. 监控系统资源使用情况", "6. 检查日志中的异常信息"], "immediate_actions": ["重启WebSocket连接", "重置ExecutionEngine状态", "清理过期缓存", "检查系统资源", "查看错误日志"]}, "summary": {"overall_status": "needs_attention", "critical_issues": 3, "warnings": 0, "key_findings": ["UnifiedOrderSpreadCalculator文件不存在", "UnifiedOrderSpreadCalculator缺少关键方法: calculate_order_based_spread, build_cumulative_table_30_levels", "ExecutionEngine不支持execution_context参数"], "priority_actions": ["立即修复代码一致性问题", "实施实时监控机制", "添加自动化测试", "优化错误处理逻辑"]}}