#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
价差趋同监控器 - 负责监控价差趋同并触发平仓
从ExecutionEngine中分离出来的专门模块
"""

import asyncio
import time
import os
import inspect
from typing import Dict, List, Any, Optional, Tuple
from utils.logger import get_logger
from datetime import datetime

# 全局单例
_INSTANCE = None

def get_convergence_monitor():
    """获取ConvergenceMonitor单例"""
    global _INSTANCE
    if not _INSTANCE:
        logger = get_logger("ConvergenceMonitor")
        logger.warning("ConvergenceMonitor未初始化，返回None")
    return _INSTANCE

def init_convergence_monitor(config=None, exchanges=None, opportunity_scanner=None):
    """🔥 修复：初始化ConvergenceMonitor单例 - 添加OpportunityScanner依赖注入"""
    global _INSTANCE
    if _INSTANCE is None:
        if not config:
            # 🔥 修复：使用已导入的os模块
            # 🚨 修复：统一使用.env配置，不允许硬编码默认值
            config = {
                "CLOSE_SPREAD_MIN": os.getenv("CLOSE_SPREAD_MIN", "-0.003"),  # 🔥 修复：现货溢价0.3%阈值
                # 🔥 删除：CLOSE_SPREAD_MAX - 不再使用区间判断
                "MAX_CONVERGENCE_WAIT": os.getenv("MAX_CONVERGENCE_WAIT", "1800")  # 默认30分钟
            }
        _INSTANCE = ConvergenceMonitor(config, exchanges, opportunity_scanner)
    return _INSTANCE

class ConvergenceMonitor:
    """🔥 新增：价差趋同监控器 - 专门监控价差趋同至目标区间"""

    def __init__(self, config=None, exchanges=None, opportunity_scanner=None):
        """🔥 修复：添加OpportunityScanner依赖注入，解决价格获取问题"""
        self.logger = get_logger(self.__class__.__name__)
        self.exchanges = exchanges or {}
        self.opportunity_scanner = opportunity_scanner  # 🔥 关键修复：注入OpportunityScanner实例

        # 🚨 修复：统一使用.env配置，不允许硬编码默认值
        if not config:
            # 🔥 修复：使用已导入的os模块
            config = {
                "CLOSE_SPREAD_MIN": os.getenv("CLOSE_SPREAD_MIN", "-0.003"),  # 🔥 修复：现货溢价0.3%阈值
                # 🔥 删除：CLOSE_SPREAD_MAX - 不再使用区间判断
                "MAX_CONVERGENCE_WAIT": os.getenv("MAX_CONVERGENCE_WAIT", "1800")  # 默认30分钟
            }

        # 🚨 修复：.env中的值已经是小数格式，不需要除以100
        self.close_spread_min = float(config.get("CLOSE_SPREAD_MIN", "-0.003"))  # 🔥 现货溢价阈值
        # 🔥 删除：self.close_spread_max - 不再使用最大阈值
        self.max_wait_time = int(config.get("MAX_CONVERGENCE_WAIT", "1800"))  # 默认30分钟

        # 添加活跃监控数据
        self.active_monitors = {}

        self.logger.info("✅ 价差趋同监控器初始化完成")
        self.logger.info(f"  📊 平仓阈值: 现货溢价≥{abs(self.close_spread_min)*100:.1f}% (current_spread <= {self.close_spread_min})")
        self.logger.info(f"  ⏱️ 最大等待时间: {self.max_wait_time}秒")

    async def start_monitoring(self, symbol: str, spot_exchange: Any, futures_exchange: Any,
                             initial_spread: float = None, target_spread: float = None,
                             opening_snapshot: Dict[str, Any] = None) -> bool:
        """🔥 修复：开始监控价差趋同 - 支持开仓快照数据"""
        try:
            # 停止已有的监控
            if symbol in self.active_monitors:
                self.logger.info(f"⚠️ 已存在监控: {symbol}，先停止")
                await self.stop_monitoring(symbol)

            # 🔥 修复：使用已导入的os模块

            # 🚨 修复：统一使用.env配置，不允许硬编码默认值
            if target_spread is None:
                target_spread = float(os.getenv("CLOSE_SPREAD_MIN", "0.0005"))  # 🔥 修复：添加默认值，避免None错误

            # 将百分比转换为小数
            target_spread_decimal = target_spread / 100.0 if target_spread > 1.0 else target_spread

            # 🔥 **关键修复**：创建新的监控信息，包含开仓快照
            self.active_monitors[symbol] = {
                "spot_exchange": spot_exchange,
                "futures_exchange": futures_exchange,
                "start_time": time.time(),  # 使用时间戳
                "initial_spread": initial_spread,
                "target_spread": target_spread_decimal,
                "last_spread": initial_spread,
                "last_update_time": time.time(),
                "is_active": True,
                "opening_snapshot": opening_snapshot,  # 🔥 新增：保存开仓时的快照数据
                "last_snapshot": None  # 🔥 新增：保存最后一次快照
            }
            
            self.logger.info(f"🔍 开始监控价差趋同: {symbol} ({spot_exchange} ↔ {futures_exchange})")
            self.logger.info(f"   初始价差: {initial_spread*100:.2f}%")
            self.logger.info(f"   目标价差: {target_spread_decimal*100:.2f}%")
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 启动趋同监控失败: {e}")
            return False

    async def stop_monitoring(self, symbol: str, reason: str = "手动停止") -> bool:
        """停止监控指定交易对的价差趋同"""
        if symbol in self.active_monitors:
            self.active_monitors[symbol]["is_active"] = False
            
            elapsed = time.time() - self.active_monitors[symbol]["start_time"]
            self.logger.info(f"⏹️ 停止监控 {symbol}，持续时间: {elapsed:.1f}秒，原因: {reason}")
            
            return True
        return False
        
    async def resume_monitoring(self, symbol: str) -> bool:
        """🔥 新增：恢复监控 - 平仓失败后继续监控价差"""
        if symbol in self.active_monitors:
            monitor_info = self.active_monitors[symbol]
            if not monitor_info["is_active"]:
                monitor_info["is_active"] = True
                self.logger.info(f"🔄 恢复监控 {symbol}，继续等待价差趋同")
                return True
            else:
                self.logger.debug(f"📊 {symbol} 监控已经是活跃状态")
                return True
        else:
            self.logger.warning(f"⚠️ 无法恢复监控 {symbol}，未找到监控记录")
            return False

    async def get_current_spread_with_opening_snapshot(self, symbol: str) -> float:
        """🔥 新增：使用开仓快照数据进行趋同监控 - 确保数据一致性"""
        if symbol not in self.active_monitors:
            self.logger.warning(f"⚠️ 未找到监控: {symbol}")
            return 0.0

        monitor_info = self.active_monitors[symbol]
        opening_snapshot = monitor_info.get("opening_snapshot")

        if opening_snapshot:
            # 🔥 使用开仓时的快照数据，确保与开仓计算完全一致
            try:
                from core.unified_order_spread_calculator import get_order_spread_calculator
                calculator = get_order_spread_calculator()

                # 🔥 使用开仓快照中的订单簿数据
                order_result = calculator.calculate_order_based_spread(
                    opening_snapshot['spot_orderbook'],
                    opening_snapshot['futures_orderbook'],
                    100.0, "opening"
                )

                if order_result:
                    self.logger.debug(f"✅ 使用开仓快照数据: {symbol}")
                    self.logger.debug(f"   快照时间戳: {opening_snapshot['snapshot_timestamp']}")
                    return order_result.executable_spread
                else:
                    self.logger.warning(f"⚠️ 开仓快照计算失败: {symbol}")

            except Exception as e:
                self.logger.error(f"❌ 开仓快照计算异常: {e}")

        # 🔥 备用方案：使用实时数据快照
        return await self.get_current_spread(symbol)

    async def get_current_spread(self, symbol: str) -> float:
        """🔥 修复：获取当前价差 - 使用DataSnapshotValidator统一快照机制"""
        if symbol not in self.active_monitors:
            self.logger.warning(f"⚠️ 未找到监控: {symbol}")
            return 0.0

        monitor_info = self.active_monitors[symbol]

        # 🚀 新增：详细时间戳记录
        start_time = time.time()

        try:
            # 🔥 关键修复：使用OpportunityScanner.market_data获取实时价格
            if not self.opportunity_scanner:
                self.logger.warning("⚠️ OpportunityScanner实例未注入，返回上次记录的价差")
                return monitor_info.get("last_spread", 0)

            # 🔥 获取交易所名称（标准化处理）
            spot_exchange_name = self._get_exchange_name(monitor_info["spot_exchange"])
            futures_exchange_name = self._get_exchange_name(monitor_info["futures_exchange"])

            # 🔥 构造数据key，符合OpportunityScanner.market_data的key格式
            spot_key = f"{spot_exchange_name}_spot_{symbol}"
            futures_key = f"{futures_exchange_name}_futures_{symbol}"

            # 🔥 从OpportunityScanner.market_data获取市场数据
            spot_market_data = self.opportunity_scanner.market_data.get(spot_key)
            futures_market_data = self.opportunity_scanner.market_data.get(futures_key)

            if not spot_market_data or not futures_market_data:
                self.logger.error(f"❌ 缺少市场数据: spot={bool(spot_market_data)}, futures={bool(futures_market_data)}")
                return monitor_info.get("last_spread", 0)

            # 🔥 验证价格数据有效性
            spot_price = spot_market_data.price
            futures_price = futures_market_data.price
            if spot_price is None or futures_price is None or spot_price <= 0 or futures_price <= 0:
                self.logger.debug(f"⚠️ 价格数据不完整: spot={spot_price}, futures={futures_price}")
                return monitor_info.get("last_spread", 0)

            # 🔥 **核心修复**：添加与开仓完全相同的DataSnapshotValidator快照机制
            spot_orderbook = spot_market_data.orderbook if hasattr(spot_market_data, 'orderbook') else {}
            futures_orderbook = futures_market_data.orderbook if hasattr(futures_market_data, 'orderbook') else {}

            # 🔥 **关键修复1**：使用DataSnapshotValidator创建统一快照
            from core.data_snapshot_validator import DataSnapshotValidator
            snapshot_validator = DataSnapshotValidator()

            # 🔥 **关键修复2**：创建数据快照，确保时间戳一致性
            data_snapshot = snapshot_validator.create_validated_snapshot(
                spot_market_data, futures_market_data,
                spot_orderbook, futures_orderbook
            )

            if not data_snapshot:
                self.logger.warning(f"⚠️ 数据快照创建失败: {symbol}")
                return monitor_info.get("last_spread", 0)

            # 🔥 **关键修复3**：使用快照数据进行计算，确保与开仓一致
            from core.unified_order_spread_calculator import get_order_spread_calculator
            calculator = get_order_spread_calculator()

            # 🔥 使用快照中的订单簿数据，确保数据一致性
            order_result = calculator.calculate_order_based_spread(
                data_snapshot['spot_orderbook'],
                data_snapshot['futures_orderbook'],
                100.0, "opening"  # 🔥 修复：使用opening上下文，与OpportunityScanner一致
            )

            if order_result is None:
                self.logger.warning(f"⚠️ Order差价计算被拒绝，可能因滑点过大: {symbol}")
                self.logger.warning(f"   现货订单簿: {len(spot_orderbook.get('asks', []))}档asks, {len(spot_orderbook.get('bids', []))}档bids")
                self.logger.warning(f"   期货订单簿: {len(futures_orderbook.get('asks', []))}档asks, {len(futures_orderbook.get('bids', []))}档bids")
                self.logger.warning(f"   建议: 检查{symbol}的流动性或调整滑点阈值")
                return monitor_info.get("last_spread", 0)

            # 🔥 **关键修复4**：将计算结果添加到快照中（缓存机制）
            data_snapshot['calculation_result'] = order_result

            # 🔥 使用Order计算结果中的加权平均价格，确保与OpportunityScanner一致
            current_spread = order_result.executable_spread
            spot_price = order_result.spot_execution_price
            futures_price = order_result.futures_execution_price

            # 🔥 **关键修复5**：记录快照元数据，追踪数据来源
            self.logger.debug(f"✅ 使用数据快照计算价差: {symbol}")
            self.logger.debug(f"   快照时间戳: {data_snapshot['snapshot_timestamp']}")
            self.logger.debug(f"   验证元数据: {data_snapshot.get('validation_metadata', {})}")

            # 🔥 **关键修复6**：更新监控信息（使用快照计算结果）
            monitor_info["last_spread"] = current_spread
            monitor_info["last_update_time"] = time.time()
            monitor_info["spot_price"] = spot_price  # Order加权平均价格
            monitor_info["futures_price"] = futures_price  # Order加权平均价格
            monitor_info["last_snapshot"] = data_snapshot  # 🔥 新增：保存最后一次快照

            # 🚀 新增：详细时间戳日志
            elapsed_ms = (time.time() - start_time) * 1000
            elapsed_total = time.time() - monitor_info["start_time"]

            # 🔥 统一精度：价格和差价都使用8位小数，确保最高精度
            self.logger.info(f"📊 [价差跟踪-快照] {symbol} | "
                           f"现货${spot_price:.8f} ↔ 期货${futures_price:.8f} | "
                           f"差价={current_spread*100:.8f}% | "
                           f"获取耗时={elapsed_ms:.1f}ms | "
                           f"监控总时长={elapsed_total:.3f}s | "
                           f"快照时间戳={data_snapshot['snapshot_timestamp']} | "
                           f"上下文=opening")

            return current_spread

        except Exception as e:
            self.logger.error(f"❌ 获取价差异常: {e}")
            return monitor_info.get("last_spread", 0)

    async def detect_convergence_signal(self, symbol: str) -> bool:
        """检测价差是否已经趋同到目标区间"""
        if symbol not in self.active_monitors or not self.active_monitors[symbol]["is_active"]:
            return False

        monitor_info = self.active_monitors[symbol]

        # 🚀 新增：趋同检测时间戳记录
        detection_start = time.time()

        current_spread = await self.get_current_spread(symbol)
        target_spread = monitor_info["target_spread"]

        # 🔥 修复：简化平仓逻辑，使用单一阈值判断
        # CLOSE_SPREAD_MIN=-0.001 表示现货溢价0.1%阈值
        # 当 current_spread <= -0.001 时平仓

        # 🚀 新增：详细趋同检测日志
        elapsed_total = time.time() - monitor_info["start_time"]
        self.logger.info(f"🔍 [趋同检测] {symbol} | "
                        f"当前价差={current_spread*100:.3f}% | "
                        f"平仓阈值={self.close_spread_min*100:.1f}% | "
                        f"监控时长={elapsed_total:.3f}s")

        # 🚀 新增：详细逻辑判断日志
        detection_time = (time.time() - detection_start) * 1000

        # 🔥 **关键修复**：平仓逻辑需要考虑执行上下文
        # 现在ConvergenceMonitor使用"opening"上下文计算差价，与OpportunityScanner一致
        # 但平仓判断需要考虑实际的平仓成本

        # 🔥 核心平仓逻辑：当现货溢价达到阈值时平仓
        # current_spread <= CLOSE_SPREAD_MIN 表示现货溢价≥阈值
        should_close = current_spread <= self.close_spread_min

        if should_close:
            self.logger.info(f"✅ 现货溢价达到平仓阈值: {symbol} | "
                           f"当前价差={current_spread*100:.3f}% <= 阈值={self.close_spread_min*100:.1f}% | "
                           f"检测耗时={detection_time:.1f}ms")
            return True
        else:
            if current_spread >= 0:
                self.logger.debug(f"⏳ 仍为期货溢价: {symbol} | 当前={current_spread*100:.3f}% (等待价差收敛)")
            else:
                self.logger.debug(f"⏳ 现货溢价未达阈值: {symbol} | 当前={current_spread*100:.3f}% > 阈值={self.close_spread_min*100:.1f}%")
            
        # 检查是否超时
        elapsed = time.time() - monitor_info["start_time"]
        # 🔥 修复：使用已导入的os模块
        # 🔥 修复：使用从配置读取的最大等待时间，不再硬编码
        max_wait = int(os.getenv("MAX_CONVERGENCE_WAIT", str(self.max_wait_time)))
        
        if elapsed > max_wait:
            self.logger.warning(f"⚠️ 趋同监控超时: {elapsed:.1f}秒 > {max_wait}秒")
            return False
            
        return False

    def get_monitoring_status(self, symbol: str) -> Dict:
        """获取价差监控状态"""
        try:
            if symbol not in self.active_monitors:
                return {"active": False, "symbol": symbol}
            
            monitor_info = self.active_monitors[symbol]
            current_time = time.time()
            
            return {
                "active": True,
                "symbol": symbol,
                "spot_exchange": monitor_info["spot_exchange"],
                "futures_exchange": monitor_info["futures_exchange"],
                "duration": current_time - monitor_info["start_time"],
                "initial_spread": monitor_info["initial_spread"],
                "initial_spread_percent": monitor_info["initial_spread"] * 100,
                "current_spread": monitor_info["last_spread"],
                "current_spread_percent": monitor_info["last_spread"] * 100,
                "target_spread": monitor_info["target_spread"],
                "target_spread_percent": monitor_info["target_spread"] * 100,
                "last_update_time": monitor_info["last_update_time"],
                "spot_price": monitor_info["spot_price"],
                "futures_price": monitor_info["futures_price"]
            }
        
        except Exception as e:
            self.logger.error(f"获取监控状态异常: {e}")
            return {"active": False, "symbol": symbol, "error": str(e)}

    async def monitor_price_convergence(self, symbol: str) -> Dict[str, Any]:
        """🔥 监控价格趋同 - 按照全流程工作流.md要求"""
        try:
            if symbol not in self.active_monitors:
                return {"error": "未找到活跃监控", "symbol": symbol}
            
            monitor_info = self.active_monitors[symbol]
            
            # 获取当前价差
            current_spread = await self.get_current_spread(symbol)
            
            # 检查趋同状态
            is_converged = await self.detect_convergence_signal(symbol)
            
            # 计算监控时长
            elapsed_time = time.time() - monitor_info["start_time"]
            
            return {
                "symbol": symbol,
                "current_spread": current_spread,
                "current_spread_percent": current_spread * 100,
                "target_spread": monitor_info["target_spread"],
                "target_spread_percent": monitor_info["target_spread"] * 100,
                "is_converged": is_converged,
                "elapsed_time": elapsed_time,
                "spot_price": monitor_info.get("spot_price", 0),
                "futures_price": monitor_info.get("futures_price", 0),
                "status": "converged" if is_converged else "monitoring"
            }
            
        except Exception as e:
            self.logger.error(f"❌ 监控价格趋同异常: {e}")
            return {"error": str(e), "symbol": symbol}

    def _get_exchange_name(self, exchange) -> str:
        """🔥 使用统一的交易所名称获取函数"""
        from exchanges.exchanges_base import get_exchange_name
        return get_exchange_name(exchange)

    def is_convergence_target_reached(self, current_spread: float, target_spread: float = None) -> bool:
        """🔥 修复：检查是否达到平仓阈值 - 使用单一阈值判断"""

        # 🔥 简化逻辑：使用单一阈值判断
        # CLOSE_SPREAD_MIN=-0.001 表示现货溢价0.1%阈值
        # 当 current_spread <= -0.001 时平仓
        return current_spread <= self.close_spread_min

    def get_convergence_status(self, current_spread: float, elapsed_time: float) -> Dict:
        """获取趋同状态信息"""
        try:
            remaining_time = max(0, self.max_wait_time - elapsed_time)
            progress = elapsed_time / self.max_wait_time if self.max_wait_time > 0 else 1.0
            
            target_reached = self.is_convergence_target_reached(current_spread)
            
            status = {
                "current_spread": current_spread,
                "current_spread_percent": current_spread * 100,
                "close_threshold": self.close_spread_min,  # 🔥 修复：使用单一阈值
                "close_threshold_percent": self.close_spread_min * 100,
                "elapsed_time": elapsed_time,
                "remaining_time": remaining_time,
                "max_wait_time": self.max_wait_time,
                "progress": progress,
                "target_reached": target_reached,
                "timeout": remaining_time <= 0
            }
            
            return status
            
        except Exception as e:
            self.logger.error(f"获取趋同状态失败: {e}")
            return {"error": str(e)}


# 🔥 生产环境：移除测试代码，保持监控模块纯净