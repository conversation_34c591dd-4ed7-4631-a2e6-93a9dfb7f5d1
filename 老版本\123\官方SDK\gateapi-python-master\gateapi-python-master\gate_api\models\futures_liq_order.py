# coding: utf-8

"""
    Gate API v4

    Welcome to Gate.io API  APIv4 provides spot, margin and futures trading operations. There are public APIs to retrieve the real-time market statistics, and private APIs which needs authentication to trade on user's behalf.  # noqa: E501

    Contact: <EMAIL>
    Generated by: https://openapi-generator.tech
"""


import pprint
import re  # noqa: F401

import six

from gate_api.configuration import Configuration


class FuturesLiqOrder(object):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        'time': 'int',
        'contract': 'str',
        'size': 'int',
        'order_size': 'int',
        'order_price': 'str',
        'fill_price': 'str',
        'left': 'int'
    }

    attribute_map = {
        'time': 'time',
        'contract': 'contract',
        'size': 'size',
        'order_size': 'order_size',
        'order_price': 'order_price',
        'fill_price': 'fill_price',
        'left': 'left'
    }

    def __init__(self, time=None, contract=None, size=None, order_size=None, order_price=None, fill_price=None, left=None, local_vars_configuration=None):  # noqa: E501
        # type: (int, str, int, int, str, str, int, Configuration) -> None
        """FuturesLiqOrder - a model defined in OpenAPI"""  # noqa: E501
        if local_vars_configuration is None:
            local_vars_configuration = Configuration()
        self.local_vars_configuration = local_vars_configuration

        self._time = None
        self._contract = None
        self._size = None
        self._order_size = None
        self._order_price = None
        self._fill_price = None
        self._left = None
        self.discriminator = None

        if time is not None:
            self.time = time
        if contract is not None:
            self.contract = contract
        if size is not None:
            self.size = size
        if order_size is not None:
            self.order_size = order_size
        if order_price is not None:
            self.order_price = order_price
        if fill_price is not None:
            self.fill_price = fill_price
        if left is not None:
            self.left = left

    @property
    def time(self):
        """Gets the time of this FuturesLiqOrder.  # noqa: E501

        Liquidation time  # noqa: E501

        :return: The time of this FuturesLiqOrder.  # noqa: E501
        :rtype: int
        """
        return self._time

    @time.setter
    def time(self, time):
        """Sets the time of this FuturesLiqOrder.

        Liquidation time  # noqa: E501

        :param time: The time of this FuturesLiqOrder.  # noqa: E501
        :type: int
        """

        self._time = time

    @property
    def contract(self):
        """Gets the contract of this FuturesLiqOrder.  # noqa: E501

        Futures contract  # noqa: E501

        :return: The contract of this FuturesLiqOrder.  # noqa: E501
        :rtype: str
        """
        return self._contract

    @contract.setter
    def contract(self, contract):
        """Sets the contract of this FuturesLiqOrder.

        Futures contract  # noqa: E501

        :param contract: The contract of this FuturesLiqOrder.  # noqa: E501
        :type: str
        """

        self._contract = contract

    @property
    def size(self):
        """Gets the size of this FuturesLiqOrder.  # noqa: E501

        User position size  # noqa: E501

        :return: The size of this FuturesLiqOrder.  # noqa: E501
        :rtype: int
        """
        return self._size

    @size.setter
    def size(self, size):
        """Sets the size of this FuturesLiqOrder.

        User position size  # noqa: E501

        :param size: The size of this FuturesLiqOrder.  # noqa: E501
        :type: int
        """

        self._size = size

    @property
    def order_size(self):
        """Gets the order_size of this FuturesLiqOrder.  # noqa: E501

        Number of forced liquidation orders  # noqa: E501

        :return: The order_size of this FuturesLiqOrder.  # noqa: E501
        :rtype: int
        """
        return self._order_size

    @order_size.setter
    def order_size(self, order_size):
        """Sets the order_size of this FuturesLiqOrder.

        Number of forced liquidation orders  # noqa: E501

        :param order_size: The order_size of this FuturesLiqOrder.  # noqa: E501
        :type: int
        """

        self._order_size = order_size

    @property
    def order_price(self):
        """Gets the order_price of this FuturesLiqOrder.  # noqa: E501

        Liquidation order price  # noqa: E501

        :return: The order_price of this FuturesLiqOrder.  # noqa: E501
        :rtype: str
        """
        return self._order_price

    @order_price.setter
    def order_price(self, order_price):
        """Sets the order_price of this FuturesLiqOrder.

        Liquidation order price  # noqa: E501

        :param order_price: The order_price of this FuturesLiqOrder.  # noqa: E501
        :type: str
        """

        self._order_price = order_price

    @property
    def fill_price(self):
        """Gets the fill_price of this FuturesLiqOrder.  # noqa: E501

        Liquidation order average taker price  # noqa: E501

        :return: The fill_price of this FuturesLiqOrder.  # noqa: E501
        :rtype: str
        """
        return self._fill_price

    @fill_price.setter
    def fill_price(self, fill_price):
        """Sets the fill_price of this FuturesLiqOrder.

        Liquidation order average taker price  # noqa: E501

        :param fill_price: The fill_price of this FuturesLiqOrder.  # noqa: E501
        :type: str
        """

        self._fill_price = fill_price

    @property
    def left(self):
        """Gets the left of this FuturesLiqOrder.  # noqa: E501

        System liquidation order maker size  # noqa: E501

        :return: The left of this FuturesLiqOrder.  # noqa: E501
        :rtype: int
        """
        return self._left

    @left.setter
    def left(self, left):
        """Sets the left of this FuturesLiqOrder.

        System liquidation order maker size  # noqa: E501

        :param left: The left of this FuturesLiqOrder.  # noqa: E501
        :type: int
        """

        self._left = left

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.openapi_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, FuturesLiqOrder):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, FuturesLiqOrder):
            return True

        return self.to_dict() != other.to_dict()
