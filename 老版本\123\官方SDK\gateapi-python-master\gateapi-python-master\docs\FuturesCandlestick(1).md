# FuturesCandlestick

data point in every timestamp
## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**t** | **float** | Unix timestamp in seconds | [optional] 
**v** | **int** | size volume (contract size). Only returned if &#x60;contract&#x60; is not prefixed | [optional] 
**c** | **str** | Close price (quote currency) | [optional] 
**h** | **str** | Highest price (quote currency) | [optional] 
**l** | **str** | Lowest price (quote currency) | [optional] 
**o** | **str** | Open price (quote currency) | [optional] 
**sum** | **str** | Trading volume (unit: Quote currency) | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


