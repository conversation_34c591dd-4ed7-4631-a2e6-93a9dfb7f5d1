# CountdownCancelAllFuturesTask

Countdown cancel task detail
## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**timeout** | **int** | Countdown time, in seconds  At least 5 seconds, 0 means cancel the countdown | 
**contract** | **str** | Futures contract | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


