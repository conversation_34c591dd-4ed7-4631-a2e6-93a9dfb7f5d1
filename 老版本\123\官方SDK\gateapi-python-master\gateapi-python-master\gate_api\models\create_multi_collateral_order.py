# coding: utf-8

"""
    Gate API v4

    Welcome to Gate.io API  APIv4 provides spot, margin and futures trading operations. There are public APIs to retrieve the real-time market statistics, and private APIs which needs authentication to trade on user's behalf.  # noqa: E501

    Contact: <EMAIL>
    Generated by: https://openapi-generator.tech
"""


import pprint
import re  # noqa: F401

import six

from gate_api.configuration import Configuration


class CreateMultiCollateralOrder(object):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        'order_id': 'str',
        'order_type': 'str',
        'fixed_type': 'str',
        'fixed_rate': 'str',
        'auto_renew': 'bool',
        'auto_repay': 'bool',
        'borrow_currency': 'str',
        'borrow_amount': 'str',
        'collateral_currencies': 'list[CollateralCurrency]'
    }

    attribute_map = {
        'order_id': 'order_id',
        'order_type': 'order_type',
        'fixed_type': 'fixed_type',
        'fixed_rate': 'fixed_rate',
        'auto_renew': 'auto_renew',
        'auto_repay': 'auto_repay',
        'borrow_currency': 'borrow_currency',
        'borrow_amount': 'borrow_amount',
        'collateral_currencies': 'collateral_currencies'
    }

    def __init__(self, order_id=None, order_type=None, fixed_type=None, fixed_rate=None, auto_renew=None, auto_repay=None, borrow_currency=None, borrow_amount=None, collateral_currencies=None, local_vars_configuration=None):  # noqa: E501
        # type: (str, str, str, str, bool, bool, str, str, list[CollateralCurrency], Configuration) -> None
        """CreateMultiCollateralOrder - a model defined in OpenAPI"""  # noqa: E501
        if local_vars_configuration is None:
            local_vars_configuration = Configuration()
        self.local_vars_configuration = local_vars_configuration

        self._order_id = None
        self._order_type = None
        self._fixed_type = None
        self._fixed_rate = None
        self._auto_renew = None
        self._auto_repay = None
        self._borrow_currency = None
        self._borrow_amount = None
        self._collateral_currencies = None
        self.discriminator = None

        if order_id is not None:
            self.order_id = order_id
        if order_type is not None:
            self.order_type = order_type
        if fixed_type is not None:
            self.fixed_type = fixed_type
        if fixed_rate is not None:
            self.fixed_rate = fixed_rate
        if auto_renew is not None:
            self.auto_renew = auto_renew
        if auto_repay is not None:
            self.auto_repay = auto_repay
        self.borrow_currency = borrow_currency
        self.borrow_amount = borrow_amount
        if collateral_currencies is not None:
            self.collateral_currencies = collateral_currencies

    @property
    def order_id(self):
        """Gets the order_id of this CreateMultiCollateralOrder.  # noqa: E501

        Order ID  # noqa: E501

        :return: The order_id of this CreateMultiCollateralOrder.  # noqa: E501
        :rtype: str
        """
        return self._order_id

    @order_id.setter
    def order_id(self, order_id):
        """Sets the order_id of this CreateMultiCollateralOrder.

        Order ID  # noqa: E501

        :param order_id: The order_id of this CreateMultiCollateralOrder.  # noqa: E501
        :type: str
        """

        self._order_id = order_id

    @property
    def order_type(self):
        """Gets the order_type of this CreateMultiCollateralOrder.  # noqa: E501

        current - current, fixed - fixed, if not specified, default to current  # noqa: E501

        :return: The order_type of this CreateMultiCollateralOrder.  # noqa: E501
        :rtype: str
        """
        return self._order_type

    @order_type.setter
    def order_type(self, order_type):
        """Sets the order_type of this CreateMultiCollateralOrder.

        current - current, fixed - fixed, if not specified, default to current  # noqa: E501

        :param order_type: The order_type of this CreateMultiCollateralOrder.  # noqa: E501
        :type: str
        """

        self._order_type = order_type

    @property
    def fixed_type(self):
        """Gets the fixed_type of this CreateMultiCollateralOrder.  # noqa: E501

        Fixed interest rate loan period: 7d - 7 days, 30d - 30 days. Must be provided for fixed  # noqa: E501

        :return: The fixed_type of this CreateMultiCollateralOrder.  # noqa: E501
        :rtype: str
        """
        return self._fixed_type

    @fixed_type.setter
    def fixed_type(self, fixed_type):
        """Sets the fixed_type of this CreateMultiCollateralOrder.

        Fixed interest rate loan period: 7d - 7 days, 30d - 30 days. Must be provided for fixed  # noqa: E501

        :param fixed_type: The fixed_type of this CreateMultiCollateralOrder.  # noqa: E501
        :type: str
        """

        self._fixed_type = fixed_type

    @property
    def fixed_rate(self):
        """Gets the fixed_rate of this CreateMultiCollateralOrder.  # noqa: E501

        Fixed interest rate, must be specified for fixed  # noqa: E501

        :return: The fixed_rate of this CreateMultiCollateralOrder.  # noqa: E501
        :rtype: str
        """
        return self._fixed_rate

    @fixed_rate.setter
    def fixed_rate(self, fixed_rate):
        """Sets the fixed_rate of this CreateMultiCollateralOrder.

        Fixed interest rate, must be specified for fixed  # noqa: E501

        :param fixed_rate: The fixed_rate of this CreateMultiCollateralOrder.  # noqa: E501
        :type: str
        """

        self._fixed_rate = fixed_rate

    @property
    def auto_renew(self):
        """Gets the auto_renew of this CreateMultiCollateralOrder.  # noqa: E501

        Fixed interest rate, automatic renewal  # noqa: E501

        :return: The auto_renew of this CreateMultiCollateralOrder.  # noqa: E501
        :rtype: bool
        """
        return self._auto_renew

    @auto_renew.setter
    def auto_renew(self, auto_renew):
        """Sets the auto_renew of this CreateMultiCollateralOrder.

        Fixed interest rate, automatic renewal  # noqa: E501

        :param auto_renew: The auto_renew of this CreateMultiCollateralOrder.  # noqa: E501
        :type: bool
        """

        self._auto_renew = auto_renew

    @property
    def auto_repay(self):
        """Gets the auto_repay of this CreateMultiCollateralOrder.  # noqa: E501

        Fixed interest rate, automatic repayment  # noqa: E501

        :return: The auto_repay of this CreateMultiCollateralOrder.  # noqa: E501
        :rtype: bool
        """
        return self._auto_repay

    @auto_repay.setter
    def auto_repay(self, auto_repay):
        """Sets the auto_repay of this CreateMultiCollateralOrder.

        Fixed interest rate, automatic repayment  # noqa: E501

        :param auto_repay: The auto_repay of this CreateMultiCollateralOrder.  # noqa: E501
        :type: bool
        """

        self._auto_repay = auto_repay

    @property
    def borrow_currency(self):
        """Gets the borrow_currency of this CreateMultiCollateralOrder.  # noqa: E501

        Borrowed currency  # noqa: E501

        :return: The borrow_currency of this CreateMultiCollateralOrder.  # noqa: E501
        :rtype: str
        """
        return self._borrow_currency

    @borrow_currency.setter
    def borrow_currency(self, borrow_currency):
        """Sets the borrow_currency of this CreateMultiCollateralOrder.

        Borrowed currency  # noqa: E501

        :param borrow_currency: The borrow_currency of this CreateMultiCollateralOrder.  # noqa: E501
        :type: str
        """
        if self.local_vars_configuration.client_side_validation and borrow_currency is None:  # noqa: E501
            raise ValueError("Invalid value for `borrow_currency`, must not be `None`")  # noqa: E501

        self._borrow_currency = borrow_currency

    @property
    def borrow_amount(self):
        """Gets the borrow_amount of this CreateMultiCollateralOrder.  # noqa: E501

        Borrowing amount  # noqa: E501

        :return: The borrow_amount of this CreateMultiCollateralOrder.  # noqa: E501
        :rtype: str
        """
        return self._borrow_amount

    @borrow_amount.setter
    def borrow_amount(self, borrow_amount):
        """Sets the borrow_amount of this CreateMultiCollateralOrder.

        Borrowing amount  # noqa: E501

        :param borrow_amount: The borrow_amount of this CreateMultiCollateralOrder.  # noqa: E501
        :type: str
        """
        if self.local_vars_configuration.client_side_validation and borrow_amount is None:  # noqa: E501
            raise ValueError("Invalid value for `borrow_amount`, must not be `None`")  # noqa: E501

        self._borrow_amount = borrow_amount

    @property
    def collateral_currencies(self):
        """Gets the collateral_currencies of this CreateMultiCollateralOrder.  # noqa: E501

        Collateral currency and amount  # noqa: E501

        :return: The collateral_currencies of this CreateMultiCollateralOrder.  # noqa: E501
        :rtype: list[CollateralCurrency]
        """
        return self._collateral_currencies

    @collateral_currencies.setter
    def collateral_currencies(self, collateral_currencies):
        """Sets the collateral_currencies of this CreateMultiCollateralOrder.

        Collateral currency and amount  # noqa: E501

        :param collateral_currencies: The collateral_currencies of this CreateMultiCollateralOrder.  # noqa: E501
        :type: list[CollateralCurrency]
        """

        self._collateral_currencies = collateral_currencies

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.openapi_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateMultiCollateralOrder):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateMultiCollateralOrder):
            return True

        return self.to_dict() != other.to_dict()
