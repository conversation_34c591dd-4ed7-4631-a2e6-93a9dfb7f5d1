# coding: utf-8

"""
    Gate API v4

    Welcome to Gate.io API  APIv4 provides spot, margin and futures trading operations. There are public APIs to retrieve the real-time market statistics, and private APIs which needs authentication to trade on user's behalf.  # noqa: E501

    Contact: <EMAIL>
    Generated by: https://openapi-generator.tech
"""


import pprint
import re  # noqa: F401

import six

from gate_api.configuration import Configuration


class WithdrawalRecord(object):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        'id': 'str',
        'txid': 'str',
        'block_number': 'str',
        'withdraw_order_id': 'str',
        'timestamp': 'str',
        'amount': 'str',
        'fee': 'str',
        'currency': 'str',
        'fail_reason': 'str',
        'timestamp2': 'str',
        'memo': 'str',
        'status': 'str',
        'chain': 'str'
    }

    attribute_map = {
        'id': 'id',
        'txid': 'txid',
        'block_number': 'block_number',
        'withdraw_order_id': 'withdraw_order_id',
        'timestamp': 'timestamp',
        'amount': 'amount',
        'fee': 'fee',
        'currency': 'currency',
        'fail_reason': 'fail_reason',
        'timestamp2': 'timestamp2',
        'memo': 'memo',
        'status': 'status',
        'chain': 'chain'
    }

    def __init__(self, id=None, txid=None, block_number=None, withdraw_order_id=None, timestamp=None, amount=None, fee=None, currency=None, fail_reason=None, timestamp2=None, memo=None, status=None, chain=None, local_vars_configuration=None):  # noqa: E501
        # type: (str, str, str, str, str, str, str, str, str, str, str, str, str, Configuration) -> None
        """WithdrawalRecord - a model defined in OpenAPI"""  # noqa: E501
        if local_vars_configuration is None:
            local_vars_configuration = Configuration()
        self.local_vars_configuration = local_vars_configuration

        self._id = None
        self._txid = None
        self._block_number = None
        self._withdraw_order_id = None
        self._timestamp = None
        self._amount = None
        self._fee = None
        self._currency = None
        self._fail_reason = None
        self._timestamp2 = None
        self._memo = None
        self._status = None
        self._chain = None
        self.discriminator = None

        if id is not None:
            self.id = id
        if txid is not None:
            self.txid = txid
        if block_number is not None:
            self.block_number = block_number
        if withdraw_order_id is not None:
            self.withdraw_order_id = withdraw_order_id
        if timestamp is not None:
            self.timestamp = timestamp
        self.amount = amount
        if fee is not None:
            self.fee = fee
        self.currency = currency
        if fail_reason is not None:
            self.fail_reason = fail_reason
        if timestamp2 is not None:
            self.timestamp2 = timestamp2
        if memo is not None:
            self.memo = memo
        if status is not None:
            self.status = status
        self.chain = chain

    @property
    def id(self):
        """Gets the id of this WithdrawalRecord.  # noqa: E501

        Record ID  # noqa: E501

        :return: The id of this WithdrawalRecord.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this WithdrawalRecord.

        Record ID  # noqa: E501

        :param id: The id of this WithdrawalRecord.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def txid(self):
        """Gets the txid of this WithdrawalRecord.  # noqa: E501

        Hash record of the withdrawal  # noqa: E501

        :return: The txid of this WithdrawalRecord.  # noqa: E501
        :rtype: str
        """
        return self._txid

    @txid.setter
    def txid(self, txid):
        """Sets the txid of this WithdrawalRecord.

        Hash record of the withdrawal  # noqa: E501

        :param txid: The txid of this WithdrawalRecord.  # noqa: E501
        :type: str
        """

        self._txid = txid

    @property
    def block_number(self):
        """Gets the block_number of this WithdrawalRecord.  # noqa: E501

        区块编号  # noqa: E501

        :return: The block_number of this WithdrawalRecord.  # noqa: E501
        :rtype: str
        """
        return self._block_number

    @block_number.setter
    def block_number(self, block_number):
        """Sets the block_number of this WithdrawalRecord.

        区块编号  # noqa: E501

        :param block_number: The block_number of this WithdrawalRecord.  # noqa: E501
        :type: str
        """

        self._block_number = block_number

    @property
    def withdraw_order_id(self):
        """Gets the withdraw_order_id of this WithdrawalRecord.  # noqa: E501

        Client order id, up to 32 length and can only include 0-9, A-Z, a-z, underscore(_), hyphen(-) or dot(.)   # noqa: E501

        :return: The withdraw_order_id of this WithdrawalRecord.  # noqa: E501
        :rtype: str
        """
        return self._withdraw_order_id

    @withdraw_order_id.setter
    def withdraw_order_id(self, withdraw_order_id):
        """Sets the withdraw_order_id of this WithdrawalRecord.

        Client order id, up to 32 length and can only include 0-9, A-Z, a-z, underscore(_), hyphen(-) or dot(.)   # noqa: E501

        :param withdraw_order_id: The withdraw_order_id of this WithdrawalRecord.  # noqa: E501
        :type: str
        """

        self._withdraw_order_id = withdraw_order_id

    @property
    def timestamp(self):
        """Gets the timestamp of this WithdrawalRecord.  # noqa: E501

        Operation time  # noqa: E501

        :return: The timestamp of this WithdrawalRecord.  # noqa: E501
        :rtype: str
        """
        return self._timestamp

    @timestamp.setter
    def timestamp(self, timestamp):
        """Sets the timestamp of this WithdrawalRecord.

        Operation time  # noqa: E501

        :param timestamp: The timestamp of this WithdrawalRecord.  # noqa: E501
        :type: str
        """

        self._timestamp = timestamp

    @property
    def amount(self):
        """Gets the amount of this WithdrawalRecord.  # noqa: E501

        Currency amount  # noqa: E501

        :return: The amount of this WithdrawalRecord.  # noqa: E501
        :rtype: str
        """
        return self._amount

    @amount.setter
    def amount(self, amount):
        """Sets the amount of this WithdrawalRecord.

        Currency amount  # noqa: E501

        :param amount: The amount of this WithdrawalRecord.  # noqa: E501
        :type: str
        """
        if self.local_vars_configuration.client_side_validation and amount is None:  # noqa: E501
            raise ValueError("Invalid value for `amount`, must not be `None`")  # noqa: E501

        self._amount = amount

    @property
    def fee(self):
        """Gets the fee of this WithdrawalRecord.  # noqa: E501

        fee  # noqa: E501

        :return: The fee of this WithdrawalRecord.  # noqa: E501
        :rtype: str
        """
        return self._fee

    @fee.setter
    def fee(self, fee):
        """Sets the fee of this WithdrawalRecord.

        fee  # noqa: E501

        :param fee: The fee of this WithdrawalRecord.  # noqa: E501
        :type: str
        """

        self._fee = fee

    @property
    def currency(self):
        """Gets the currency of this WithdrawalRecord.  # noqa: E501

        Currency name  # noqa: E501

        :return: The currency of this WithdrawalRecord.  # noqa: E501
        :rtype: str
        """
        return self._currency

    @currency.setter
    def currency(self, currency):
        """Sets the currency of this WithdrawalRecord.

        Currency name  # noqa: E501

        :param currency: The currency of this WithdrawalRecord.  # noqa: E501
        :type: str
        """
        if self.local_vars_configuration.client_side_validation and currency is None:  # noqa: E501
            raise ValueError("Invalid value for `currency`, must not be `None`")  # noqa: E501

        self._currency = currency

    @property
    def fail_reason(self):
        """Gets the fail_reason of this WithdrawalRecord.  # noqa: E501

        The reason for withdrawal failure is that there is a value when status = CANCEL, and the rest of the state is empty  # noqa: E501

        :return: The fail_reason of this WithdrawalRecord.  # noqa: E501
        :rtype: str
        """
        return self._fail_reason

    @fail_reason.setter
    def fail_reason(self, fail_reason):
        """Sets the fail_reason of this WithdrawalRecord.

        The reason for withdrawal failure is that there is a value when status = CANCEL, and the rest of the state is empty  # noqa: E501

        :param fail_reason: The fail_reason of this WithdrawalRecord.  # noqa: E501
        :type: str
        """

        self._fail_reason = fail_reason

    @property
    def timestamp2(self):
        """Gets the timestamp2 of this WithdrawalRecord.  # noqa: E501

        The withdrawal end time, i.e.: withdrawal cancel time or withdrawal success time When status = CANCEL, the corresponding cancel time When status = DONE and block_number > 0, it is the time to withdrawal success  # noqa: E501

        :return: The timestamp2 of this WithdrawalRecord.  # noqa: E501
        :rtype: str
        """
        return self._timestamp2

    @timestamp2.setter
    def timestamp2(self, timestamp2):
        """Sets the timestamp2 of this WithdrawalRecord.

        The withdrawal end time, i.e.: withdrawal cancel time or withdrawal success time When status = CANCEL, the corresponding cancel time When status = DONE and block_number > 0, it is the time to withdrawal success  # noqa: E501

        :param timestamp2: The timestamp2 of this WithdrawalRecord.  # noqa: E501
        :type: str
        """

        self._timestamp2 = timestamp2

    @property
    def memo(self):
        """Gets the memo of this WithdrawalRecord.  # noqa: E501

        Additional remarks with regards to the withdrawal  # noqa: E501

        :return: The memo of this WithdrawalRecord.  # noqa: E501
        :rtype: str
        """
        return self._memo

    @memo.setter
    def memo(self, memo):
        """Sets the memo of this WithdrawalRecord.

        Additional remarks with regards to the withdrawal  # noqa: E501

        :param memo: The memo of this WithdrawalRecord.  # noqa: E501
        :type: str
        """

        self._memo = memo

    @property
    def status(self):
        """Gets the status of this WithdrawalRecord.  # noqa: E501

        Transaction status  - DONE: Completed (block_number > 0 is considered to be truly completed) - CANCEL: Canceled - REQUEST: Requesting - MANUAL: Pending manual review - BCODE: Recharge code operation - EXTPEND: Sent awaiting confirmation - FAIL: Failure on the chain awaiting confirmation - INVALID: Invalid order - VERIFY: Verifying - PROCES: Processing - PEND: Processing - DMOVE: pending manual review - REVIEW: Under review  # noqa: E501

        :return: The status of this WithdrawalRecord.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this WithdrawalRecord.

        Transaction status  - DONE: Completed (block_number > 0 is considered to be truly completed) - CANCEL: Canceled - REQUEST: Requesting - MANUAL: Pending manual review - BCODE: Recharge code operation - EXTPEND: Sent awaiting confirmation - FAIL: Failure on the chain awaiting confirmation - INVALID: Invalid order - VERIFY: Verifying - PROCES: Processing - PEND: Processing - DMOVE: pending manual review - REVIEW: Under review  # noqa: E501

        :param status: The status of this WithdrawalRecord.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def chain(self):
        """Gets the chain of this WithdrawalRecord.  # noqa: E501

        Name of the chain used in withdrawals  # noqa: E501

        :return: The chain of this WithdrawalRecord.  # noqa: E501
        :rtype: str
        """
        return self._chain

    @chain.setter
    def chain(self, chain):
        """Sets the chain of this WithdrawalRecord.

        Name of the chain used in withdrawals  # noqa: E501

        :param chain: The chain of this WithdrawalRecord.  # noqa: E501
        :type: str
        """
        if self.local_vars_configuration.client_side_validation and chain is None:  # noqa: E501
            raise ValueError("Invalid value for `chain`, must not be `None`")  # noqa: E501

        self._chain = chain

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.openapi_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, WithdrawalRecord):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, WithdrawalRecord):
            return True

        return self.to_dict() != other.to_dict()
