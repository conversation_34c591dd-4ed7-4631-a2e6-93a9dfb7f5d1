# coding: utf-8

"""
    Gate API v4

    Welcome to Gate.io API  APIv4 provides spot, margin and futures trading operations. There are public APIs to retrieve the real-time market statistics, and private APIs which needs authentication to trade on user's behalf.  # noqa: E501

    Contact: <EMAIL>
    Generated by: https://openapi-generator.tech
"""


import pprint
import re  # noqa: F401

import six

from gate_api.configuration import Configuration


class DepositAddress(object):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        'currency': 'str',
        'address': 'str',
        'multichain_addresses': 'list[MultiChainAddressItem]'
    }

    attribute_map = {
        'currency': 'currency',
        'address': 'address',
        'multichain_addresses': 'multichain_addresses'
    }

    def __init__(self, currency=None, address=None, multichain_addresses=None, local_vars_configuration=None):  # noqa: E501
        # type: (str, str, list[MultiChainAddressItem], Configuration) -> None
        """DepositAddress - a model defined in OpenAPI"""  # noqa: E501
        if local_vars_configuration is None:
            local_vars_configuration = Configuration()
        self.local_vars_configuration = local_vars_configuration

        self._currency = None
        self._address = None
        self._multichain_addresses = None
        self.discriminator = None

        self.currency = currency
        self.address = address
        if multichain_addresses is not None:
            self.multichain_addresses = multichain_addresses

    @property
    def currency(self):
        """Gets the currency of this DepositAddress.  # noqa: E501

        Currency detail  # noqa: E501

        :return: The currency of this DepositAddress.  # noqa: E501
        :rtype: str
        """
        return self._currency

    @currency.setter
    def currency(self, currency):
        """Sets the currency of this DepositAddress.

        Currency detail  # noqa: E501

        :param currency: The currency of this DepositAddress.  # noqa: E501
        :type: str
        """
        if self.local_vars_configuration.client_side_validation and currency is None:  # noqa: E501
            raise ValueError("Invalid value for `currency`, must not be `None`")  # noqa: E501

        self._currency = currency

    @property
    def address(self):
        """Gets the address of this DepositAddress.  # noqa: E501

        Deposit address  # noqa: E501

        :return: The address of this DepositAddress.  # noqa: E501
        :rtype: str
        """
        return self._address

    @address.setter
    def address(self, address):
        """Sets the address of this DepositAddress.

        Deposit address  # noqa: E501

        :param address: The address of this DepositAddress.  # noqa: E501
        :type: str
        """
        if self.local_vars_configuration.client_side_validation and address is None:  # noqa: E501
            raise ValueError("Invalid value for `address`, must not be `None`")  # noqa: E501

        self._address = address

    @property
    def multichain_addresses(self):
        """Gets the multichain_addresses of this DepositAddress.  # noqa: E501


        :return: The multichain_addresses of this DepositAddress.  # noqa: E501
        :rtype: list[MultiChainAddressItem]
        """
        return self._multichain_addresses

    @multichain_addresses.setter
    def multichain_addresses(self, multichain_addresses):
        """Sets the multichain_addresses of this DepositAddress.


        :param multichain_addresses: The multichain_addresses of this DepositAddress.  # noqa: E501
        :type: list[MultiChainAddressItem]
        """

        self._multichain_addresses = multichain_addresses

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.openapi_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DepositAddress):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DepositAddress):
            return True

        return self.to_dict() != other.to_dict()
