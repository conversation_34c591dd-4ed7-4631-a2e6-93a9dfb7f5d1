# coding: utf-8

"""
    Gate API v4

    Welcome to Gate.io API  APIv4 provides spot, margin and futures trading operations. There are public APIs to retrieve the real-time market statistics, and private APIs which needs authentication to trade on user's behalf.  # noqa: E501

    Contact: <EMAIL>
    Generated by: https://openapi-generator.tech
"""


import pprint
import re  # noqa: F401

import six

from gate_api.configuration import Configuration


class MarginLeverageTier(object):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        'upper_limit': 'str',
        'mmr': 'str',
        'leverage': 'str'
    }

    attribute_map = {
        'upper_limit': 'upper_limit',
        'mmr': 'mmr',
        'leverage': 'leverage'
    }

    def __init__(self, upper_limit=None, mmr=None, leverage=None, local_vars_configuration=None):  # noqa: E501
        # type: (str, str, str, Configuration) -> None
        """MarginLeverageTier - a model defined in OpenAPI"""  # noqa: E501
        if local_vars_configuration is None:
            local_vars_configuration = Configuration()
        self.local_vars_configuration = local_vars_configuration

        self._upper_limit = None
        self._mmr = None
        self._leverage = None
        self.discriminator = None

        if upper_limit is not None:
            self.upper_limit = upper_limit
        if mmr is not None:
            self.mmr = mmr
        if leverage is not None:
            self.leverage = leverage

    @property
    def upper_limit(self):
        """Gets the upper_limit of this MarginLeverageTier.  # noqa: E501

        Maximum loan limit  # noqa: E501

        :return: The upper_limit of this MarginLeverageTier.  # noqa: E501
        :rtype: str
        """
        return self._upper_limit

    @upper_limit.setter
    def upper_limit(self, upper_limit):
        """Sets the upper_limit of this MarginLeverageTier.

        Maximum loan limit  # noqa: E501

        :param upper_limit: The upper_limit of this MarginLeverageTier.  # noqa: E501
        :type: str
        """

        self._upper_limit = upper_limit

    @property
    def mmr(self):
        """Gets the mmr of this MarginLeverageTier.  # noqa: E501

        Maintenance margin rate  # noqa: E501

        :return: The mmr of this MarginLeverageTier.  # noqa: E501
        :rtype: str
        """
        return self._mmr

    @mmr.setter
    def mmr(self, mmr):
        """Sets the mmr of this MarginLeverageTier.

        Maintenance margin rate  # noqa: E501

        :param mmr: The mmr of this MarginLeverageTier.  # noqa: E501
        :type: str
        """

        self._mmr = mmr

    @property
    def leverage(self):
        """Gets the leverage of this MarginLeverageTier.  # noqa: E501

        Maximum leverage multiple  # noqa: E501

        :return: The leverage of this MarginLeverageTier.  # noqa: E501
        :rtype: str
        """
        return self._leverage

    @leverage.setter
    def leverage(self, leverage):
        """Sets the leverage of this MarginLeverageTier.

        Maximum leverage multiple  # noqa: E501

        :param leverage: The leverage of this MarginLeverageTier.  # noqa: E501
        :type: str
        """

        self._leverage = leverage

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.openapi_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, MarginLeverageTier):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, MarginLeverageTier):
            return True

        return self.to_dict() != other.to_dict()
