# coding: utf-8

"""
    Gate API v4

    Welcome to Gate.io API  APIv4 provides spot, margin and futures trading operations. There are public APIs to retrieve the real-time market statistics, and private APIs which needs authentication to trade on user's behalf.  # noqa: E501

    Contact: <EMAIL>
    Generated by: https://openapi-generator.tech
"""


import pprint
import re  # noqa: F401

import six

from gate_api.configuration import Configuration


class UnifiedSettings(object):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        'usdt_futures': 'bool',
        'spot_hedge': 'bool',
        'use_funding': 'bool',
        'options': 'bool'
    }

    attribute_map = {
        'usdt_futures': 'usdt_futures',
        'spot_hedge': 'spot_hedge',
        'use_funding': 'use_funding',
        'options': 'options'
    }

    def __init__(self, usdt_futures=None, spot_hedge=None, use_funding=None, options=None, local_vars_configuration=None):  # noqa: E501
        # type: (bool, bool, bool, bool, Configuration) -> None
        """UnifiedSettings - a model defined in OpenAPI"""  # noqa: E501
        if local_vars_configuration is None:
            local_vars_configuration = Configuration()
        self.local_vars_configuration = local_vars_configuration

        self._usdt_futures = None
        self._spot_hedge = None
        self._use_funding = None
        self._options = None
        self.discriminator = None

        if usdt_futures is not None:
            self.usdt_futures = usdt_futures
        if spot_hedge is not None:
            self.spot_hedge = spot_hedge
        if use_funding is not None:
            self.use_funding = use_funding
        if options is not None:
            self.options = options

    @property
    def usdt_futures(self):
        """Gets the usdt_futures of this UnifiedSettings.  # noqa: E501

        USDT contract switch. In cross-currency margin mode, it can only be turned on and not off  # noqa: E501

        :return: The usdt_futures of this UnifiedSettings.  # noqa: E501
        :rtype: bool
        """
        return self._usdt_futures

    @usdt_futures.setter
    def usdt_futures(self, usdt_futures):
        """Sets the usdt_futures of this UnifiedSettings.

        USDT contract switch. In cross-currency margin mode, it can only be turned on and not off  # noqa: E501

        :param usdt_futures: The usdt_futures of this UnifiedSettings.  # noqa: E501
        :type: bool
        """

        self._usdt_futures = usdt_futures

    @property
    def spot_hedge(self):
        """Gets the spot_hedge of this UnifiedSettings.  # noqa: E501

        Spot hedging switch.   # noqa: E501

        :return: The spot_hedge of this UnifiedSettings.  # noqa: E501
        :rtype: bool
        """
        return self._spot_hedge

    @spot_hedge.setter
    def spot_hedge(self, spot_hedge):
        """Sets the spot_hedge of this UnifiedSettings.

        Spot hedging switch.   # noqa: E501

        :param spot_hedge: The spot_hedge of this UnifiedSettings.  # noqa: E501
        :type: bool
        """

        self._spot_hedge = spot_hedge

    @property
    def use_funding(self):
        """Gets the use_funding of this UnifiedSettings.  # noqa: E501

        switch, when the mode is cross-currency margin mode, whether to use Uniloan financial funds as margin  # noqa: E501

        :return: The use_funding of this UnifiedSettings.  # noqa: E501
        :rtype: bool
        """
        return self._use_funding

    @use_funding.setter
    def use_funding(self, use_funding):
        """Sets the use_funding of this UnifiedSettings.

        switch, when the mode is cross-currency margin mode, whether to use Uniloan financial funds as margin  # noqa: E501

        :param use_funding: The use_funding of this UnifiedSettings.  # noqa: E501
        :type: bool
        """

        self._use_funding = use_funding

    @property
    def options(self):
        """Gets the options of this UnifiedSettings.  # noqa: E501

        Option switch. In cross-currency margin mode, it can only be turned on and not off  # noqa: E501

        :return: The options of this UnifiedSettings.  # noqa: E501
        :rtype: bool
        """
        return self._options

    @options.setter
    def options(self, options):
        """Sets the options of this UnifiedSettings.

        Option switch. In cross-currency margin mode, it can only be turned on and not off  # noqa: E501

        :param options: The options of this UnifiedSettings.  # noqa: E501
        :type: bool
        """

        self._options = options

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.openapi_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, UnifiedSettings):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, UnifiedSettings):
            return True

        return self.to_dict() != other.to_dict()
