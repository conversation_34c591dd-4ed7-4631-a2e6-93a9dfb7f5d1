# coding: utf-8

"""
    Gate API v4

    Welcome to Gate.io API  APIv4 provides spot, margin and futures trading operations. There are public APIs to retrieve the real-time market statistics, and private APIs which needs authentication to trade on user's behalf.  # noqa: E501

    Contact: <EMAIL>
    Generated by: https://openapi-generator.tech
"""


import pprint
import re  # noqa: F401

import six

from gate_api.configuration import Configuration


class CrossMarginAccount(object):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        'user_id': 'int',
        'refresh_time': 'int',
        'locked': 'bool',
        'balances': 'dict(str, CrossMarginBalance)',
        'total': 'str',
        'borrowed': 'str',
        'interest': 'str',
        'risk': 'str',
        'total_initial_margin': 'str',
        'total_margin_balance': 'str',
        'total_maintenance_margin': 'str',
        'total_initial_margin_rate': 'str',
        'total_maintenance_margin_rate': 'str',
        'total_available_margin': 'str',
        'portfolio_margin_total': 'str',
        'portfolio_margin_total_liab': 'str',
        'portfolio_margin_total_equity': 'str'
    }

    attribute_map = {
        'user_id': 'user_id',
        'refresh_time': 'refresh_time',
        'locked': 'locked',
        'balances': 'balances',
        'total': 'total',
        'borrowed': 'borrowed',
        'interest': 'interest',
        'risk': 'risk',
        'total_initial_margin': 'total_initial_margin',
        'total_margin_balance': 'total_margin_balance',
        'total_maintenance_margin': 'total_maintenance_margin',
        'total_initial_margin_rate': 'total_initial_margin_rate',
        'total_maintenance_margin_rate': 'total_maintenance_margin_rate',
        'total_available_margin': 'total_available_margin',
        'portfolio_margin_total': 'portfolio_margin_total',
        'portfolio_margin_total_liab': 'portfolio_margin_total_liab',
        'portfolio_margin_total_equity': 'portfolio_margin_total_equity'
    }

    def __init__(self, user_id=None, refresh_time=None, locked=None, balances=None, total=None, borrowed=None, interest=None, risk=None, total_initial_margin=None, total_margin_balance=None, total_maintenance_margin=None, total_initial_margin_rate=None, total_maintenance_margin_rate=None, total_available_margin=None, portfolio_margin_total=None, portfolio_margin_total_liab=None, portfolio_margin_total_equity=None, local_vars_configuration=None):  # noqa: E501
        # type: (int, int, bool, dict(str, CrossMarginBalance), str, str, str, str, str, str, str, str, str, str, str, str, str, Configuration) -> None
        """CrossMarginAccount - a model defined in OpenAPI"""  # noqa: E501
        if local_vars_configuration is None:
            local_vars_configuration = Configuration()
        self.local_vars_configuration = local_vars_configuration

        self._user_id = None
        self._refresh_time = None
        self._locked = None
        self._balances = None
        self._total = None
        self._borrowed = None
        self._interest = None
        self._risk = None
        self._total_initial_margin = None
        self._total_margin_balance = None
        self._total_maintenance_margin = None
        self._total_initial_margin_rate = None
        self._total_maintenance_margin_rate = None
        self._total_available_margin = None
        self._portfolio_margin_total = None
        self._portfolio_margin_total_liab = None
        self._portfolio_margin_total_equity = None
        self.discriminator = None

        if user_id is not None:
            self.user_id = user_id
        if refresh_time is not None:
            self.refresh_time = refresh_time
        if locked is not None:
            self.locked = locked
        if balances is not None:
            self.balances = balances
        if total is not None:
            self.total = total
        if borrowed is not None:
            self.borrowed = borrowed
        if interest is not None:
            self.interest = interest
        if risk is not None:
            self.risk = risk
        if total_initial_margin is not None:
            self.total_initial_margin = total_initial_margin
        if total_margin_balance is not None:
            self.total_margin_balance = total_margin_balance
        if total_maintenance_margin is not None:
            self.total_maintenance_margin = total_maintenance_margin
        if total_initial_margin_rate is not None:
            self.total_initial_margin_rate = total_initial_margin_rate
        if total_maintenance_margin_rate is not None:
            self.total_maintenance_margin_rate = total_maintenance_margin_rate
        if total_available_margin is not None:
            self.total_available_margin = total_available_margin
        if portfolio_margin_total is not None:
            self.portfolio_margin_total = portfolio_margin_total
        if portfolio_margin_total_liab is not None:
            self.portfolio_margin_total_liab = portfolio_margin_total_liab
        if portfolio_margin_total_equity is not None:
            self.portfolio_margin_total_equity = portfolio_margin_total_equity

    @property
    def user_id(self):
        """Gets the user_id of this CrossMarginAccount.  # noqa: E501

        User ID  # noqa: E501

        :return: The user_id of this CrossMarginAccount.  # noqa: E501
        :rtype: int
        """
        return self._user_id

    @user_id.setter
    def user_id(self, user_id):
        """Sets the user_id of this CrossMarginAccount.

        User ID  # noqa: E501

        :param user_id: The user_id of this CrossMarginAccount.  # noqa: E501
        :type: int
        """

        self._user_id = user_id

    @property
    def refresh_time(self):
        """Gets the refresh_time of this CrossMarginAccount.  # noqa: E501

        Time of the most recent refresh  # noqa: E501

        :return: The refresh_time of this CrossMarginAccount.  # noqa: E501
        :rtype: int
        """
        return self._refresh_time

    @refresh_time.setter
    def refresh_time(self, refresh_time):
        """Sets the refresh_time of this CrossMarginAccount.

        Time of the most recent refresh  # noqa: E501

        :param refresh_time: The refresh_time of this CrossMarginAccount.  # noqa: E501
        :type: int
        """

        self._refresh_time = refresh_time

    @property
    def locked(self):
        """Gets the locked of this CrossMarginAccount.  # noqa: E501

        Whether account is locked  # noqa: E501

        :return: The locked of this CrossMarginAccount.  # noqa: E501
        :rtype: bool
        """
        return self._locked

    @locked.setter
    def locked(self, locked):
        """Sets the locked of this CrossMarginAccount.

        Whether account is locked  # noqa: E501

        :param locked: The locked of this CrossMarginAccount.  # noqa: E501
        :type: bool
        """

        self._locked = locked

    @property
    def balances(self):
        """Gets the balances of this CrossMarginAccount.  # noqa: E501


        :return: The balances of this CrossMarginAccount.  # noqa: E501
        :rtype: dict(str, CrossMarginBalance)
        """
        return self._balances

    @balances.setter
    def balances(self, balances):
        """Sets the balances of this CrossMarginAccount.


        :param balances: The balances of this CrossMarginAccount.  # noqa: E501
        :type: dict(str, CrossMarginBalance)
        """

        self._balances = balances

    @property
    def total(self):
        """Gets the total of this CrossMarginAccount.  # noqa: E501

        Total account value in USDT, i.e., the sum of all currencies' `(available+freeze)*price*discount`  # noqa: E501

        :return: The total of this CrossMarginAccount.  # noqa: E501
        :rtype: str
        """
        return self._total

    @total.setter
    def total(self, total):
        """Sets the total of this CrossMarginAccount.

        Total account value in USDT, i.e., the sum of all currencies' `(available+freeze)*price*discount`  # noqa: E501

        :param total: The total of this CrossMarginAccount.  # noqa: E501
        :type: str
        """

        self._total = total

    @property
    def borrowed(self):
        """Gets the borrowed of this CrossMarginAccount.  # noqa: E501

        Total borrowed value in USDT, i.e., the sum of all currencies' `borrowed*price*discount`  # noqa: E501

        :return: The borrowed of this CrossMarginAccount.  # noqa: E501
        :rtype: str
        """
        return self._borrowed

    @borrowed.setter
    def borrowed(self, borrowed):
        """Sets the borrowed of this CrossMarginAccount.

        Total borrowed value in USDT, i.e., the sum of all currencies' `borrowed*price*discount`  # noqa: E501

        :param borrowed: The borrowed of this CrossMarginAccount.  # noqa: E501
        :type: str
        """

        self._borrowed = borrowed

    @property
    def interest(self):
        """Gets the interest of this CrossMarginAccount.  # noqa: E501

        Total unpaid interests in USDT, i.e., the sum of all currencies' `interest*price*discount`  # noqa: E501

        :return: The interest of this CrossMarginAccount.  # noqa: E501
        :rtype: str
        """
        return self._interest

    @interest.setter
    def interest(self, interest):
        """Sets the interest of this CrossMarginAccount.

        Total unpaid interests in USDT, i.e., the sum of all currencies' `interest*price*discount`  # noqa: E501

        :param interest: The interest of this CrossMarginAccount.  # noqa: E501
        :type: str
        """

        self._interest = interest

    @property
    def risk(self):
        """Gets the risk of this CrossMarginAccount.  # noqa: E501

        Risk rate. When it belows 110%, liquidation will be triggered. Calculation formula: `total / (borrowed+interest)`  # noqa: E501

        :return: The risk of this CrossMarginAccount.  # noqa: E501
        :rtype: str
        """
        return self._risk

    @risk.setter
    def risk(self, risk):
        """Sets the risk of this CrossMarginAccount.

        Risk rate. When it belows 110%, liquidation will be triggered. Calculation formula: `total / (borrowed+interest)`  # noqa: E501

        :param risk: The risk of this CrossMarginAccount.  # noqa: E501
        :type: str
        """

        self._risk = risk

    @property
    def total_initial_margin(self):
        """Gets the total_initial_margin of this CrossMarginAccount.  # noqa: E501

        Total initial margin  # noqa: E501

        :return: The total_initial_margin of this CrossMarginAccount.  # noqa: E501
        :rtype: str
        """
        return self._total_initial_margin

    @total_initial_margin.setter
    def total_initial_margin(self, total_initial_margin):
        """Sets the total_initial_margin of this CrossMarginAccount.

        Total initial margin  # noqa: E501

        :param total_initial_margin: The total_initial_margin of this CrossMarginAccount.  # noqa: E501
        :type: str
        """

        self._total_initial_margin = total_initial_margin

    @property
    def total_margin_balance(self):
        """Gets the total_margin_balance of this CrossMarginAccount.  # noqa: E501

        Total Margin Balance (∑(positive equity ＊ index price * discount) + ∑(negative equity * index price))  # noqa: E501

        :return: The total_margin_balance of this CrossMarginAccount.  # noqa: E501
        :rtype: str
        """
        return self._total_margin_balance

    @total_margin_balance.setter
    def total_margin_balance(self, total_margin_balance):
        """Sets the total_margin_balance of this CrossMarginAccount.

        Total Margin Balance (∑(positive equity ＊ index price * discount) + ∑(negative equity * index price))  # noqa: E501

        :param total_margin_balance: The total_margin_balance of this CrossMarginAccount.  # noqa: E501
        :type: str
        """

        self._total_margin_balance = total_margin_balance

    @property
    def total_maintenance_margin(self):
        """Gets the total_maintenance_margin of this CrossMarginAccount.  # noqa: E501

        Total maintenance margin  # noqa: E501

        :return: The total_maintenance_margin of this CrossMarginAccount.  # noqa: E501
        :rtype: str
        """
        return self._total_maintenance_margin

    @total_maintenance_margin.setter
    def total_maintenance_margin(self, total_maintenance_margin):
        """Sets the total_maintenance_margin of this CrossMarginAccount.

        Total maintenance margin  # noqa: E501

        :param total_maintenance_margin: The total_maintenance_margin of this CrossMarginAccount.  # noqa: E501
        :type: str
        """

        self._total_maintenance_margin = total_maintenance_margin

    @property
    def total_initial_margin_rate(self):
        """Gets the total_initial_margin_rate of this CrossMarginAccount.  # noqa: E501

        Total initial margin rate  # noqa: E501

        :return: The total_initial_margin_rate of this CrossMarginAccount.  # noqa: E501
        :rtype: str
        """
        return self._total_initial_margin_rate

    @total_initial_margin_rate.setter
    def total_initial_margin_rate(self, total_initial_margin_rate):
        """Sets the total_initial_margin_rate of this CrossMarginAccount.

        Total initial margin rate  # noqa: E501

        :param total_initial_margin_rate: The total_initial_margin_rate of this CrossMarginAccount.  # noqa: E501
        :type: str
        """

        self._total_initial_margin_rate = total_initial_margin_rate

    @property
    def total_maintenance_margin_rate(self):
        """Gets the total_maintenance_margin_rate of this CrossMarginAccount.  # noqa: E501

        Total maintenance margin rate  # noqa: E501

        :return: The total_maintenance_margin_rate of this CrossMarginAccount.  # noqa: E501
        :rtype: str
        """
        return self._total_maintenance_margin_rate

    @total_maintenance_margin_rate.setter
    def total_maintenance_margin_rate(self, total_maintenance_margin_rate):
        """Sets the total_maintenance_margin_rate of this CrossMarginAccount.

        Total maintenance margin rate  # noqa: E501

        :param total_maintenance_margin_rate: The total_maintenance_margin_rate of this CrossMarginAccount.  # noqa: E501
        :type: str
        """

        self._total_maintenance_margin_rate = total_maintenance_margin_rate

    @property
    def total_available_margin(self):
        """Gets the total_available_margin of this CrossMarginAccount.  # noqa: E501

        Total available margin  # noqa: E501

        :return: The total_available_margin of this CrossMarginAccount.  # noqa: E501
        :rtype: str
        """
        return self._total_available_margin

    @total_available_margin.setter
    def total_available_margin(self, total_available_margin):
        """Sets the total_available_margin of this CrossMarginAccount.

        Total available margin  # noqa: E501

        :param total_available_margin: The total_available_margin of this CrossMarginAccount.  # noqa: E501
        :type: str
        """

        self._total_available_margin = total_available_margin

    @property
    def portfolio_margin_total(self):
        """Gets the portfolio_margin_total of this CrossMarginAccount.  # noqa: E501

        Total amount of the portfolio margin account  # noqa: E501

        :return: The portfolio_margin_total of this CrossMarginAccount.  # noqa: E501
        :rtype: str
        """
        return self._portfolio_margin_total

    @portfolio_margin_total.setter
    def portfolio_margin_total(self, portfolio_margin_total):
        """Sets the portfolio_margin_total of this CrossMarginAccount.

        Total amount of the portfolio margin account  # noqa: E501

        :param portfolio_margin_total: The portfolio_margin_total of this CrossMarginAccount.  # noqa: E501
        :type: str
        """

        self._portfolio_margin_total = portfolio_margin_total

    @property
    def portfolio_margin_total_liab(self):
        """Gets the portfolio_margin_total_liab of this CrossMarginAccount.  # noqa: E501

        Total liabilities of the portfolio margin account  # noqa: E501

        :return: The portfolio_margin_total_liab of this CrossMarginAccount.  # noqa: E501
        :rtype: str
        """
        return self._portfolio_margin_total_liab

    @portfolio_margin_total_liab.setter
    def portfolio_margin_total_liab(self, portfolio_margin_total_liab):
        """Sets the portfolio_margin_total_liab of this CrossMarginAccount.

        Total liabilities of the portfolio margin account  # noqa: E501

        :param portfolio_margin_total_liab: The portfolio_margin_total_liab of this CrossMarginAccount.  # noqa: E501
        :type: str
        """

        self._portfolio_margin_total_liab = portfolio_margin_total_liab

    @property
    def portfolio_margin_total_equity(self):
        """Gets the portfolio_margin_total_equity of this CrossMarginAccount.  # noqa: E501

        Total equity of the portfolio margin account  # noqa: E501

        :return: The portfolio_margin_total_equity of this CrossMarginAccount.  # noqa: E501
        :rtype: str
        """
        return self._portfolio_margin_total_equity

    @portfolio_margin_total_equity.setter
    def portfolio_margin_total_equity(self, portfolio_margin_total_equity):
        """Sets the portfolio_margin_total_equity of this CrossMarginAccount.

        Total equity of the portfolio margin account  # noqa: E501

        :param portfolio_margin_total_equity: The portfolio_margin_total_equity of this CrossMarginAccount.  # noqa: E501
        :type: str
        """

        self._portfolio_margin_total_equity = portfolio_margin_total_equity

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.openapi_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CrossMarginAccount):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CrossMarginAccount):
            return True

        return self.to_dict() != other.to_dict()
