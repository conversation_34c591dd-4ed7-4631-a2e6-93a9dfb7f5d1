# coding: utf-8

"""
    Gate API v4

    Welcome to Gate.io API  APIv4 provides spot, margin and futures trading operations. There are public APIs to retrieve the real-time market statistics, and private APIs which needs authentication to trade on user's behalf.  # noqa: E501

    Contact: <EMAIL>
    Generated by: https://openapi-generator.tech
"""


import pprint
import re  # noqa: F401

import six

from gate_api.configuration import Configuration


class OptionsMMPReset(object):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        'underlying': 'str',
        'window': 'int',
        'frozen_period': 'int',
        'qty_limit': 'str',
        'delta_limit': 'str',
        'trigger_time_ms': 'int',
        'frozen_until_ms': 'int'
    }

    attribute_map = {
        'underlying': 'underlying',
        'window': 'window',
        'frozen_period': 'frozen_period',
        'qty_limit': 'qty_limit',
        'delta_limit': 'delta_limit',
        'trigger_time_ms': 'trigger_time_ms',
        'frozen_until_ms': 'frozen_until_ms'
    }

    def __init__(self, underlying=None, window=None, frozen_period=None, qty_limit=None, delta_limit=None, trigger_time_ms=None, frozen_until_ms=None, local_vars_configuration=None):  # noqa: E501
        # type: (str, int, int, str, str, int, int, Configuration) -> None
        """OptionsMMPReset - a model defined in OpenAPI"""  # noqa: E501
        if local_vars_configuration is None:
            local_vars_configuration = Configuration()
        self.local_vars_configuration = local_vars_configuration

        self._underlying = None
        self._window = None
        self._frozen_period = None
        self._qty_limit = None
        self._delta_limit = None
        self._trigger_time_ms = None
        self._frozen_until_ms = None
        self.discriminator = None

        self.underlying = underlying
        if window is not None:
            self.window = window
        if frozen_period is not None:
            self.frozen_period = frozen_period
        if qty_limit is not None:
            self.qty_limit = qty_limit
        if delta_limit is not None:
            self.delta_limit = delta_limit
        if trigger_time_ms is not None:
            self.trigger_time_ms = trigger_time_ms
        if frozen_until_ms is not None:
            self.frozen_until_ms = frozen_until_ms

    @property
    def underlying(self):
        """Gets the underlying of this OptionsMMPReset.  # noqa: E501

        Underlying  # noqa: E501

        :return: The underlying of this OptionsMMPReset.  # noqa: E501
        :rtype: str
        """
        return self._underlying

    @underlying.setter
    def underlying(self, underlying):
        """Sets the underlying of this OptionsMMPReset.

        Underlying  # noqa: E501

        :param underlying: The underlying of this OptionsMMPReset.  # noqa: E501
        :type: str
        """
        if self.local_vars_configuration.client_side_validation and underlying is None:  # noqa: E501
            raise ValueError("Invalid value for `underlying`, must not be `None`")  # noqa: E501

        self._underlying = underlying

    @property
    def window(self):
        """Gets the window of this OptionsMMPReset.  # noqa: E501

        Time window (milliseconds), between 1-5000, 0 means disabling MMP  # noqa: E501

        :return: The window of this OptionsMMPReset.  # noqa: E501
        :rtype: int
        """
        return self._window

    @window.setter
    def window(self, window):
        """Sets the window of this OptionsMMPReset.

        Time window (milliseconds), between 1-5000, 0 means disabling MMP  # noqa: E501

        :param window: The window of this OptionsMMPReset.  # noqa: E501
        :type: int
        """

        self._window = window

    @property
    def frozen_period(self):
        """Gets the frozen_period of this OptionsMMPReset.  # noqa: E501

        Freeze duration (milliseconds), 0 means always frozen, need to call reset API to unfreeze  # noqa: E501

        :return: The frozen_period of this OptionsMMPReset.  # noqa: E501
        :rtype: int
        """
        return self._frozen_period

    @frozen_period.setter
    def frozen_period(self, frozen_period):
        """Sets the frozen_period of this OptionsMMPReset.

        Freeze duration (milliseconds), 0 means always frozen, need to call reset API to unfreeze  # noqa: E501

        :param frozen_period: The frozen_period of this OptionsMMPReset.  # noqa: E501
        :type: int
        """

        self._frozen_period = frozen_period

    @property
    def qty_limit(self):
        """Gets the qty_limit of this OptionsMMPReset.  # noqa: E501

        Trading volume upper limit (positive number, up to 2 decimal places)  # noqa: E501

        :return: The qty_limit of this OptionsMMPReset.  # noqa: E501
        :rtype: str
        """
        return self._qty_limit

    @qty_limit.setter
    def qty_limit(self, qty_limit):
        """Sets the qty_limit of this OptionsMMPReset.

        Trading volume upper limit (positive number, up to 2 decimal places)  # noqa: E501

        :param qty_limit: The qty_limit of this OptionsMMPReset.  # noqa: E501
        :type: str
        """

        self._qty_limit = qty_limit

    @property
    def delta_limit(self):
        """Gets the delta_limit of this OptionsMMPReset.  # noqa: E501

        Upper limit of net delta value (positive number, up to 2 decimal places)  # noqa: E501

        :return: The delta_limit of this OptionsMMPReset.  # noqa: E501
        :rtype: str
        """
        return self._delta_limit

    @delta_limit.setter
    def delta_limit(self, delta_limit):
        """Sets the delta_limit of this OptionsMMPReset.

        Upper limit of net delta value (positive number, up to 2 decimal places)  # noqa: E501

        :param delta_limit: The delta_limit of this OptionsMMPReset.  # noqa: E501
        :type: str
        """

        self._delta_limit = delta_limit

    @property
    def trigger_time_ms(self):
        """Gets the trigger_time_ms of this OptionsMMPReset.  # noqa: E501

        Trigger freeze time (milliseconds), 0 means no freeze is triggered  # noqa: E501

        :return: The trigger_time_ms of this OptionsMMPReset.  # noqa: E501
        :rtype: int
        """
        return self._trigger_time_ms

    @trigger_time_ms.setter
    def trigger_time_ms(self, trigger_time_ms):
        """Sets the trigger_time_ms of this OptionsMMPReset.

        Trigger freeze time (milliseconds), 0 means no freeze is triggered  # noqa: E501

        :param trigger_time_ms: The trigger_time_ms of this OptionsMMPReset.  # noqa: E501
        :type: int
        """

        self._trigger_time_ms = trigger_time_ms

    @property
    def frozen_until_ms(self):
        """Gets the frozen_until_ms of this OptionsMMPReset.  # noqa: E501

        Unfreeze time (milliseconds). If the freeze duration is not configured, there will be no unfreeze time after the freeze is triggered.  # noqa: E501

        :return: The frozen_until_ms of this OptionsMMPReset.  # noqa: E501
        :rtype: int
        """
        return self._frozen_until_ms

    @frozen_until_ms.setter
    def frozen_until_ms(self, frozen_until_ms):
        """Sets the frozen_until_ms of this OptionsMMPReset.

        Unfreeze time (milliseconds). If the freeze duration is not configured, there will be no unfreeze time after the freeze is triggered.  # noqa: E501

        :param frozen_until_ms: The frozen_until_ms of this OptionsMMPReset.  # noqa: E501
        :type: int
        """

        self._frozen_until_ms = frozen_until_ms

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.openapi_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, OptionsMMPReset):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, OptionsMMPReset):
            return True

        return self.to_dict() != other.to_dict()
