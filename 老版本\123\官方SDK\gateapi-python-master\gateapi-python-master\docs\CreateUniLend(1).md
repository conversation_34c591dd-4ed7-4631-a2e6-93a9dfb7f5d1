# CreateUniLend

Lend or redeem
## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**currency** | **str** | Currency name | 
**amount** | **str** | The amount of currency could be lent | 
**type** | **str** | type: lend - lend, redeem - redeem | 
**min_rate** | **str** | The minimum interest rate. If the value is too high, it might lead to the unsuccessful lending and no profit will be gained for that hour.  | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


