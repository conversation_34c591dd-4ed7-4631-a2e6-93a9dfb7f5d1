# coding: utf-8

"""
    Gate API v4

    Welcome to Gate.io API  APIv4 provides spot, margin and futures trading operations. There are public APIs to retrieve the real-time market statistics, and private APIs which needs authentication to trade on user's behalf.  # noqa: E501

    Contact: <EMAIL>
    Generated by: https://openapi-generator.tech
"""


import pprint
import re  # noqa: F401

import six

from gate_api.configuration import Configuration


class MockFuturesOrder(object):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        'contract': 'str',
        'size': 'str',
        'left': 'str'
    }

    attribute_map = {
        'contract': 'contract',
        'size': 'size',
        'left': 'left'
    }

    def __init__(self, contract=None, size=None, left=None, local_vars_configuration=None):  # noqa: E501
        # type: (str, str, str, Configuration) -> None
        """MockFuturesOrder - a model defined in OpenAPI"""  # noqa: E501
        if local_vars_configuration is None:
            local_vars_configuration = Configuration()
        self.local_vars_configuration = local_vars_configuration

        self._contract = None
        self._size = None
        self._left = None
        self.discriminator = None

        self.contract = contract
        self.size = size
        self.left = left

    @property
    def contract(self):
        """Gets the contract of this MockFuturesOrder.  # noqa: E501

        Futures name, currently only supports perpetual futures for BTC and ETH with USDT.  # noqa: E501

        :return: The contract of this MockFuturesOrder.  # noqa: E501
        :rtype: str
        """
        return self._contract

    @contract.setter
    def contract(self, contract):
        """Sets the contract of this MockFuturesOrder.

        Futures name, currently only supports perpetual futures for BTC and ETH with USDT.  # noqa: E501

        :param contract: The contract of this MockFuturesOrder.  # noqa: E501
        :type: str
        """
        if self.local_vars_configuration.client_side_validation and contract is None:  # noqa: E501
            raise ValueError("Invalid value for `contract`, must not be `None`")  # noqa: E501

        self._contract = contract

    @property
    def size(self):
        """Gets the size of this MockFuturesOrder.  # noqa: E501

        Futures quantity, representing the initial order quantity, not involved in actual settlement.  # noqa: E501

        :return: The size of this MockFuturesOrder.  # noqa: E501
        :rtype: str
        """
        return self._size

    @size.setter
    def size(self, size):
        """Sets the size of this MockFuturesOrder.

        Futures quantity, representing the initial order quantity, not involved in actual settlement.  # noqa: E501

        :param size: The size of this MockFuturesOrder.  # noqa: E501
        :type: str
        """
        if self.local_vars_configuration.client_side_validation and size is None:  # noqa: E501
            raise ValueError("Invalid value for `size`, must not be `None`")  # noqa: E501

        self._size = size

    @property
    def left(self):
        """Gets the left of this MockFuturesOrder.  # noqa: E501

        Unfilled contract quantity, involved in actual calculation  # noqa: E501

        :return: The left of this MockFuturesOrder.  # noqa: E501
        :rtype: str
        """
        return self._left

    @left.setter
    def left(self, left):
        """Sets the left of this MockFuturesOrder.

        Unfilled contract quantity, involved in actual calculation  # noqa: E501

        :param left: The left of this MockFuturesOrder.  # noqa: E501
        :type: str
        """
        if self.local_vars_configuration.client_side_validation and left is None:  # noqa: E501
            raise ValueError("Invalid value for `left`, must not be `None`")  # noqa: E501

        self._left = left

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.openapi_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, MockFuturesOrder):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, MockFuturesOrder):
            return True

        return self.to_dict() != other.to_dict()
