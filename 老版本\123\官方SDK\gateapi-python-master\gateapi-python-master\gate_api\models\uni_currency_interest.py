# coding: utf-8

"""
    Gate API v4

    Welcome to Gate.io API  APIv4 provides spot, margin and futures trading operations. There are public APIs to retrieve the real-time market statistics, and private APIs which needs authentication to trade on user's behalf.  # noqa: E501

    Contact: <EMAIL>
    Generated by: https://openapi-generator.tech
"""


import pprint
import re  # noqa: F401

import six

from gate_api.configuration import Configuration


class UniCurrencyInterest(object):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        'currency': 'str',
        'interest_status': 'str'
    }

    attribute_map = {
        'currency': 'currency',
        'interest_status': 'interest_status'
    }

    def __init__(self, currency=None, interest_status=None, local_vars_configuration=None):  # noqa: E501
        # type: (str, str, Configuration) -> None
        """UniCurrencyInterest - a model defined in OpenAPI"""  # noqa: E501
        if local_vars_configuration is None:
            local_vars_configuration = Configuration()
        self.local_vars_configuration = local_vars_configuration

        self._currency = None
        self._interest_status = None
        self.discriminator = None

        if currency is not None:
            self.currency = currency
        if interest_status is not None:
            self.interest_status = interest_status

    @property
    def currency(self):
        """Gets the currency of this UniCurrencyInterest.  # noqa: E501

        Currency  # noqa: E501

        :return: The currency of this UniCurrencyInterest.  # noqa: E501
        :rtype: str
        """
        return self._currency

    @currency.setter
    def currency(self, currency):
        """Sets the currency of this UniCurrencyInterest.

        Currency  # noqa: E501

        :param currency: The currency of this UniCurrencyInterest.  # noqa: E501
        :type: str
        """

        self._currency = currency

    @property
    def interest_status(self):
        """Gets the interest_status of this UniCurrencyInterest.  # noqa: E501

        Interest status: interest_dividend - regular dividend, interest_reinvest - interest reinvestment  # noqa: E501

        :return: The interest_status of this UniCurrencyInterest.  # noqa: E501
        :rtype: str
        """
        return self._interest_status

    @interest_status.setter
    def interest_status(self, interest_status):
        """Sets the interest_status of this UniCurrencyInterest.

        Interest status: interest_dividend - regular dividend, interest_reinvest - interest reinvestment  # noqa: E501

        :param interest_status: The interest_status of this UniCurrencyInterest.  # noqa: E501
        :type: str
        """

        self._interest_status = interest_status

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.openapi_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, UniCurrencyInterest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, UniCurrencyInterest):
            return True

        return self.to_dict() != other.to_dict()
