# coding: utf-8

"""
    Gate API v4

    Welcome to Gate.io API  APIv4 provides spot, margin and futures trading operations. There are public APIs to retrieve the real-time market statistics, and private APIs which needs authentication to trade on user's behalf.  # noqa: E501

    Contact: <EMAIL>
    Generated by: https://openapi-generator.tech
"""


import pprint
import re  # noqa: F401

import six

from gate_api.configuration import Configuration


class CrossMarginLoan(object):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        'id': 'str',
        'create_time': 'int',
        'update_time': 'int',
        'currency': 'str',
        'amount': 'str',
        'text': 'str',
        'status': 'int',
        'repaid': 'str',
        'repaid_interest': 'str',
        'unpaid_interest': 'str'
    }

    attribute_map = {
        'id': 'id',
        'create_time': 'create_time',
        'update_time': 'update_time',
        'currency': 'currency',
        'amount': 'amount',
        'text': 'text',
        'status': 'status',
        'repaid': 'repaid',
        'repaid_interest': 'repaid_interest',
        'unpaid_interest': 'unpaid_interest'
    }

    def __init__(self, id=None, create_time=None, update_time=None, currency=None, amount=None, text=None, status=None, repaid=None, repaid_interest=None, unpaid_interest=None, local_vars_configuration=None):  # noqa: E501
        # type: (str, int, int, str, str, str, int, str, str, str, Configuration) -> None
        """CrossMarginLoan - a model defined in OpenAPI"""  # noqa: E501
        if local_vars_configuration is None:
            local_vars_configuration = Configuration()
        self.local_vars_configuration = local_vars_configuration

        self._id = None
        self._create_time = None
        self._update_time = None
        self._currency = None
        self._amount = None
        self._text = None
        self._status = None
        self._repaid = None
        self._repaid_interest = None
        self._unpaid_interest = None
        self.discriminator = None

        if id is not None:
            self.id = id
        if create_time is not None:
            self.create_time = create_time
        if update_time is not None:
            self.update_time = update_time
        self.currency = currency
        self.amount = amount
        if text is not None:
            self.text = text
        if status is not None:
            self.status = status
        if repaid is not None:
            self.repaid = repaid
        if repaid_interest is not None:
            self.repaid_interest = repaid_interest
        if unpaid_interest is not None:
            self.unpaid_interest = unpaid_interest

    @property
    def id(self):
        """Gets the id of this CrossMarginLoan.  # noqa: E501

        Loan record ID  # noqa: E501

        :return: The id of this CrossMarginLoan.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this CrossMarginLoan.

        Loan record ID  # noqa: E501

        :param id: The id of this CrossMarginLoan.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def create_time(self):
        """Gets the create_time of this CrossMarginLoan.  # noqa: E501

        Creation timestamp, in milliseconds  # noqa: E501

        :return: The create_time of this CrossMarginLoan.  # noqa: E501
        :rtype: int
        """
        return self._create_time

    @create_time.setter
    def create_time(self, create_time):
        """Sets the create_time of this CrossMarginLoan.

        Creation timestamp, in milliseconds  # noqa: E501

        :param create_time: The create_time of this CrossMarginLoan.  # noqa: E501
        :type: int
        """

        self._create_time = create_time

    @property
    def update_time(self):
        """Gets the update_time of this CrossMarginLoan.  # noqa: E501

        Update timestamp, in milliseconds  # noqa: E501

        :return: The update_time of this CrossMarginLoan.  # noqa: E501
        :rtype: int
        """
        return self._update_time

    @update_time.setter
    def update_time(self, update_time):
        """Sets the update_time of this CrossMarginLoan.

        Update timestamp, in milliseconds  # noqa: E501

        :param update_time: The update_time of this CrossMarginLoan.  # noqa: E501
        :type: int
        """

        self._update_time = update_time

    @property
    def currency(self):
        """Gets the currency of this CrossMarginLoan.  # noqa: E501

        Currency name  # noqa: E501

        :return: The currency of this CrossMarginLoan.  # noqa: E501
        :rtype: str
        """
        return self._currency

    @currency.setter
    def currency(self, currency):
        """Sets the currency of this CrossMarginLoan.

        Currency name  # noqa: E501

        :param currency: The currency of this CrossMarginLoan.  # noqa: E501
        :type: str
        """
        if self.local_vars_configuration.client_side_validation and currency is None:  # noqa: E501
            raise ValueError("Invalid value for `currency`, must not be `None`")  # noqa: E501

        self._currency = currency

    @property
    def amount(self):
        """Gets the amount of this CrossMarginLoan.  # noqa: E501

        Borrowed amount  # noqa: E501

        :return: The amount of this CrossMarginLoan.  # noqa: E501
        :rtype: str
        """
        return self._amount

    @amount.setter
    def amount(self, amount):
        """Sets the amount of this CrossMarginLoan.

        Borrowed amount  # noqa: E501

        :param amount: The amount of this CrossMarginLoan.  # noqa: E501
        :type: str
        """
        if self.local_vars_configuration.client_side_validation and amount is None:  # noqa: E501
            raise ValueError("Invalid value for `amount`, must not be `None`")  # noqa: E501

        self._amount = amount

    @property
    def text(self):
        """Gets the text of this CrossMarginLoan.  # noqa: E501

        User defined custom ID  # noqa: E501

        :return: The text of this CrossMarginLoan.  # noqa: E501
        :rtype: str
        """
        return self._text

    @text.setter
    def text(self, text):
        """Sets the text of this CrossMarginLoan.

        User defined custom ID  # noqa: E501

        :param text: The text of this CrossMarginLoan.  # noqa: E501
        :type: str
        """

        self._text = text

    @property
    def status(self):
        """Gets the status of this CrossMarginLoan.  # noqa: E501

        Deprecated. Currently, all statuses have been set to 2.  Borrow loan status, which includes:  - 1: failed to borrow - 2: borrowed but not repaid - 3: repayment complete  # noqa: E501

        :return: The status of this CrossMarginLoan.  # noqa: E501
        :rtype: int
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this CrossMarginLoan.

        Deprecated. Currently, all statuses have been set to 2.  Borrow loan status, which includes:  - 1: failed to borrow - 2: borrowed but not repaid - 3: repayment complete  # noqa: E501

        :param status: The status of this CrossMarginLoan.  # noqa: E501
        :type: int
        """
        allowed_values = [1, 2, 3]  # noqa: E501
        if self.local_vars_configuration.client_side_validation and status not in allowed_values:  # noqa: E501
            raise ValueError(
                "Invalid value for `status` ({0}), must be one of {1}"  # noqa: E501
                .format(status, allowed_values)
            )

        self._status = status

    @property
    def repaid(self):
        """Gets the repaid of this CrossMarginLoan.  # noqa: E501

        Repaid amount  # noqa: E501

        :return: The repaid of this CrossMarginLoan.  # noqa: E501
        :rtype: str
        """
        return self._repaid

    @repaid.setter
    def repaid(self, repaid):
        """Sets the repaid of this CrossMarginLoan.

        Repaid amount  # noqa: E501

        :param repaid: The repaid of this CrossMarginLoan.  # noqa: E501
        :type: str
        """

        self._repaid = repaid

    @property
    def repaid_interest(self):
        """Gets the repaid_interest of this CrossMarginLoan.  # noqa: E501

        Repaid interest  # noqa: E501

        :return: The repaid_interest of this CrossMarginLoan.  # noqa: E501
        :rtype: str
        """
        return self._repaid_interest

    @repaid_interest.setter
    def repaid_interest(self, repaid_interest):
        """Sets the repaid_interest of this CrossMarginLoan.

        Repaid interest  # noqa: E501

        :param repaid_interest: The repaid_interest of this CrossMarginLoan.  # noqa: E501
        :type: str
        """

        self._repaid_interest = repaid_interest

    @property
    def unpaid_interest(self):
        """Gets the unpaid_interest of this CrossMarginLoan.  # noqa: E501

        Outstanding interest yet to be paid  # noqa: E501

        :return: The unpaid_interest of this CrossMarginLoan.  # noqa: E501
        :rtype: str
        """
        return self._unpaid_interest

    @unpaid_interest.setter
    def unpaid_interest(self, unpaid_interest):
        """Sets the unpaid_interest of this CrossMarginLoan.

        Outstanding interest yet to be paid  # noqa: E501

        :param unpaid_interest: The unpaid_interest of this CrossMarginLoan.  # noqa: E501
        :type: str
        """

        self._unpaid_interest = unpaid_interest

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.openapi_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CrossMarginLoan):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CrossMarginLoan):
            return True

        return self.to_dict() != other.to_dict()
