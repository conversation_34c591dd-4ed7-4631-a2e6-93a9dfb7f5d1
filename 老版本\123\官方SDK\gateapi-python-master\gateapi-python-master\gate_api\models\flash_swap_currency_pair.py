# coding: utf-8

"""
    Gate API v4

    Welcome to Gate.io API  APIv4 provides spot, margin and futures trading operations. There are public APIs to retrieve the real-time market statistics, and private APIs which needs authentication to trade on user's behalf.  # noqa: E501

    Contact: <EMAIL>
    Generated by: https://openapi-generator.tech
"""


import pprint
import re  # noqa: F401

import six

from gate_api.configuration import Configuration


class FlashSwapCurrencyPair(object):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        'currency_pair': 'str',
        'sell_currency': 'str',
        'buy_currency': 'str',
        'sell_min_amount': 'str',
        'sell_max_amount': 'str',
        'buy_min_amount': 'str',
        'buy_max_amount': 'str'
    }

    attribute_map = {
        'currency_pair': 'currency_pair',
        'sell_currency': 'sell_currency',
        'buy_currency': 'buy_currency',
        'sell_min_amount': 'sell_min_amount',
        'sell_max_amount': 'sell_max_amount',
        'buy_min_amount': 'buy_min_amount',
        'buy_max_amount': 'buy_max_amount'
    }

    def __init__(self, currency_pair=None, sell_currency=None, buy_currency=None, sell_min_amount=None, sell_max_amount=None, buy_min_amount=None, buy_max_amount=None, local_vars_configuration=None):  # noqa: E501
        # type: (str, str, str, str, str, str, str, Configuration) -> None
        """FlashSwapCurrencyPair - a model defined in OpenAPI"""  # noqa: E501
        if local_vars_configuration is None:
            local_vars_configuration = Configuration()
        self.local_vars_configuration = local_vars_configuration

        self._currency_pair = None
        self._sell_currency = None
        self._buy_currency = None
        self._sell_min_amount = None
        self._sell_max_amount = None
        self._buy_min_amount = None
        self._buy_max_amount = None
        self.discriminator = None

        if currency_pair is not None:
            self.currency_pair = currency_pair
        if sell_currency is not None:
            self.sell_currency = sell_currency
        if buy_currency is not None:
            self.buy_currency = buy_currency
        if sell_min_amount is not None:
            self.sell_min_amount = sell_min_amount
        if sell_max_amount is not None:
            self.sell_max_amount = sell_max_amount
        if buy_min_amount is not None:
            self.buy_min_amount = buy_min_amount
        if buy_max_amount is not None:
            self.buy_max_amount = buy_max_amount

    @property
    def currency_pair(self):
        """Gets the currency_pair of this FlashSwapCurrencyPair.  # noqa: E501

        The currency pair, BTC_USDT represents selling Bitcoin (BTC) and buying Tether (USDT).  # noqa: E501

        :return: The currency_pair of this FlashSwapCurrencyPair.  # noqa: E501
        :rtype: str
        """
        return self._currency_pair

    @currency_pair.setter
    def currency_pair(self, currency_pair):
        """Sets the currency_pair of this FlashSwapCurrencyPair.

        The currency pair, BTC_USDT represents selling Bitcoin (BTC) and buying Tether (USDT).  # noqa: E501

        :param currency_pair: The currency_pair of this FlashSwapCurrencyPair.  # noqa: E501
        :type: str
        """

        self._currency_pair = currency_pair

    @property
    def sell_currency(self):
        """Gets the sell_currency of this FlashSwapCurrencyPair.  # noqa: E501

        The currency to be sold  # noqa: E501

        :return: The sell_currency of this FlashSwapCurrencyPair.  # noqa: E501
        :rtype: str
        """
        return self._sell_currency

    @sell_currency.setter
    def sell_currency(self, sell_currency):
        """Sets the sell_currency of this FlashSwapCurrencyPair.

        The currency to be sold  # noqa: E501

        :param sell_currency: The sell_currency of this FlashSwapCurrencyPair.  # noqa: E501
        :type: str
        """

        self._sell_currency = sell_currency

    @property
    def buy_currency(self):
        """Gets the buy_currency of this FlashSwapCurrencyPair.  # noqa: E501

        The currency to be bought  # noqa: E501

        :return: The buy_currency of this FlashSwapCurrencyPair.  # noqa: E501
        :rtype: str
        """
        return self._buy_currency

    @buy_currency.setter
    def buy_currency(self, buy_currency):
        """Sets the buy_currency of this FlashSwapCurrencyPair.

        The currency to be bought  # noqa: E501

        :param buy_currency: The buy_currency of this FlashSwapCurrencyPair.  # noqa: E501
        :type: str
        """

        self._buy_currency = buy_currency

    @property
    def sell_min_amount(self):
        """Gets the sell_min_amount of this FlashSwapCurrencyPair.  # noqa: E501

        The minimum quantity required for selling  # noqa: E501

        :return: The sell_min_amount of this FlashSwapCurrencyPair.  # noqa: E501
        :rtype: str
        """
        return self._sell_min_amount

    @sell_min_amount.setter
    def sell_min_amount(self, sell_min_amount):
        """Sets the sell_min_amount of this FlashSwapCurrencyPair.

        The minimum quantity required for selling  # noqa: E501

        :param sell_min_amount: The sell_min_amount of this FlashSwapCurrencyPair.  # noqa: E501
        :type: str
        """

        self._sell_min_amount = sell_min_amount

    @property
    def sell_max_amount(self):
        """Gets the sell_max_amount of this FlashSwapCurrencyPair.  # noqa: E501

        The maximum quantity allowed for selling  # noqa: E501

        :return: The sell_max_amount of this FlashSwapCurrencyPair.  # noqa: E501
        :rtype: str
        """
        return self._sell_max_amount

    @sell_max_amount.setter
    def sell_max_amount(self, sell_max_amount):
        """Sets the sell_max_amount of this FlashSwapCurrencyPair.

        The maximum quantity allowed for selling  # noqa: E501

        :param sell_max_amount: The sell_max_amount of this FlashSwapCurrencyPair.  # noqa: E501
        :type: str
        """

        self._sell_max_amount = sell_max_amount

    @property
    def buy_min_amount(self):
        """Gets the buy_min_amount of this FlashSwapCurrencyPair.  # noqa: E501

        The minimum quantity required for buying  # noqa: E501

        :return: The buy_min_amount of this FlashSwapCurrencyPair.  # noqa: E501
        :rtype: str
        """
        return self._buy_min_amount

    @buy_min_amount.setter
    def buy_min_amount(self, buy_min_amount):
        """Sets the buy_min_amount of this FlashSwapCurrencyPair.

        The minimum quantity required for buying  # noqa: E501

        :param buy_min_amount: The buy_min_amount of this FlashSwapCurrencyPair.  # noqa: E501
        :type: str
        """

        self._buy_min_amount = buy_min_amount

    @property
    def buy_max_amount(self):
        """Gets the buy_max_amount of this FlashSwapCurrencyPair.  # noqa: E501

        The maximum quantity allowed for buying  # noqa: E501

        :return: The buy_max_amount of this FlashSwapCurrencyPair.  # noqa: E501
        :rtype: str
        """
        return self._buy_max_amount

    @buy_max_amount.setter
    def buy_max_amount(self, buy_max_amount):
        """Sets the buy_max_amount of this FlashSwapCurrencyPair.

        The maximum quantity allowed for buying  # noqa: E501

        :param buy_max_amount: The buy_max_amount of this FlashSwapCurrencyPair.  # noqa: E501
        :type: str
        """

        self._buy_max_amount = buy_max_amount

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.openapi_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, FlashSwapCurrencyPair):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, FlashSwapCurrencyPair):
            return True

        return self.to_dict() != other.to_dict()
