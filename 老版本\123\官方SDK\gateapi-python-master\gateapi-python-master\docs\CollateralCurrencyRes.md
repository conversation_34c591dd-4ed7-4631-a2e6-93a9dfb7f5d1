# CollateralCurrencyRes

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**succeeded** | **bool** | Update success status | [optional] 
**label** | **str** | Error identifier for unsuccessful operations; empty for successful. | [optional] 
**message** | **str** | Error description in case of operation failure; empty when successful. | [optional] 
**currency** | **str** | Currency | [optional] 
**amount** | **str** | Quantity of successful collateral operation; 0 if the operation fails. | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


