"""
系统配置管理模块
从.env文件加载所有配置参数
"""
import os
from typing import Dict, Any, List
# 🔥 删除重复导入：load_dotenv由main.py统一处理
from dataclasses import dataclass
from decimal import Decimal
import logging

# 🔥 网络配置管理器集成
from config.network_config import init_network_config, get_network_config_manager

# 🔥 删除重复的load_dotenv()调用 - 由main.py统一加载
# 环境变量已由main.py加载，此处不再重复加载


@dataclass
class ExchangeConfig:
    """交易所配置"""
    api_key: str
    api_secret: str
    api_passphrase: str = ""
    total_balance: Decimal = Decimal("0")
    initial_spot_percent: float = 50.0
    initial_futures_percent: float = 50.0
    is_unified_account: bool = False


@dataclass
class TradingConfig:
    """交易参数配置"""
    min_spread: float
    max_spread: float
    close_spread_min: float
    # 🔥 删除：close_spread_max - 不再使用区间判断
    spot_slippage: float
    futures_market_slippage: float  # 🔥 纯市价单策略
    max_retry_times: int


@dataclass
class SystemConfig:
    """系统参数配置"""
    balance_check_interval: int
    balance_threshold_percent: float
    ws_heartbeat_interval: int
    ws_reconnect_max_attempts: int
    ws_connect_timeout: int
    ws_reconnect_timeout: int
    ws_stats_interval: int
    ws_reconnect_delay: float
    fund_transfer_timeout: int
    debug_mode: bool
    log_level: str
    allow_low_balance_test: bool = False
    min_required_balance: float = 200.0


@dataclass
class RiskConfig:
    """风控参数配置"""
    min_position_diff: float
    min_amount_diff: float


class ArbitrageConfig:
    """期现套利系统配置"""
    
    # 🔥 删除重复的TARGET_SYMBOLS配置读取：使用通用代币系统
    # TARGET_SYMBOLS由通用代币系统统一管理，避免重复读取
    DEFAULT_TEST_SYMBOL = os.getenv('DEFAULT_TEST_SYMBOL', 'ADA-USDT')

    @classmethod
    def get_target_symbols(cls):
        """获取目标交易对 - 直接从环境变量读取，避免循环依赖"""
        try:
            target_symbols_str = os.getenv('TARGET_SYMBOLS', 'ADA-USDT,DOGE-USDT,MATIC-USDT')
            return [symbol.strip() for symbol in target_symbols_str.split(",") if symbol.strip()]
        except:
            return ['ADA-USDT', 'DOGE-USDT', 'MATIC-USDT']  # 应急默认值

    # 价差阈值配置
    MIN_SPREAD = float(os.getenv('MIN_SPREAD', '0.001'))  # 🔥 修复：与.env保持一致(0.1%)
    MAX_SPREAD = float(os.getenv('MAX_SPREAD', '0.10'))   # 套利最大价差阈值(10%)
    CLOSE_SPREAD_MIN = float(os.getenv('CLOSE_SPREAD_MIN', '-0.003'))  # 🔥 修复：现货溢价0.3%阈值
    # 🔥 删除：CLOSE_SPREAD_MAX - 不再使用区间判断，使用单一阈值

    # 订单金额限制 - 🔥 修复：使用正确的默认值，确保与.env配置一致
    MIN_ORDER_AMOUNT_USD = float(os.getenv('MIN_ORDER_AMOUNT_USD', '50.0'))  # 修复：与.env保持一致50.0
    MAX_ORDER_AMOUNT_USD = float(os.getenv('MAX_ORDER_AMOUNT_USD', '110.0'))  # 修复：与.env保持一致
    SINGLE_TRADE_LIMIT_USD = float(os.getenv('SINGLE_TRADE_LIMIT_USD', '250.0'))  # 修复：与.env保持一致
    DAILY_TRADE_LIMIT_USD = float(os.getenv('DAILY_TRADE_LIMIT_USD', '2000.0'))

    # 交易所资金配置
    GATE_TOTAL_BALANCE = float(os.getenv('GATE_TOTAL_BALANCE', '100'))
    BYBIT_TOTAL_BALANCE = float(os.getenv('BYBIT_TOTAL_BALANCE', '100'))
    OKX_TOTAL_BALANCE = float(os.getenv('OKX_TOTAL_BALANCE', '100'))

    # 资金平衡配置
    GATE_INITIAL_SPOT_PERCENT = int(os.getenv('GATE_INITIAL_SPOT_PERCENT', '50'))
    GATE_INITIAL_FUTURES_PERCENT = int(os.getenv('GATE_INITIAL_FUTURES_PERCENT', '50'))
    BALANCE_CHECK_INTERVAL = int(os.getenv('BALANCE_CHECK_INTERVAL', '3600'))
    BALANCE_THRESHOLD_PERCENT = int(os.getenv('BALANCE_THRESHOLD_PERCENT', '10'))

    # 测试配置
    TEST_PLACE_ORDER = os.getenv('TEST_PLACE_ORDER', 'true').lower() == 'true'

    @classmethod
    def validate_config(cls) -> bool:
        """验证配置的有效性"""
        # 🔥 修复：验证价差配置逻辑 - 使用单一阈值
        # 检查零值配置
        if cls.CLOSE_SPREAD_MIN == 0:
            raise ValueError(f"平仓价差不能为零：CLOSE_SPREAD_MIN={cls.CLOSE_SPREAD_MIN}")

        # 🔥 简化验证：只验证CLOSE_SPREAD_MIN，不再使用区间判断
        if cls.CLOSE_SPREAD_MIN < 0:
            # 负值配置：现货溢价阈值
            # CLOSE_SPREAD_MIN=-0.001 表示现货溢价0.1%阈值
            if cls.CLOSE_SPREAD_MIN < -0.1:  # 限制最大现货溢价阈值为10%
                raise ValueError(f"现货溢价阈值过大：{abs(cls.CLOSE_SPREAD_MIN)*100:.1f}% > 10%")
        else:
            # 正值配置：期货溢价阈值
            if cls.CLOSE_SPREAD_MIN > 0.1:  # 限制最大期货溢价阈值为10%
                raise ValueError(f"期货溢价阈值过大：{cls.CLOSE_SPREAD_MIN*100:.1f}% > 10%")
            # 正值配置下，平仓阈值不应大于开仓最小价差
            if cls.MIN_SPREAD <= cls.CLOSE_SPREAD_MIN:
                raise ValueError(f"开仓最小价差({cls.MIN_SPREAD})必须大于平仓阈值({cls.CLOSE_SPREAD_MIN})")

        # 验证币种配置 - 使用通用代币系统
        try:
            target_symbols = cls.get_target_symbols()
            if not target_symbols or len(target_symbols) == 0:
                raise ValueError("必须配置至少一个目标交易对")
        except:
            raise ValueError("无法获取目标交易对配置，请检查.env中的TARGET_SYMBOLS")

        return True

    @classmethod
    def get_arbitrage_combinations(cls) -> List[Dict]:
        """获取套利组合配置"""
        return [
            {"name": "A", "spot_exchange": "gate", "futures_exchange": "bybit"},
            {"name": "B", "spot_exchange": "bybit", "futures_exchange": "gate"},
            {"name": "C", "spot_exchange": "okx", "futures_exchange": "bybit"},
            {"name": "D", "spot_exchange": "bybit", "futures_exchange": "okx"},
            {"name": "G", "spot_exchange": "okx", "futures_exchange": "gate"},
            {"name": "H", "spot_exchange": "gate", "futures_exchange": "okx"},
        ]

# 🔥 删除循环依赖：直接设置兼容性属性
# ArbitrageConfig.TARGET_SYMBOLS = ArbitrageConfig.get_target_symbols()  # 删除，避免linter错误


class Settings:
    """全局配置管理器"""
    
    def __init__(self):
        # 🔥 初始化网络配置管理器
        self.network_config_manager = init_network_config()
        
        # 🔥 删除重复的TARGET_SYMBOLS配置：使用通用代币系统统一管理
        # self.TARGET_SYMBOLS 由通用代币系统提供，避免重复配置
        
        # 验证配置有效性
        ArbitrageConfig.validate_config()
        
        # 加载其他配置
        self._load_configs()
        self._validate_configs()
    
    def _load_configs(self):
        """加载所有配置"""
        # 基础配置
        # TEST_MODE已删除 - 直接执行
        self.DEBUG_MODE = os.getenv('DEBUG_MODE', 'false').lower() == 'true'
        self.LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')
        
        # 🔥 删除重复配置读取：使用类级别的配置常量
        self.MAX_ORDER_AMOUNT_USD = ArbitrageConfig.MAX_ORDER_AMOUNT_USD
        self.MIN_ORDER_AMOUNT_USD = ArbitrageConfig.MIN_ORDER_AMOUNT_USD
        self.SINGLE_TRADE_LIMIT_USD = ArbitrageConfig.SINGLE_TRADE_LIMIT_USD
        self.DAILY_TRADE_LIMIT_USD = ArbitrageConfig.DAILY_TRADE_LIMIT_USD
        
        # 资金配置 - 🔥 修复：默认值与ArbitrageConfig保持一致
        self.GATE_TOTAL_BALANCE = Decimal(os.getenv('GATE_TOTAL_BALANCE', '100'))  # 与ArbitrageConfig一致
        self.BYBIT_TOTAL_BALANCE = Decimal(os.getenv('BYBIT_TOTAL_BALANCE', '100'))  # 与ArbitrageConfig一致
        self.OKX_TOTAL_BALANCE = Decimal(os.getenv('OKX_TOTAL_BALANCE', '100'))  # 与ArbitrageConfig一致
        
        # 套利参数
        self.MIN_SPREAD = float(os.getenv('MIN_SPREAD', '0.001'))  # 🔥 修复：与.env保持一致(0.1%)
        self.MAX_SPREAD = float(os.getenv('MAX_SPREAD', '0.10'))
        
        # 套利组合配置
        self.arbitrage_combinations = ArbitrageConfig.get_arbitrage_combinations()
        
        # 交易所配置
        self.exchanges = {
            'gate': ExchangeConfig(
                api_key=os.getenv('GATE_API_KEY', ''),
                api_secret=os.getenv('GATE_API_SECRET', ''),
                total_balance=self.GATE_TOTAL_BALANCE,
                initial_spot_percent=float(os.getenv('GATE_INITIAL_SPOT_PERCENT', '50')),
                initial_futures_percent=float(os.getenv('GATE_INITIAL_FUTURES_PERCENT', '50')),
                is_unified_account=False
            ),
            'bybit': ExchangeConfig(
                api_key=os.getenv('BYBIT_API_KEY', ''),
                api_secret=os.getenv('BYBIT_API_SECRET', ''),
                total_balance=self.BYBIT_TOTAL_BALANCE,
                is_unified_account=True
            ),
            'okx': ExchangeConfig(
                api_key=os.getenv('OKX_API_KEY', ''),
                api_secret=os.getenv('OKX_API_SECRET', ''),
                api_passphrase=os.getenv('OKX_API_PASSPHRASE', ''),
                total_balance=self.OKX_TOTAL_BALANCE,
                is_unified_account=True
            )
        }
        
        # 交易参数
        self.trading = TradingConfig(
            min_spread=self.MIN_SPREAD,
            max_spread=self.MAX_SPREAD,
            close_spread_min=float(os.getenv('CLOSE_SPREAD_MIN', '-0.003')),  # 🔥 修复：现货溢价0.3%阈值
            # 🔥 删除：close_spread_max - 不再使用区间判断
            spot_slippage=float(os.getenv('SPOT_MARKET_SLIPPAGE', '0.0003')),
            futures_market_slippage=float(os.getenv('FUTURES_MARKET_SLIPPAGE', '0.0002')),  # 🔥 纯市价单策略
            max_retry_times=int(os.getenv('MAX_RETRY_TIMES', '3'))
        )
        
        # 系统参数
        self.system = SystemConfig(
            balance_check_interval=int(os.getenv('BALANCE_CHECK_INTERVAL', '3600')),
            balance_threshold_percent=float(os.getenv('BALANCE_THRESHOLD_PERCENT', '10')),
            ws_heartbeat_interval=int(os.getenv('WS_HEARTBEAT_INTERVAL', '20')),
            ws_reconnect_max_attempts=int(os.getenv('WS_RECONNECT_MAX_ATTEMPTS', '10')),
            ws_connect_timeout=int(os.getenv('WS_CONNECT_TIMEOUT', '1000')),
            ws_reconnect_timeout=int(os.getenv('WS_RECONNECT_TIMEOUT', '3000')),
            ws_stats_interval=int(os.getenv('WS_STATS_INTERVAL', '60')),
            ws_reconnect_delay=float(os.getenv('WS_RECONNECT_DELAY', '2.0')),
            fund_transfer_timeout=int(os.getenv('FUND_TRANSFER_TIMEOUT', '10')),
            debug_mode=self.DEBUG_MODE,
            log_level=self.LOG_LEVEL,
            allow_low_balance_test=os.getenv('ALLOW_LOW_BALANCE_TEST', 'false').lower() == 'true',
            min_required_balance=float(os.getenv('MIN_REQUIRED_BALANCE', '200.0'))
        )
        
        # 风控参数
        self.risk = RiskConfig(
            min_position_diff=float(os.getenv('MIN_POSITION_DIFF', '0.00001')),
            min_amount_diff=float(os.getenv('MIN_AMOUNT_DIFF', '0.01'))
        )
        
        # 通知配置
        self.notification_webhook = os.getenv('NOTIFICATION_WEBHOOK', '')
        self.enable_notifications = os.getenv('ENABLE_NOTIFICATIONS', 'true').lower() == 'true'
        
        # 🔥 删除重复的交易对格式生成：使用货币适配器统一处理
        # 交易对格式转换由exchanges/currency_adapter.py统一处理，避免重复逻辑
        self.EXCHANGE_SYMBOLS = {}

        # 🔥 使用通用代币系统获取交易对
        try:
            from core.universal_token_system import get_universal_token_system
            token_system = get_universal_token_system()
            target_symbols = token_system.get_supported_symbols()
        except:
            target_symbols = []

        # Gate.io格式：BTC-USDT -> BTC_USDT
        self.EXCHANGE_SYMBOLS["gate"] = []
        for symbol in target_symbols:
            if '-' in symbol:
                gate_symbol = symbol.replace('-', '_')
            elif '_' in symbol:
                gate_symbol = symbol
            else:
                # BTCUSDT -> BTC_USDT
                if symbol.endswith('USDT') and len(symbol) > 4:
                    base = symbol[:-4]
                    gate_symbol = f"{base}_USDT"
                else:
                    gate_symbol = symbol
            self.EXCHANGE_SYMBOLS["gate"].append(gate_symbol)
        
        # Bybit格式：BTC-USDT -> BTCUSDT
        self.EXCHANGE_SYMBOLS["bybit"] = []
        for symbol in target_symbols:
            if '-' in symbol:
                bybit_symbol = symbol.replace('-', '')
            elif '_' in symbol:
                bybit_symbol = symbol.replace('_', '')
            else:
                bybit_symbol = symbol
            self.EXCHANGE_SYMBOLS["bybit"].append(bybit_symbol)
        
        # OKX格式：保持BTC-USDT
        self.EXCHANGE_SYMBOLS["okx"] = []
        for symbol in target_symbols:
            if '_' in symbol:
                okx_symbol = symbol.replace('_', '-')
            elif '-' in symbol:
                okx_symbol = symbol
            else:
                # BTCUSDT -> BTC-USDT
                if symbol.endswith('USDT') and len(symbol) > 4:
                    base = symbol[:-4]
                    okx_symbol = f"{base}-USDT"
                else:
                    okx_symbol = symbol
            self.EXCHANGE_SYMBOLS["okx"].append(okx_symbol)
        
        # 兼容性：保留SYMBOLS属性指向Gate格式
        self.SYMBOLS = self.EXCHANGE_SYMBOLS["gate"]
        
        # 🔥 所有配置已在此方法中加载完成
        # 注释：移除不存在的方法调用，避免AttributeError
    
    def _validate_configs(self):
        """验证配置有效性"""
        # 验证API密钥 - 低余额测试模式下允许跳过部分验证
        if not self.system.allow_low_balance_test:
            for name, exchange in self.exchanges.items():
                if not exchange.api_key or not exchange.api_secret:
                    logging.error(f"{name.upper()} API密钥未配置: api_key={exchange.api_key}, api_secret={'***' if exchange.api_secret else ''}")
                    raise ValueError(f"{name.upper()} API密钥未配置")
                if name == 'okx' and not exchange.api_passphrase:
                    logging.error("OKX API密码未配置")
                    raise ValueError("OKX API密码未配置")
        
        # 验证资金配置
        for name, exchange in self.exchanges.items():
            if exchange.total_balance < 0:
                logging.error(f"{name.upper()} 总资金不能为负数，当前值: {exchange.total_balance}")
                raise ValueError(f"{name.upper()} 总资金不能为负数")
                
            # 低余额测试模式下，允许余额为0
            if exchange.total_balance == 0 and not self.system.allow_low_balance_test:
                logging.error(f"{name.upper()} 总资金必须大于0，当前值: {exchange.total_balance}")
                raise ValueError(f"{name.upper()} 总资金必须大于0")
                
            if not exchange.is_unified_account:
                if exchange.initial_spot_percent + exchange.initial_futures_percent != 100:
                    logging.error(f"{name.upper()} 现货和期货初始比例之和必须为100%，当前: {exchange.initial_spot_percent}+{exchange.initial_futures_percent}")
                    raise ValueError(f"{name.upper()} 现货和期货初始比例之和必须为100%")
        
        # 🔥 修复：验证交易参数 - 支持现货溢价区间
        if self.trading.min_spread >= self.trading.max_spread:
            logging.error(f"最小价差必须小于最大价差，当前: min_spread={self.trading.min_spread}, max_spread={self.trading.max_spread}")
            raise ValueError("最小价差必须小于最大价差")

        # 🔥 修复：使用单一阈值验证
        if self.trading.close_spread_min < 0:
            # 负值配置：现货溢价阈值
            logging.info(f"✅ 现货溢价阈值配置验证通过：现货溢价≥{abs(self.trading.close_spread_min)*100:.1f}%时平仓")
        else:
            # 正值配置：期货溢价阈值
            # 正值配置下，平仓阈值不应大于开仓最小价差
            if self.trading.close_spread_min > self.trading.min_spread:
                logging.error(f"平仓阈值不应大于开仓最小价差，当前: close_spread_min={self.trading.close_spread_min}, min_spread={self.trading.min_spread}")
                raise ValueError("平仓阈值不应大于开仓最小价差")
            logging.info(f"✅ 期货溢价阈值配置验证通过：期货溢价≤{self.trading.close_spread_min*100:.1f}%时平仓")
    
    def get_exchange_config(self, exchange_name: str) -> ExchangeConfig:
        """获取指定交易所配置"""
        if exchange_name not in self.exchanges:
            raise ValueError(f"不支持的交易所: {exchange_name}")
        return self.exchanges[exchange_name]
    
    def get_available_combinations(self) -> list:
        """获取所有可用的套利组合"""
        return self.arbitrage_combinations
    
    def get(self, key: str, default=None):
        """获取配置值的通用方法"""
        return os.getenv(key, default)
    
    def get_network_config(self) -> Dict[str, Any]:
        """获取网络配置"""
        return {
            'http': self.network_config_manager.get_http_config(),
            'retry': self.network_config_manager.get_retry_config(),
            'websocket': self.network_config_manager.get_websocket_config(),
            'timing': self.network_config_manager.get_timing_config()
        }
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式，便于日志记录"""
        return {
            'exchanges': {name: {
                'api_key': f"***{config.api_key[-4:]}" if config.api_key and len(config.api_key) > 4 else "未配置",
                'total_balance': str(config.total_balance),
                'is_unified_account': config.is_unified_account
            } for name, config in self.exchanges.items()},
            'trading': {
                'min_spread': self.trading.min_spread,
                'max_spread': self.trading.max_spread,
                'spot_slippage': self.trading.spot_slippage,
                'max_retry_times': self.trading.max_retry_times
            },
            'system': {
                'debug_mode': self.system.debug_mode,
                'log_level': self.system.log_level,
                'allow_low_balance_test': self.system.allow_low_balance_test
            }
        }


# 全局配置实例
_global_settings = None


def get_config() -> Settings:
    """获取全局配置实例"""
    global _global_settings
    if _global_settings is None:
        _global_settings = Settings()
    return _global_settings


def load_config() -> Settings:
    """加载配置（兼容性函数，等同于get_config）"""
    return get_config()


def reset_config():
    """重置配置（主要用于测试）"""
    global _global_settings
    _global_settings = None


# 🔥 生产环境：移除测试代码，保持配置模块纯净