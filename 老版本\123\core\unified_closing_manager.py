# -*- coding: utf-8 -*-
"""
🔥 统一平仓管理器

提供跨交易所的统一平仓接口，支持：
- 现货平仓
- 期货平仓  
- 紧急平仓
- 重试机制
- API精度兼容
"""

import logging
import time
import asyncio
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from exchanges.exchanges_base import OrderSide, OrderType, AccountType

logger = logging.getLogger(__name__)

@dataclass
class ClosingOrderParams:
    """平仓订单参数"""
    symbol: str
    side: str
    order_type: str
    quantity: str  # 格式化后的数量字符串
    price: Optional[str] = None  # 格式化后的价格字符串
    market_type: str = "spot"
    
    # 调试信息
    original_quantity: float = 0.0
    original_price: Optional[float] = None
    precision_used: int = 4
    is_retry: bool = False
    retry_count: int = 0
    exchange_name: str = ""

@dataclass
class ClosingResult:
    """平仓结果"""
    success: bool
    order_id: Optional[str] = None
    executed_quantity: float = 0.0
    executed_price: float = 0.0
    error_message: Optional[str] = None
    execution_time_ms: float = 0.0
    retry_count: int = 0
    
    # 调试信息
    params_used: Optional[ClosingOrderParams] = None

class UnifiedClosingManager:
    """🔥 统一平仓管理器 - API精度+步长+缓存+重试机制"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # 🔥 使用交易规则预加载器
        from core.trading_rules_preloader import get_trading_rules_preloader
        self.rules_preloader = get_trading_rules_preloader()
        
        # 🔥 重试配置
        self.max_retries = 3
        self.retry_precisions = [6, 4, 2, 1]  # 重试时使用的精度序列
        
        # 性能统计
        self.stats = {
            "total_closings": 0,
            "successful_closings": 0,
            "failed_closings": 0,
            "total_retries": 0,
            "avg_execution_time_ms": 0.0,
            "precision_errors": 0,
            "step_size_errors": 0
        }
        
        self.logger.info("✅ 统一平仓管理器初始化完成")
        self.logger.info("   🔥 API精度+步长+缓存+重试机制")
        self.logger.info("   🔥 严格截取，绝不四舍五入")
        self.logger.info(f"   最大重试次数: {self.max_retries}")
        self.logger.info(f"   重试精度序列: {self.retry_precisions}")
    
    async def prepare_closing_params(self, 
                                   symbol: str, 
                                   side: str,
                                   quantity: float, 
                                   price: Optional[float],
                                   exchange,
                                   market_type: str = "spot",
                                   order_type: str = "market",
                                   retry_precision: Optional[int] = None,
                                   retry_count: int = 0) -> Optional[ClosingOrderParams]:
        """
        🔥 核心方法：准备平仓参数
        
        平仓：API精度+步长+缓存+通用正确步长+重试机制
        """
        try:
            # 🔥 修复：统一交易所名称格式
            exchange_name = exchange.__class__.__name__.lower().replace("exchange", "").replace("mock", "")
            
            # 🔥 使用预加载的交易规则（重试机制）
            order_data = self.rules_preloader.prepare_closing_order(
                exchange_name, symbol, market_type, quantity, price, retry_precision
            )
            
            if not order_data:
                self.logger.error(f"无法准备平仓订单: {exchange_name} {symbol} {market_type}")
                return None
            
            # 🔥 创建平仓参数
            params = ClosingOrderParams(
                symbol=symbol,
                side=side,
                order_type=order_type,
                quantity=order_data["quantity"],
                price=order_data["price"],
                market_type=market_type,
                original_quantity=quantity,
                original_price=price,
                precision_used=order_data["precision_used"],
                is_retry=order_data["is_retry"],
                retry_count=retry_count,
                exchange_name=exchange_name
            )
            
            self.logger.info(f"✅ 平仓参数准备完成: {exchange_name} {symbol}")
            self.logger.info(f"   数量: {quantity:.6f} → {order_data['quantity']} (精度: {order_data['precision_used']})")
            if order_data["is_retry"]:
                self.logger.info(f"   🔄 重试模式: 第{retry_count}次重试")
            if order_data["price"]:
                self.logger.info(f"   价格: {price:.6f} → {order_data['price']}")
            
            return params
            
        except Exception as e:
            self.logger.error(f"准备平仓参数失败: {symbol} {side} {quantity} - {e}")
            return None
    
    async def execute_closing_order_with_retry(self, 
                                             symbol: str,
                                             side: str,
                                             quantity: float,
                                             exchange,
                                             market_type: str = "spot",
                                             price: Optional[float] = None,
                                             orderbook: Dict = None) -> ClosingResult:
        """
        🔥 执行平仓订单 - 带重试机制
        
        平仓：API精度+步长+缓存+通用正确步长+重试机制
        """
        start_time = time.time()
        
        try:
            self.stats["total_closings"] += 1
            
            # 🔥 第一次尝试：使用API精度
            params = await self.prepare_closing_params(
                symbol, side, quantity, price, exchange, market_type
            )
            
            if params:
                result = await self._execute_single_closing_order(params, exchange, orderbook)
                if result.success:
                    self.stats["successful_closings"] += 1
                    result.execution_time_ms = (time.time() - start_time) * 1000
                    return result
                
                # 🔥 检查是否是精度/步长错误
                if self._is_precision_error(result.error_message):
                    self.stats["precision_errors"] += 1
                    self.logger.warning(f"检测到精度错误，启动重试机制: {result.error_message}")
                    
                    # 🔥 重试机制：使用不同精度
                    for retry_count, retry_precision in enumerate(self.retry_precisions, 1):
                        if retry_count > self.max_retries:
                            break
                        
                        self.stats["total_retries"] += 1
                        self.logger.info(f"🔄 平仓重试 {retry_count}/{self.max_retries}: 使用精度 {retry_precision}")
                        
                        retry_params = await self.prepare_closing_params(
                            symbol, side, quantity, price, exchange, market_type,
                            retry_precision=retry_precision, retry_count=retry_count
                        )
                        
                        if retry_params:
                            retry_result = await self._execute_single_closing_order(retry_params, exchange, orderbook)
                            retry_result.retry_count = retry_count
                            
                            if retry_result.success:
                                self.stats["successful_closings"] += 1
                                retry_result.execution_time_ms = (time.time() - start_time) * 1000
                                self.logger.info(f"✅ 平仓重试成功: 第{retry_count}次重试，精度{retry_precision}")
                                return retry_result
                            else:
                                self.logger.warning(f"❌ 平仓重试失败: 第{retry_count}次重试 - {retry_result.error_message}")
                    
                    # 所有重试都失败
                    self.stats["failed_closings"] += 1
                    return ClosingResult(
                        success=False,
                        error_message=f"所有重试都失败，最后错误: {result.error_message}",
                        execution_time_ms=(time.time() - start_time) * 1000,
                        retry_count=self.max_retries
                    )
                else:
                    # 非精度错误，不重试
                    self.stats["failed_closings"] += 1
                    result.execution_time_ms = (time.time() - start_time) * 1000
                    return result
            
            # 参数准备失败
            self.stats["failed_closings"] += 1
            return ClosingResult(
                success=False,
                error_message="参数准备失败",
                execution_time_ms=(time.time() - start_time) * 1000
            )
            
        except Exception as e:
            self.stats["failed_closings"] += 1
            error_msg = f"平仓执行异常: {e}"
            self.logger.error(error_msg)
            return ClosingResult(
                success=False,
                error_message=error_msg,
                execution_time_ms=(time.time() - start_time) * 1000
            )
    
    async def _execute_single_closing_order(self, 
                                          params: ClosingOrderParams, 
                                          exchange,
                                          orderbook: Dict = None) -> ClosingResult:
        """执行单次平仓订单"""
        try:
            self.logger.info(f"🚀 执行平仓订单: {params.exchange_name} {params.symbol}")
            self.logger.info(f"   参数: side={params.side}, quantity={params.quantity}, market_type={params.market_type}")
            if params.is_retry:
                self.logger.info(f"   🔄 重试模式: 第{params.retry_count}次，精度{params.precision_used}")
            
            # 🔥 关键修复：现货平仓使用SpotTrader的market_sell方法，确保orderbook传递实时WebSocket数据
            if params.market_type == "spot":
                # 获取SpotTrader实例
                try:
                    from core.execution_engine import get_execution_engine
                    engine = get_execution_engine()
                    
                    # 获取对应的SpotTrader
                    exchange_name = params.exchange_name
                    spot_trader = getattr(engine, 'spot_traders', {}).get(exchange_name)
                    
                    if spot_trader and params.side == "sell":
                        # 🔥 使用SpotTrader的market_sell方法，传递orderbook参数
                        self.logger.info(f"🔍 使用SpotTrader.market_sell平仓: {params.symbol}")
                        
                        # 🔥 关键修复：确保orderbook参数包含实时WebSocket数据，不使用历史数据
                        if orderbook is None:
                            self.logger.warning(f"⚠️ orderbook参数为None，获取实时WebSocket深度数据")
                            try:
                                # 🔥 修复：直接使用ExecutionEngine的WebSocket数据获取方法
                                if hasattr(engine, '_get_websocket_orderbook'):
                                    orderbook = engine._get_websocket_orderbook(exchange_name, params.symbol, "spot")
                                    
                                    if orderbook and orderbook.get('asks') and orderbook.get('bids'):
                                        # 🔥 记录实时数据使用情况
                                        timestamp = orderbook.get('timestamp', 0)
                                        data_age = (time.time() * 1000 - timestamp) if timestamp else 0
                                        self.logger.info(f"✅ 成功获取实时WebSocket现货深度数据用于平仓")
                                        self.logger.info(f"   数据新鲜度: {data_age:.1f}ms")
                                        self.logger.info(f"   深度质量: asks={len(orderbook.get('asks', []))}, bids={len(orderbook.get('bids', []))}")
                                    else:
                                        self.logger.warning(f"⚠️ 实时WebSocket深度数据无效，使用基础平仓")
                                        orderbook = None
                                else:
                                    self.logger.warning(f"⚠️ ExecutionEngine未提供WebSocket数据获取方法")
                                    orderbook = None
                            except Exception as e:
                                self.logger.warning(f"⚠️ 获取实时WebSocket深度数据失败: {e}，使用基础平仓")
                                orderbook = None
                        else:
                            # 🔥 验证传入的orderbook是否为实时数据
                            if isinstance(orderbook, dict) and orderbook.get('timestamp'):
                                timestamp = orderbook.get('timestamp', 0)
                                data_age = (time.time() * 1000 - timestamp) if timestamp else 0
                                self.logger.info(f"✅ 使用传入的实时orderbook数据，数据年龄: {data_age:.1f}ms")
                            else:
                                self.logger.warning(f"⚠️ 传入的orderbook数据可能不是实时数据")
                        
                        # 🔥 关键修复：正确调用SpotTrader.market_sell，确保实时orderbook参数传递
                        result = await spot_trader.market_sell(
                            symbol=params.symbol,
                            amount=float(params.quantity),
                            orderbook=orderbook,  # 🔥 关键：传递实时WebSocket orderbook参数
                            disable_split=True  # 平仓不拆单
                        )
                        
                        if result and result.get('status') in ['filled', 'open', 'new', 'success']:
                            self.logger.info(f"✅ 现货平仓成功（使用实时WebSocket数据）: {result.get('id', '')}")
                            return ClosingResult(
                                success=True,
                                order_id=result.get('id', ''),
                                executed_quantity=result.get('filled', float(params.quantity)),
                                executed_price=result.get('average', 0),
                                retry_count=params.retry_count,
                                params_used=params
                            )
                        else:
                            error_msg = result.get('error', f"SpotTrader平仓失败: {result}")
                            return ClosingResult(
                                success=False,
                                error_message=error_msg,
                                retry_count=params.retry_count,
                                params_used=params
                            )
                    
                except Exception as trader_error:
                    self.logger.warning(f"SpotTrader平仓失败，使用交易所接口: {trader_error}")
            
            # 🔥 兜底方案：使用交易所统一接口（期货平仓或SpotTrader失败的现货平仓）
            order_payload = {
                "symbol": params.symbol,
                "side": params.side,
                "order_type": params.order_type,
                "amount": float(params.quantity),
                "market_type": params.market_type
            }
            
            if params.price:
                order_payload["price"] = float(params.price)
            
            # 🔥 调用交易所统一接口
            order_result = await exchange.place_order(**order_payload)
            
            # 🔥 解析结果
            if order_result and order_result.get("order_id"):
                result = ClosingResult(
                    success=True,
                    order_id=order_result.get("order_id"),
                    executed_quantity=order_result.get("amount", float(params.quantity)),
                    executed_price=order_result.get("price", float(params.price or 0)),
                    retry_count=params.retry_count,
                    params_used=params
                )
                
                self.logger.info(f"✅ 平仓成功: {result.order_id}")
                self.logger.info(f"   执行数量: {result.executed_quantity}")
                self.logger.info(f"   执行价格: {result.executed_price}")
                
                return result
            else:
                error_msg = f"平仓失败: 无效的订单结果 - {order_result}"
                self.logger.error(error_msg)
                return ClosingResult(
                    success=False,
                    error_message=error_msg,
                    retry_count=params.retry_count,
                    params_used=params
                )
                
        except Exception as e:
            error_msg = f"平仓执行异常: {e}"
            self.logger.error(error_msg)
            return ClosingResult(
                success=False,
                error_message=error_msg,
                retry_count=params.retry_count,
                params_used=params
            )
    
    def _is_precision_error(self, error_message: Optional[str]) -> bool:
        """检查是否是精度/步长错误"""
        if not error_message:
            return False
        
        error_message = error_message.lower()
        precision_error_keywords = [
            "qty invalid",
            "quantity invalid", 
            "step size",
            "precision",
            "decimal places",
            "invalid amount",
            "amount precision",
            "lot size",
            "tick size"
        ]
        
        return any(keyword in error_message for keyword in precision_error_keywords)
    
    async def unified_market_sell_close(self, 
                                      symbol: str, 
                                      quantity: float, 
                                      exchange,
                                      market_type: str = "spot") -> ClosingResult:
        """🔥 统一市价卖出平仓接口"""
        return await self.execute_closing_order_with_retry(
            symbol=symbol,
            side="sell",
            quantity=quantity,
            exchange=exchange,
            market_type=market_type
        )
    
    async def unified_market_buy_close(self,
                                     symbol: str,
                                     quantity: float,
                                     exchange,
                                     market_type: str = "spot") -> ClosingResult:
        """🔥 统一市价买入平仓接口"""
        return await self.execute_closing_order_with_retry(
            symbol=symbol,
            side="buy",
            quantity=quantity,
            exchange=exchange,
            market_type=market_type
        )

    async def close_position_unified(self, symbol: str, exchange, market_type: str,
                                   side: str = None, orderbook: Dict = None) -> ClosingResult:
        """🔥 统一平仓接口 - 支持所有交易所"""
        try:
            self.logger.info(f"🔥 统一平仓: {symbol} {market_type}")

            if market_type == "spot":
                # 现货平仓：需要指定数量和方向
                if not side:
                    return ClosingResult(
                        success=False,
                        error_message="现货平仓需要指定side参数"
                    )
                
                # 🔥 修复：获取现货余额作为平仓数量，按照全流程工作流.md使用统一余额系统
                try:
                    base_currency = symbol.split('-')[0]  # 如 LINK-USDT → LINK
                    
                    # 🔥 使用统一账户兼容性检查
                    if hasattr(exchange, 'is_unified_account') and exchange.is_unified_account():
                        # 统一账户（Bybit、OKX）
                        balance = await exchange.get_balance(AccountType.UNIFIED)
                    else:
                        # 分离账户（Gate.io）
                        balance = await exchange.get_balance(AccountType.SPOT)
                    
                    # 🔥 修复：正确解析余额格式，防止dict+dict错误
                    available_amount = 0.0
                    if base_currency in balance:
                        balance_info = balance[base_currency]
                        if isinstance(balance_info, dict):
                            # 优先使用available，其次使用free，最后使用total
                            available_amount = float(balance_info.get('available', 
                                                   balance_info.get('free', 
                                                   balance_info.get('total', 0.0))))
                        elif isinstance(balance_info, (int, float, str)):
                            available_amount = float(balance_info) if balance_info else 0.0
                        else:
                            self.logger.warning(f"⚠️ 未知余额格式: {type(balance_info)} = {balance_info}")
                            available_amount = 0.0
                    
                    self.logger.info(f"🔍 现货余额查询: {base_currency} = {available_amount:.8f}")
                    
                    if available_amount <= 0.000001:  # 使用更小的阈值，避免精度问题
                        return ClosingResult(
                            success=False,
                            error_message=f"无{base_currency}现货持仓需要平仓，余额: {available_amount:.8f}"
                        )
                    
                    self.logger.info(f"🔍 检测到现货持仓: {available_amount:.8f} {base_currency}")
                    
                    # 🔥 使用获取的余额数量进行平仓，传递orderbook参数
                    result = await self.execute_closing_order_with_retry(
                        symbol=symbol,
                        side=side,
                        quantity=available_amount,
                        exchange=exchange,
                        market_type=market_type,
                        orderbook=orderbook  # 🔥 关键：传递orderbook参数
                    )
                    
                    # 🔥 删除重复验证：平仓成功后不再重复验证，避免重复API调用
                    # 根据MD文档要求，消除重复验证逻辑，提升性能
                    if result.success:
                        self.logger.info(f"✅ 现货平仓成功: {symbol} 数量={result.executed_quantity:.8f}")
                        # 🔥 删除重复验证和重试逻辑，避免重复get_position调用
                    
                    return result

                except Exception as e:
                    self.logger.error(f"获取现货持仓失败: {e}")
                    return ClosingResult(
                        success=False,
                        error_message=f"获取现货持仓失败: {e}"
                    )
            else:
                # 🔥 修复：期货平仓 - 直接使用原生API，避免递归调用
                try:
                    # 获取期货持仓
                    positions = await exchange.get_position(symbol)
                    
                    # 找到有效持仓
                    valid_position = None
                    for pos in positions:
                        position_size = float(pos.get('size', 0))
                        if position_size > 0:
                            valid_position = pos
                            break
                    
                    if not valid_position:
                        return ClosingResult(
                            success=False,
                            error_message=f"无{symbol}期货持仓需要平仓"
                        )
                    
                    position_size = float(valid_position.get('size', 0))
                    position_side = valid_position.get('side', 'unknown')
                    self.logger.info(f"🔍 检测到期货持仓: {position_size} {symbol} ({position_side})")

                    # 🔥 关键修复：调用统一模块进行数量处理，避免重复造轮子
                    # 使用trading_rules_preloader的统一数量处理功能
                    exchange_name = getattr(exchange, 'name', 'unknown').lower()
                    processed_amount = position_size

                    # 🔥 修复：删除手动反向转换，使用统一的合约转换方法
                    # 平仓时直接使用持仓数量，由TradingRulesPreloader统一处理合约转换
                    processed_amount = position_size
                    self.logger.info(f"🔧 期货平仓使用持仓数量: {processed_amount}")

                    # 🔥 修复：直接使用原生期货平仓API，避免调用exchange.close_position造成递归
                    # 根据持仓方向确定平仓方向
                    close_side_enum = OrderSide.BUY if position_side.lower() in ["short", "sell"] else OrderSide.SELL

                    # 🔥 关键修复：期货平仓使用专用方法，避免合约转换错误
                    self.logger.info(f"🚀 执行期货平仓订单: {symbol} {close_side_enum.value} {processed_amount} (MARKET)")

                    # 🔥 修复：对于OKX期货，直接调用专用平仓API，避免合约转换
                    exchange_name = getattr(exchange, 'name', 'unknown').lower()
                    
                    if exchange_name == "okx":
                        # 🔥 OKX期货平仓：直接调用create_futures_order，跳过合约转换
                        result = await exchange.create_futures_order(
                            symbol=symbol,
                            side=close_side_enum,
                            order_type=OrderType.MARKET,
                            amount=processed_amount,  # 直接使用持仓数量，不进行合约转换
                            params={
                                "reduceOnly": True,  # 只减仓
                                "timeInForce": "IOC",  # 立即成交或取消
                                "tdMode": "cross"  # 全仓模式
                            },
                            skip_contract_conversion=True  # 🔥 关键：跳过合约转换
                        )
                    else:
                        # 🔥 其他交易所：使用标准place_order方法
                        place_order_params = {
                            "symbol": symbol,
                            "side": close_side_enum,
                            "order_type": OrderType.MARKET,  # 强制市价单
                            "amount": processed_amount,  # 使用统一模块处理后的数量
                            "market_type": "futures",
                            "params": {
                                "reduceOnly": True,  # 关键：只减仓，避免开新仓
                                "timeInForce": "IOC",  # 立即成交或取消，避免挂单
                                "close": True,  # Gate.io专用平仓标识
                                "positionSide": "net"  # 网格仓位模式
                            }
                        }
                        
                        self.logger.info(f"🔧 期货平仓参数: {place_order_params}")
                        result = await exchange.place_order(**place_order_params)
                    
                    self.logger.info(f"🔧 期货平仓结果: {result}")

                    if result and result.get("order_id"):
                        closing_result = ClosingResult(
                            success=True,
                            order_id=result.get("order_id", ""),
                            executed_quantity=result.get("amount", position_size),
                            executed_price=result.get("price", 0)
                        )
                        
                        # 🔥 删除重复验证：期货平仓成功后不再重复验证，避免重复get_position调用
                        # 根据MD文档要求，消除重复验证逻辑，提升性能
                        self.logger.info(f"✅ 期货平仓成功: {symbol} 数量={closing_result.executed_quantity:.8f}")
                        # 🔥 删除重复验证和重试逻辑，避免重复get_position调用
                        
                        return closing_result
                    else:
                        return ClosingResult(
                            success=False,
                            error_message=f"期货平仓失败: {result}"
                        )
                        
                except Exception as e:
                    return ClosingResult(
                        success=False,
                        error_message=f"期货平仓异常: {e}"
                    )

        except Exception as e:
            self.logger.error(f"统一平仓失败: {e}")
            return ClosingResult(
                success=False,
                error_message=f"统一平仓异常: {e}"
            )

    async def close_spot_position(self, symbol: str, quantity: float, exchange) -> ClosingResult:
        """🔥 平仓现货仓位"""
        return await self.execute_closing_order_with_retry(
            symbol=symbol,
            side="sell",  # 现货平仓通常是卖出
            quantity=quantity,
            exchange=exchange,
            market_type="spot"
        )

    async def close_futures_position(self, symbol: str, exchange,
                                   close_type: str = "market") -> ClosingResult:
        """🔥 平仓期货仓位"""
        try:
            # 🔥 修复：使用统一期货平仓逻辑，避免递归调用
            return await self.close_position_unified(
                symbol=symbol,
                exchange=exchange,
                market_type="futures"
            )
        except Exception as e:
            return ClosingResult(
                success=False,
                error_message=f"期货平仓异常: {e}"
            )

    async def emergency_close_all(self, exchange, symbols: List[str]) -> List[ClosingResult]:
        """🔥 紧急平仓所有仓位"""
        results = []

        for symbol in symbols:
            try:
                # 尝试平仓现货
                spot_result = await self.close_position_unified(symbol, exchange, "spot", "sell")
                results.append(spot_result)

                # 尝试平仓期货
                futures_result = await self.close_position_unified(symbol, exchange, "futures")
                results.append(futures_result)

            except Exception as e:
                results.append(ClosingResult(
                    success=False,
                    error_message=f"紧急平仓{symbol}失败: {e}"
                ))

        return results

    # 🔥 删除重复验证方法：_verify_position_closed已删除，避免重复get_position调用
    # 根据MD文档要求，消除所有重复验证逻辑，提升性能

    # 🔥 删除重复重试方法：_retry_remaining_position已删除，避免重复get_position调用
    # 根据MD文档要求，消除所有重复验证和重试逻辑，提升性能
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计"""
        success_rate = (self.stats["successful_closings"] / self.stats["total_closings"] * 100) if self.stats["total_closings"] > 0 else 0
        avg_retries = (self.stats["total_retries"] / self.stats["total_closings"]) if self.stats["total_closings"] > 0 else 0
        
        return {
            **self.stats,
            "success_rate": success_rate,
            "avg_retries_per_closing": avg_retries
        }

# 🔥 全局实例
_closing_manager = None

def get_closing_manager() -> UnifiedClosingManager:
    """获取统一平仓管理器实例"""
    global _closing_manager
    if _closing_manager is None:
        _closing_manager = UnifiedClosingManager()
    return _closing_manager
