# coding: utf-8

"""
    Gate API v4

    Welcome to Gate.io API  APIv4 provides spot, margin and futures trading operations. There are public APIs to retrieve the real-time market statistics, and private APIs which needs authentication to trade on user's behalf.  # noqa: E501

    Contact: <EMAIL>
    Generated by: https://openapi-generator.tech
"""


import pprint
import re  # noqa: F401

import six

from gate_api.configuration import Configuration


class FlashSwapOrder(object):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        'id': 'int',
        'create_time': 'int',
        'user_id': 'int',
        'sell_currency': 'str',
        'sell_amount': 'str',
        'buy_currency': 'str',
        'buy_amount': 'str',
        'price': 'str',
        'status': 'int'
    }

    attribute_map = {
        'id': 'id',
        'create_time': 'create_time',
        'user_id': 'user_id',
        'sell_currency': 'sell_currency',
        'sell_amount': 'sell_amount',
        'buy_currency': 'buy_currency',
        'buy_amount': 'buy_amount',
        'price': 'price',
        'status': 'status'
    }

    def __init__(self, id=None, create_time=None, user_id=None, sell_currency=None, sell_amount=None, buy_currency=None, buy_amount=None, price=None, status=None, local_vars_configuration=None):  # noqa: E501
        # type: (int, int, int, str, str, str, str, str, int, Configuration) -> None
        """FlashSwapOrder - a model defined in OpenAPI"""  # noqa: E501
        if local_vars_configuration is None:
            local_vars_configuration = Configuration()
        self.local_vars_configuration = local_vars_configuration

        self._id = None
        self._create_time = None
        self._user_id = None
        self._sell_currency = None
        self._sell_amount = None
        self._buy_currency = None
        self._buy_amount = None
        self._price = None
        self._status = None
        self.discriminator = None

        if id is not None:
            self.id = id
        if create_time is not None:
            self.create_time = create_time
        if user_id is not None:
            self.user_id = user_id
        if sell_currency is not None:
            self.sell_currency = sell_currency
        if sell_amount is not None:
            self.sell_amount = sell_amount
        if buy_currency is not None:
            self.buy_currency = buy_currency
        if buy_amount is not None:
            self.buy_amount = buy_amount
        if price is not None:
            self.price = price
        if status is not None:
            self.status = status

    @property
    def id(self):
        """Gets the id of this FlashSwapOrder.  # noqa: E501

        Flash swap order ID  # noqa: E501

        :return: The id of this FlashSwapOrder.  # noqa: E501
        :rtype: int
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this FlashSwapOrder.

        Flash swap order ID  # noqa: E501

        :param id: The id of this FlashSwapOrder.  # noqa: E501
        :type: int
        """

        self._id = id

    @property
    def create_time(self):
        """Gets the create_time of this FlashSwapOrder.  # noqa: E501

        Creation time of order (in milliseconds)  # noqa: E501

        :return: The create_time of this FlashSwapOrder.  # noqa: E501
        :rtype: int
        """
        return self._create_time

    @create_time.setter
    def create_time(self, create_time):
        """Sets the create_time of this FlashSwapOrder.

        Creation time of order (in milliseconds)  # noqa: E501

        :param create_time: The create_time of this FlashSwapOrder.  # noqa: E501
        :type: int
        """

        self._create_time = create_time

    @property
    def user_id(self):
        """Gets the user_id of this FlashSwapOrder.  # noqa: E501

        User ID  # noqa: E501

        :return: The user_id of this FlashSwapOrder.  # noqa: E501
        :rtype: int
        """
        return self._user_id

    @user_id.setter
    def user_id(self, user_id):
        """Sets the user_id of this FlashSwapOrder.

        User ID  # noqa: E501

        :param user_id: The user_id of this FlashSwapOrder.  # noqa: E501
        :type: int
        """

        self._user_id = user_id

    @property
    def sell_currency(self):
        """Gets the sell_currency of this FlashSwapOrder.  # noqa: E501

        Currency to sell  # noqa: E501

        :return: The sell_currency of this FlashSwapOrder.  # noqa: E501
        :rtype: str
        """
        return self._sell_currency

    @sell_currency.setter
    def sell_currency(self, sell_currency):
        """Sets the sell_currency of this FlashSwapOrder.

        Currency to sell  # noqa: E501

        :param sell_currency: The sell_currency of this FlashSwapOrder.  # noqa: E501
        :type: str
        """

        self._sell_currency = sell_currency

    @property
    def sell_amount(self):
        """Gets the sell_amount of this FlashSwapOrder.  # noqa: E501

        Amount to sell  # noqa: E501

        :return: The sell_amount of this FlashSwapOrder.  # noqa: E501
        :rtype: str
        """
        return self._sell_amount

    @sell_amount.setter
    def sell_amount(self, sell_amount):
        """Sets the sell_amount of this FlashSwapOrder.

        Amount to sell  # noqa: E501

        :param sell_amount: The sell_amount of this FlashSwapOrder.  # noqa: E501
        :type: str
        """

        self._sell_amount = sell_amount

    @property
    def buy_currency(self):
        """Gets the buy_currency of this FlashSwapOrder.  # noqa: E501

        Currency to buy  # noqa: E501

        :return: The buy_currency of this FlashSwapOrder.  # noqa: E501
        :rtype: str
        """
        return self._buy_currency

    @buy_currency.setter
    def buy_currency(self, buy_currency):
        """Sets the buy_currency of this FlashSwapOrder.

        Currency to buy  # noqa: E501

        :param buy_currency: The buy_currency of this FlashSwapOrder.  # noqa: E501
        :type: str
        """

        self._buy_currency = buy_currency

    @property
    def buy_amount(self):
        """Gets the buy_amount of this FlashSwapOrder.  # noqa: E501

        Amount to buy  # noqa: E501

        :return: The buy_amount of this FlashSwapOrder.  # noqa: E501
        :rtype: str
        """
        return self._buy_amount

    @buy_amount.setter
    def buy_amount(self, buy_amount):
        """Sets the buy_amount of this FlashSwapOrder.

        Amount to buy  # noqa: E501

        :param buy_amount: The buy_amount of this FlashSwapOrder.  # noqa: E501
        :type: str
        """

        self._buy_amount = buy_amount

    @property
    def price(self):
        """Gets the price of this FlashSwapOrder.  # noqa: E501

        Price  # noqa: E501

        :return: The price of this FlashSwapOrder.  # noqa: E501
        :rtype: str
        """
        return self._price

    @price.setter
    def price(self, price):
        """Sets the price of this FlashSwapOrder.

        Price  # noqa: E501

        :param price: The price of this FlashSwapOrder.  # noqa: E501
        :type: str
        """

        self._price = price

    @property
    def status(self):
        """Gets the status of this FlashSwapOrder.  # noqa: E501

        Flash swap order status  `1` - success `2` - failure  # noqa: E501

        :return: The status of this FlashSwapOrder.  # noqa: E501
        :rtype: int
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this FlashSwapOrder.

        Flash swap order status  `1` - success `2` - failure  # noqa: E501

        :param status: The status of this FlashSwapOrder.  # noqa: E501
        :type: int
        """

        self._status = status

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.openapi_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, FlashSwapOrder):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, FlashSwapOrder):
            return True

        return self.to_dict() != other.to_dict()
