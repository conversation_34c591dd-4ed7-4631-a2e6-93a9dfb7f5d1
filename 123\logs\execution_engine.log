2025-07-30 17:57:15.231 [INFO] [ExecutionEngine] 🔥 ExecutionEngine日志系统启动 - 使用统一日志配置
2025-07-30 17:57:15.231 [INFO] [ExecutionEngine] ================================================================================
2025-07-30 17:57:15.231 [INFO] [ExecutionEngine] 🚀 ExecutionEngine初始化开始
2025-07-30 17:57:15.231 [INFO] [ExecutionEngine] ================================================================================
2025-07-30 17:57:15.232 [INFO] [ExecutionEngine] ✅ 并行套利控制器集成完成
2025-07-30 17:57:15.232 [INFO] [ExecutionEngine]    📊 最大并行数量: 3
2025-07-30 17:57:15.232 [INFO] [ExecutionEngine]    🔒 完全向下兼容现有逻辑
2025-07-30 17:57:15.232 [INFO] [ExecutionEngine] ✅ ExecutionEngine初始化完成
2025-07-30 17:57:15.232 [INFO] [ExecutionEngine] 🚀 初始化ExecutionEngine...
2025-07-30 17:57:15.232 [INFO] [ExecutionEngine] 🚀 步骤1.1: 检查交易所实例...
2025-07-30 17:57:15.232 [INFO] [ExecutionEngine] ✅ 使用传递的交易所实例: ['gate', 'bybit', 'okx']
2025-07-30 17:57:15.232 [INFO] [ExecutionEngine] 🔍 gate交易所需要初始化...
2025-07-30 17:57:16.797 [INFO] [ExecutionEngine] ✅ gate交易所初始化完成
2025-07-30 17:57:16.797 [INFO] [ExecutionEngine] 🔍 bybit交易所需要初始化...
2025-07-30 17:57:18.462 [INFO] [ExecutionEngine] ✅ bybit交易所初始化完成
2025-07-30 17:57:18.462 [INFO] [ExecutionEngine] 🔍 okx交易所需要初始化...
2025-07-30 17:57:18.462 [INFO] [ExecutionEngine] ✅ okx交易所初始化完成
2025-07-30 17:57:18.462 [INFO] [ExecutionEngine] 🎯 步骤1.2: 交易所验证完成，可用: 3个
2025-07-30 17:57:18.462 [INFO] [ExecutionEngine] 🚀 步骤2.1: 开始初始化交易器...
2025-07-30 17:57:18.468 [INFO] [ExecutionEngine] 🔍 步骤2.2: 导入交易器模块完成
2025-07-30 17:57:18.468 [INFO] [ExecutionEngine] 🔍 步骤2.3.1: 为gate创建交易器...
2025-07-30 17:57:18.469 [INFO] [ExecutionEngine]    ✅ gate现货交易器创建完成
2025-07-30 17:57:18.469 [INFO] [ExecutionEngine]    ✅ gate期货交易器创建完成
2025-07-30 17:57:18.469 [INFO] [ExecutionEngine] 🔍 步骤2.3.2: 为bybit创建交易器...
2025-07-30 17:57:18.469 [INFO] [ExecutionEngine]    ✅ bybit现货交易器创建完成
2025-07-30 17:57:18.469 [INFO] [ExecutionEngine]    ✅ bybit期货交易器创建完成
2025-07-30 17:57:18.469 [INFO] [ExecutionEngine] 🔍 步骤2.3.3: 为okx创建交易器...
2025-07-30 17:57:18.469 [INFO] [ExecutionEngine]    ✅ okx现货交易器创建完成
2025-07-30 17:57:18.469 [INFO] [ExecutionEngine]    ✅ okx期货交易器创建完成
2025-07-30 17:57:18.469 [INFO] [ExecutionEngine] 🎯 步骤2.4: 交易器初始化完成，共创建: 6个交易器
2025-07-30 17:57:18.470 [INFO] [ExecutionEngine] 🚀 步骤3.1: 开始初始化统一管理器...
2025-07-30 17:57:18.470 [INFO] [ExecutionEngine] 🔍 步骤3.2: 获取统一开仓管理器...
2025-07-30 17:57:18.470 [INFO] [ExecutionEngine] ✅ 统一开仓管理器获取成功
2025-07-30 17:57:18.470 [INFO] [ExecutionEngine] 🔍 步骤3.3: 获取统一平仓管理器...
2025-07-30 17:57:18.470 [INFO] [ExecutionEngine] ✅ 统一平仓管理器获取成功
2025-07-30 17:57:18.470 [INFO] [ExecutionEngine] 🔍 步骤3.4: 获取交易规则预加载器...
2025-07-30 17:57:18.470 [INFO] [ExecutionEngine] ✅ 交易规则预加载器获取成功
2025-07-30 17:57:18.470 [INFO] [ExecutionEngine] 🔍 步骤3.5: 初始化保证金计算器...
2025-07-30 17:57:18.470 [INFO] [ExecutionEngine] ✅ 保证金计算器初始化成功
2025-07-30 17:57:18.470 [INFO] [ExecutionEngine] 🔍 步骤3.6: 检查开仓管理器初始化方法...
2025-07-30 17:57:18.470 [INFO] [ExecutionEngine] ℹ️ 开仓管理器无需初始化（已就绪）
2025-07-30 17:57:18.470 [INFO] [ExecutionEngine] 🔍 步骤3.7: 检查平仓管理器初始化方法...
2025-07-30 17:57:18.470 [INFO] [ExecutionEngine] ℹ️ 平仓管理器无需初始化（已就绪）
2025-07-30 17:57:18.470 [INFO] [ExecutionEngine] 🎯 步骤3.8: 统一管理器初始化完成
2025-07-30 17:57:18.471 [INFO] [ExecutionEngine] 🚀 步骤4.1: 开始初始化订单管理器...
2025-07-30 17:57:18.471 [INFO] [ExecutionEngine] 🔍 步骤4.2: 导入OrderManager模块完成
2025-07-30 17:57:18.471 [INFO] [ExecutionEngine] 🔍 步骤4.3: 创建OrderManager实例...
2025-07-30 17:57:18.471 [INFO] [ExecutionEngine] 🔍 现货交易器列表: ['gate', 'bybit', 'okx']
2025-07-30 17:57:18.471 [INFO] [ExecutionEngine] 🔍 期货交易器列表: ['gate', 'bybit', 'okx']
2025-07-30 17:57:18.471 [INFO] [ExecutionEngine] 🔍 检查现货交易器 gate: SpotTrader
2025-07-30 17:57:18.471 [INFO] [ExecutionEngine] 🔍 检查现货交易器 bybit: SpotTrader
2025-07-30 17:57:18.471 [INFO] [ExecutionEngine] 🔍 检查现货交易器 okx: SpotTrader
2025-07-30 17:57:18.471 [INFO] [ExecutionEngine] 🔍 检查期货交易器 gate: FuturesTrader
2025-07-30 17:57:18.471 [INFO] [ExecutionEngine] 🔍 检查期货交易器 bybit: FuturesTrader
2025-07-30 17:57:18.471 [INFO] [ExecutionEngine] 🔍 检查期货交易器 okx: FuturesTrader
2025-07-30 17:57:18.471 [INFO] [ExecutionEngine] 🔍 开始调用OrderManager构造函数...
2025-07-30 17:57:18.471 [INFO] [ExecutionEngine] ✅ OrderManager实例创建成功
2025-07-30 17:57:18.472 [INFO] [ExecutionEngine] 🎯 步骤4.4: 订单管理器初始化完成
2025-07-30 17:57:18.472 [INFO] [ExecutionEngine] 🚀 步骤5.1: 开始预加载交易规则...
2025-07-30 17:57:18.472 [INFO] [ExecutionEngine] 🔍 步骤5.2: 调用preload_all_trading_rules...
2025-07-30 17:57:26.259 [INFO] [ExecutionEngine] ✅ 步骤5.3: 交易规则预加载成功
2025-07-30 17:57:26.259 [INFO] [ExecutionEngine] 🎯 预加载统计:
2025-07-30 17:57:26.260 [INFO] [ExecutionEngine]    缓存规则数: 60
2025-07-30 17:57:26.260 [INFO] [ExecutionEngine]    成功加载: 120
2025-07-30 17:57:26.260 [INFO] [ExecutionEngine]    失败加载: 0
2025-07-30 17:57:26.260 [INFO] [ExecutionEngine]    预加载耗时: 13.2ms
2025-07-30 17:57:26.260 [INFO] [ExecutionEngine] 🎯 步骤5.4: 交易规则预加载阶段完成
2025-07-30 17:57:26.260 [INFO] [ExecutionEngine] ✅ ExecutionEngine初始化完成
2025-07-30 18:00:15.272 [INFO] [ExecutionEngine] ================================================================================
2025-07-30 18:00:15.272 [INFO] [ExecutionEngine] 🚀 【套利执行开始】 ICNT-USDT
2025-07-30 18:00:15.272 [INFO] [ExecutionEngine]    现货交易所: gate
2025-07-30 18:00:15.272 [INFO] [ExecutionEngine]    期货交易所: bybit
2025-07-30 18:00:15.272 [INFO] [ExecutionEngine]    基础数量: 153.73383920624713
2025-07-30 18:00:15.272 [INFO] [ExecutionEngine]    价差百分比: 1.03%
2025-07-30 18:00:15.272 [INFO] [ExecutionEngine] ================================================================================
2025-07-30 18:00:15.273 [INFO] [ExecutionEngine] 📊 EXECUTION_START | ICNT-USDT | gate -> bybit | 差价=1.03%
2025-07-30 18:00:15.273 [INFO] [ExecutionEngine] 🔍 执行步骤A.0: 并行控制检查...
2025-07-30 18:00:15.273 [INFO] [ExecutionEngine] ✅ 并行控制检查通过，订单编号: ARB_ICNT-USDT_1753891215
2025-07-30 18:00:15.273 [INFO] [ExecutionEngine] 🔍 执行步骤A.1: 更新执行状态...
2025-07-30 18:00:15.273 [INFO] [ExecutionEngine] ✅ 执行状态已更新为EXECUTING
2025-07-30 18:00:15.273 [INFO] [ExecutionEngine] 🔍 执行步骤A.2: 创建执行结果对象...
2025-07-30 18:00:15.273 [INFO] [ExecutionEngine] ✅ 执行结果对象创建完成
2025-07-30 18:00:15.273 [INFO] [ExecutionEngine] 🔍 执行步骤B.1: 开始98%对冲质量预检查...
2025-07-30 18:00:15.273 [INFO] [ExecutionEngine] 📊 6大缓存系统使用统计:
2025-07-30 18:00:15.274 [INFO] [ExecutionEngine]    - 余额缓存: 实时更新 (ArbitrageEngine)
2025-07-30 18:00:15.274 [INFO] [ExecutionEngine]    - 保证金缓存: 5分钟TTL (MarginCalculator)
2025-07-30 18:00:15.274 [INFO] [ExecutionEngine]    - 交易规则缓存: N/A个规则 (TradingRulesPreloader)
2025-07-30 18:00:15.274 [INFO] [ExecutionEngine]    - 精度缓存: 24小时TTL (TradingRulesPreloader)
2025-07-30 18:00:15.274 [INFO] [ExecutionEngine]    - 订单簿缓存: 实时更新 (WebSocket)
2025-07-30 18:00:15.274 [INFO] [ExecutionEngine]    - 对冲质量缓存: 10秒TTL (预检查)
2025-07-30 18:00:15.274 [INFO] [ExecutionEngine] 📈 缓存命中率: 100.0% (总条目: 0)
2025-07-30 18:00:15.275 [INFO] [ExecutionEngine] 🎯 统一对冲质量检查: 比例=0.9899 (✅通过)
2025-07-30 18:00:15.275 [INFO] [ExecutionEngine] ✅ 执行步骤B.1完成: 对冲质量检查通过
2025-07-30 18:00:15.275 [INFO] [ExecutionEngine] 🔍 执行步骤C.1: 开始极速并行执行...
2025-07-30 18:00:15.275 [DEBUG] [ExecutionEngine] 🔥 开始<30ms极速并行执行: 现货(gate) + 期货(bybit) ICNT-USDT
2025-07-30 18:00:15.275 [INFO] [ExecutionEngine] 🔧 统一金额计算: $35.0 ÷ 0.227666 = 153.733839 代币
2025-07-30 18:00:15.275 [INFO] [ExecutionEngine] 🔥 智能协调开始: 原始数量=153.73383900币
2025-07-30 18:00:15.276 [INFO] [ExecutionEngine] 🔥 bybit期货步长截取: 153.733839 → 153.733币
2025-07-30 18:00:15.276 [INFO] [ExecutionEngine] 🎯 智能协调结果: 现货=153.733800币, 期货=153.733000币
2025-07-30 18:00:15.276 [INFO] [ExecutionEngine] 📊 对冲质量检查已在TradingRulesPreloader中完成（包含完美对冲逻辑）
2025-07-30 18:00:15.276 [INFO] [ExecutionEngine] ✅ 智能协调完成: 现货=153.733800币, 期货=153.733000币
2025-07-30 18:00:15.276 [INFO] [ExecutionEngine] 🔥 统一单位：返回币数量，各交易所在下单时自行转换
2025-07-30 18:00:15.276 [INFO] [ExecutionEngine] ✅ 统一架构：数量协调完成，精度转换由各交易所统一处理
2025-07-30 18:00:15.276 [INFO] [ExecutionEngine]    协调后数量: 现货=153.73380000, 期货=153.73300000
2025-07-30 18:00:15.276 [INFO] [ExecutionEngine]    对冲质量检查已在预检查阶段完成，符合MD文件统一计算要求
2025-07-30 18:00:15.276 [DEBUG] [ExecutionEngine] ⚡ 零延迟参数准备: 1.45ms
2025-07-30 18:00:15.332 [INFO] [ExecutionEngine] ✅ 开仓验证：数据快照验证通过，使用快照数据确保一致性
2025-07-30 18:00:15.332 [DEBUG] [ExecutionEngine] ✅ 快照详情: 快照降级使用: 4620.0ms > 800ms (标准阈值)
2025-07-30 18:00:15.332 [DEBUG] [ExecutionEngine] ✅ 使用数据快照，跳过新鲜度检查
2025-07-30 18:00:15.332 [DEBUG] [ExecutionEngine] ✅ 使用快照中的差价计算结果: 1.025%
2025-07-30 18:00:15.332 [DEBUG] [ExecutionEngine] ✅ 开仓验证通过: 当前价差1.025%
2025-07-30 18:00:15.332 [DEBUG] [ExecutionEngine] 🔍 [ORDERBOOK] 验证market_data结构:
2025-07-30 18:00:15.332 [DEBUG] [ExecutionEngine]    market_data类型: <class 'core.opportunity_scanner.MarketData'>
2025-07-30 18:00:15.332 [DEBUG] [ExecutionEngine]    是否有orderbook属性: True
2025-07-30 18:00:15.332 [DEBUG] [ExecutionEngine]    orderbook类型: <class 'dict'>
2025-07-30 18:00:15.332 [DEBUG] [ExecutionEngine]    orderbook是否为dict: True
2025-07-30 18:00:15.333 [DEBUG] [ExecutionEngine] 📊 [ORDERBOOK] gate_spot_ICNT-USDT - asks=10, bids=10
2025-07-30 18:00:15.333 [INFO] [ExecutionEngine] ✅ [ORDERBOOK] 成功获取订单簿: gate_spot_ICNT-USDT
2025-07-30 18:00:15.333 [INFO] [ExecutionEngine]    数据质量: asks=10档, bids=10档
2025-07-30 18:00:15.333 [DEBUG] [ExecutionEngine] 🔍 [ORDERBOOK] 验证market_data结构:
2025-07-30 18:00:15.333 [DEBUG] [ExecutionEngine]    market_data类型: <class 'core.opportunity_scanner.MarketData'>
2025-07-30 18:00:15.333 [DEBUG] [ExecutionEngine]    是否有orderbook属性: True
2025-07-30 18:00:15.333 [DEBUG] [ExecutionEngine]    orderbook类型: <class 'dict'>
2025-07-30 18:00:15.333 [DEBUG] [ExecutionEngine]    orderbook是否为dict: True
2025-07-30 18:00:15.333 [DEBUG] [ExecutionEngine] 📊 [ORDERBOOK] bybit_futures_ICNT-USDT - asks=30, bids=30
2025-07-30 18:00:15.334 [INFO] [ExecutionEngine] ✅ [ORDERBOOK] 成功获取订单簿: bybit_futures_ICNT-USDT
2025-07-30 18:00:15.334 [INFO] [ExecutionEngine]    数据质量: asks=30档, bids=30档
2025-07-30 18:00:15.422 [DEBUG] [ExecutionEngine] 🔄 订单簿同步验证失败，重试1/3: 订单簿数据非同步: 时间差2450.0ms > 800ms
2025-07-30 18:00:15.547 [DEBUG] [ExecutionEngine] 🔍 [ORDERBOOK] 验证market_data结构:
2025-07-30 18:00:15.548 [DEBUG] [ExecutionEngine]    market_data类型: <class 'core.opportunity_scanner.MarketData'>
2025-07-30 18:00:15.548 [DEBUG] [ExecutionEngine]    是否有orderbook属性: True
2025-07-30 18:00:15.548 [DEBUG] [ExecutionEngine]    orderbook类型: <class 'dict'>
2025-07-30 18:00:15.548 [DEBUG] [ExecutionEngine]    orderbook是否为dict: True
2025-07-30 18:00:15.548 [DEBUG] [ExecutionEngine] 📊 [ORDERBOOK] gate_spot_ICNT-USDT - asks=10, bids=10
2025-07-30 18:00:15.548 [INFO] [ExecutionEngine] ✅ [ORDERBOOK] 成功获取订单簿: gate_spot_ICNT-USDT
2025-07-30 18:00:15.548 [INFO] [ExecutionEngine]    数据质量: asks=10档, bids=10档
2025-07-30 18:00:15.548 [DEBUG] [ExecutionEngine] 🔍 [ORDERBOOK] 验证market_data结构:
2025-07-30 18:00:15.548 [DEBUG] [ExecutionEngine]    market_data类型: <class 'core.opportunity_scanner.MarketData'>
2025-07-30 18:00:15.549 [DEBUG] [ExecutionEngine]    是否有orderbook属性: True
2025-07-30 18:00:15.549 [DEBUG] [ExecutionEngine]    orderbook类型: <class 'dict'>
2025-07-30 18:00:15.549 [DEBUG] [ExecutionEngine]    orderbook是否为dict: True
2025-07-30 18:00:15.549 [DEBUG] [ExecutionEngine] 📊 [ORDERBOOK] bybit_futures_ICNT-USDT - asks=30, bids=30
2025-07-30 18:00:15.549 [INFO] [ExecutionEngine] ✅ [ORDERBOOK] 成功获取订单簿: bybit_futures_ICNT-USDT
2025-07-30 18:00:15.549 [INFO] [ExecutionEngine]    数据质量: asks=30档, bids=30档
2025-07-30 18:00:15.549 [DEBUG] [ExecutionEngine] 🔄 订单簿同步验证失败，重试2/3: 订单簿数据非同步: 时间差2670.0ms > 900ms
2025-07-30 18:00:15.685 [DEBUG] [ExecutionEngine] 🔍 [ORDERBOOK] 验证market_data结构:
2025-07-30 18:00:15.685 [DEBUG] [ExecutionEngine]    market_data类型: <class 'core.opportunity_scanner.MarketData'>
2025-07-30 18:00:15.685 [DEBUG] [ExecutionEngine]    是否有orderbook属性: True
2025-07-30 18:00:15.685 [DEBUG] [ExecutionEngine]    orderbook类型: <class 'dict'>
2025-07-30 18:00:15.685 [DEBUG] [ExecutionEngine]    orderbook是否为dict: True
2025-07-30 18:00:15.685 [DEBUG] [ExecutionEngine] 📊 [ORDERBOOK] gate_spot_ICNT-USDT - asks=10, bids=10
2025-07-30 18:00:15.685 [INFO] [ExecutionEngine] ✅ [ORDERBOOK] 成功获取订单簿: gate_spot_ICNT-USDT
2025-07-30 18:00:15.685 [INFO] [ExecutionEngine]    数据质量: asks=10档, bids=10档
2025-07-30 18:00:15.686 [DEBUG] [ExecutionEngine] 🔍 [ORDERBOOK] 验证market_data结构:
2025-07-30 18:00:15.686 [DEBUG] [ExecutionEngine]    market_data类型: <class 'core.opportunity_scanner.MarketData'>
2025-07-30 18:00:15.686 [DEBUG] [ExecutionEngine]    是否有orderbook属性: True
2025-07-30 18:00:15.686 [DEBUG] [ExecutionEngine]    orderbook类型: <class 'dict'>
2025-07-30 18:00:15.686 [DEBUG] [ExecutionEngine]    orderbook是否为dict: True
2025-07-30 18:00:15.686 [DEBUG] [ExecutionEngine] 📊 [ORDERBOOK] bybit_futures_ICNT-USDT - asks=30, bids=30
2025-07-30 18:00:15.686 [INFO] [ExecutionEngine] ✅ [ORDERBOOK] 成功获取订单簿: bybit_futures_ICNT-USDT
2025-07-30 18:00:15.686 [INFO] [ExecutionEngine]    数据质量: asks=30档, bids=30档
2025-07-30 18:00:15.687 [WARNING] [ExecutionEngine] ⚠️ 订单簿同步验证最终失败: 订单簿数据非同步: 时间差2890.0ms > 1000ms
2025-07-30 18:00:15.687 [WARNING] [ExecutionEngine] ⚠️ 经过3次重试后仍然失败: 订单簿数据非同步: 时间差2890.0ms > 1000ms
2025-07-30 18:00:15.687 [INFO] [ExecutionEngine] 🎯 执行步骤C.1完成: 并行执行耗时 411.90ms
2025-07-30 18:00:15.687 [ERROR] [ExecutionEngine] ❌ 执行步骤C.1失败: 并行执行失败，结束套利: ICNT-USDT
2025-07-30 18:00:15.687 [INFO] [ExecutionEngine] ✅ 执行失败，并行控制槽位已释放: ICNT-USDT
2025-07-30 18:00:16.774 [INFO] [ExecutionEngine] 🔧 重置ExecutionEngine状态...
2025-07-30 18:00:16.786 [WARNING] [ExecutionEngine] ⚠️ ExecutionEngine健康检查失败，执行强制重置
2025-07-30 18:00:16.786 [DEBUG] [ExecutionEngine] ✅ 执行锁已释放
2025-07-30 18:00:16.786 [WARNING] [ExecutionEngine] ⚠️ ExecutionEngine状态重置完成，但系统恢复验证失败
2025-07-30 18:00:40.147 [INFO] [ExecutionEngine] ================================================================================
2025-07-30 18:00:40.147 [INFO] [ExecutionEngine] 🚀 【套利执行开始】 ICNT-USDT
2025-07-30 18:00:40.147 [INFO] [ExecutionEngine]    现货交易所: gate
2025-07-30 18:00:40.147 [INFO] [ExecutionEngine]    期货交易所: bybit
2025-07-30 18:00:40.147 [INFO] [ExecutionEngine]    基础数量: 153.30705212439773
2025-07-30 18:00:40.147 [INFO] [ExecutionEngine]    价差百分比: 0.74%
2025-07-30 18:00:40.147 [INFO] [ExecutionEngine] ================================================================================
2025-07-30 18:00:40.147 [INFO] [ExecutionEngine] 📊 EXECUTION_START | ICNT-USDT | gate -> bybit | 差价=0.74%
2025-07-30 18:00:40.147 [INFO] [ExecutionEngine] 🔍 执行步骤A.0: 并行控制检查...
2025-07-30 18:00:40.148 [INFO] [ExecutionEngine] ✅ 并行控制检查通过，订单编号: ARB_ICNT-USDT_1753891240
2025-07-30 18:00:40.148 [INFO] [ExecutionEngine] 🔍 执行步骤A.1: 更新执行状态...
2025-07-30 18:00:40.148 [INFO] [ExecutionEngine] ✅ 执行状态已更新为EXECUTING
2025-07-30 18:00:40.148 [INFO] [ExecutionEngine] 🔍 执行步骤A.2: 创建执行结果对象...
2025-07-30 18:00:40.148 [INFO] [ExecutionEngine] ✅ 执行结果对象创建完成
2025-07-30 18:00:40.148 [INFO] [ExecutionEngine] 🔍 执行步骤B.1: 开始98%对冲质量预检查...
2025-07-30 18:00:40.148 [INFO] [ExecutionEngine] 📊 6大缓存系统使用统计:
2025-07-30 18:00:40.150 [INFO] [ExecutionEngine]    - 余额缓存: 实时更新 (ArbitrageEngine)
2025-07-30 18:00:40.150 [INFO] [ExecutionEngine]    - 保证金缓存: 5分钟TTL (MarginCalculator)
2025-07-30 18:00:40.150 [INFO] [ExecutionEngine]    - 交易规则缓存: N/A个规则 (TradingRulesPreloader)
2025-07-30 18:00:40.150 [INFO] [ExecutionEngine]    - 精度缓存: 24小时TTL (TradingRulesPreloader)
2025-07-30 18:00:40.151 [INFO] [ExecutionEngine]    - 订单簿缓存: 实时更新 (WebSocket)
2025-07-30 18:00:40.151 [INFO] [ExecutionEngine]    - 对冲质量缓存: 10秒TTL (预检查)
2025-07-30 18:00:40.151 [INFO] [ExecutionEngine] 📈 缓存命中率: 100.0% (总条目: 0)
2025-07-30 18:00:40.151 [INFO] [ExecutionEngine] 🎯 统一对冲质量检查: 比例=0.9926 (✅通过)
2025-07-30 18:00:40.151 [INFO] [ExecutionEngine] ✅ 执行步骤B.1完成: 对冲质量检查通过
2025-07-30 18:00:40.151 [INFO] [ExecutionEngine] 🔍 执行步骤C.1: 开始极速并行执行...
2025-07-30 18:00:40.151 [DEBUG] [ExecutionEngine] 🔥 开始<30ms极速并行执行: 现货(gate) + 期货(bybit) ICNT-USDT
2025-07-30 18:00:40.152 [INFO] [ExecutionEngine] 🔧 统一金额计算: $35.0 ÷ 0.228300 = 153.307052 代币
2025-07-30 18:00:40.154 [INFO] [ExecutionEngine] 🔥 智能协调开始: 原始数量=153.30705200币
2025-07-30 18:00:40.154 [INFO] [ExecutionEngine] 🔥 bybit期货步长截取: 153.307052 → 153.307币
2025-07-30 18:00:40.154 [INFO] [ExecutionEngine] 🎯 智能协调结果: 现货=153.307000币, 期货=153.307000币
2025-07-30 18:00:40.154 [INFO] [ExecutionEngine] 📊 对冲质量检查已在TradingRulesPreloader中完成（包含完美对冲逻辑）
2025-07-30 18:00:40.154 [INFO] [ExecutionEngine] ✅ 智能协调完成: 现货=153.307000币, 期货=153.307000币
2025-07-30 18:00:40.155 [INFO] [ExecutionEngine] 🔥 统一单位：返回币数量，各交易所在下单时自行转换
2025-07-30 18:00:40.155 [INFO] [ExecutionEngine] ✅ 统一架构：数量协调完成，精度转换由各交易所统一处理
2025-07-30 18:00:40.155 [INFO] [ExecutionEngine]    协调后数量: 现货=153.30700000, 期货=153.30700000
2025-07-30 18:00:40.155 [INFO] [ExecutionEngine]    对冲质量检查已在预检查阶段完成，符合MD文件统一计算要求
2025-07-30 18:00:40.155 [DEBUG] [ExecutionEngine] ⚡ 零延迟参数准备: 3.47ms
2025-07-30 18:00:40.215 [INFO] [ExecutionEngine] ✅ 开仓验证：数据快照验证通过，使用快照数据确保一致性
2025-07-30 18:00:40.216 [DEBUG] [ExecutionEngine] ✅ 快照详情: 快照降级使用: 2640.0ms > 800ms (标准阈值)
2025-07-30 18:00:40.216 [DEBUG] [ExecutionEngine] ✅ 使用数据快照，跳过新鲜度检查
2025-07-30 18:00:40.216 [DEBUG] [ExecutionEngine] ✅ 使用快照中的差价计算结果: 0.745%
2025-07-30 18:00:40.216 [DEBUG] [ExecutionEngine] ✅ 开仓验证通过: 当前价差0.745%
2025-07-30 18:00:40.216 [DEBUG] [ExecutionEngine] 🔍 [ORDERBOOK] 验证market_data结构:
2025-07-30 18:00:40.216 [DEBUG] [ExecutionEngine]    market_data类型: <class 'core.opportunity_scanner.MarketData'>
2025-07-30 18:00:40.216 [DEBUG] [ExecutionEngine]    是否有orderbook属性: True
2025-07-30 18:00:40.216 [DEBUG] [ExecutionEngine]    orderbook类型: <class 'dict'>
2025-07-30 18:00:40.216 [DEBUG] [ExecutionEngine]    orderbook是否为dict: True
2025-07-30 18:00:40.216 [DEBUG] [ExecutionEngine] 📊 [ORDERBOOK] gate_spot_ICNT-USDT - asks=10, bids=10
2025-07-30 18:00:40.216 [INFO] [ExecutionEngine] ✅ [ORDERBOOK] 成功获取订单簿: gate_spot_ICNT-USDT
2025-07-30 18:00:40.217 [INFO] [ExecutionEngine]    数据质量: asks=10档, bids=10档
2025-07-30 18:00:40.217 [DEBUG] [ExecutionEngine] 🔍 [ORDERBOOK] 验证market_data结构:
2025-07-30 18:00:40.217 [DEBUG] [ExecutionEngine]    market_data类型: <class 'core.opportunity_scanner.MarketData'>
2025-07-30 18:00:40.217 [DEBUG] [ExecutionEngine]    是否有orderbook属性: True
2025-07-30 18:00:40.217 [DEBUG] [ExecutionEngine]    orderbook类型: <class 'dict'>
2025-07-30 18:00:40.217 [DEBUG] [ExecutionEngine]    orderbook是否为dict: True
2025-07-30 18:00:40.217 [DEBUG] [ExecutionEngine] 📊 [ORDERBOOK] bybit_futures_ICNT-USDT - asks=30, bids=30
2025-07-30 18:00:40.217 [INFO] [ExecutionEngine] ✅ [ORDERBOOK] 成功获取订单簿: bybit_futures_ICNT-USDT
2025-07-30 18:00:40.217 [INFO] [ExecutionEngine]    数据质量: asks=30档, bids=30档
2025-07-30 18:00:40.364 [DEBUG] [ExecutionEngine] 🔄 订单簿同步验证失败，重试1/3: 订单簿数据非同步: 时间差2030.0ms > 800ms
2025-07-30 18:00:40.459 [DEBUG] [ExecutionEngine] 🔍 [ORDERBOOK] 验证market_data结构:
2025-07-30 18:00:40.460 [DEBUG] [ExecutionEngine]    market_data类型: <class 'core.opportunity_scanner.MarketData'>
2025-07-30 18:00:40.460 [DEBUG] [ExecutionEngine]    是否有orderbook属性: True
2025-07-30 18:00:40.460 [DEBUG] [ExecutionEngine]    orderbook类型: <class 'dict'>
2025-07-30 18:00:40.460 [DEBUG] [ExecutionEngine]    orderbook是否为dict: True
2025-07-30 18:00:40.460 [DEBUG] [ExecutionEngine] 📊 [ORDERBOOK] gate_spot_ICNT-USDT - asks=10, bids=10
2025-07-30 18:00:40.460 [INFO] [ExecutionEngine] ✅ [ORDERBOOK] 成功获取订单簿: gate_spot_ICNT-USDT
2025-07-30 18:00:40.460 [INFO] [ExecutionEngine]    数据质量: asks=10档, bids=10档
2025-07-30 18:00:40.461 [DEBUG] [ExecutionEngine] 🔍 [ORDERBOOK] 验证market_data结构:
2025-07-30 18:00:40.461 [DEBUG] [ExecutionEngine]    market_data类型: <class 'core.opportunity_scanner.MarketData'>
2025-07-30 18:00:40.461 [DEBUG] [ExecutionEngine]    是否有orderbook属性: True
2025-07-30 18:00:40.461 [DEBUG] [ExecutionEngine]    orderbook类型: <class 'dict'>
2025-07-30 18:00:40.461 [DEBUG] [ExecutionEngine]    orderbook是否为dict: True
2025-07-30 18:00:40.461 [DEBUG] [ExecutionEngine] 📊 [ORDERBOOK] bybit_futures_ICNT-USDT - asks=30, bids=30
2025-07-30 18:00:40.461 [INFO] [ExecutionEngine] ✅ [ORDERBOOK] 成功获取订单簿: bybit_futures_ICNT-USDT
2025-07-30 18:00:40.461 [INFO] [ExecutionEngine]    数据质量: asks=30档, bids=30档
2025-07-30 18:00:40.461 [INFO] [ExecutionEngine] ✅ 订单簿同步验证成功 (重试2次，阈值900ms)
2025-07-30 18:00:40.462 [INFO] [ExecutionEngine] ✅ 使用WebSocket实时订单簿数据，时间差：280.0ms，数据年龄：spot=452.0ms, futures=172.0ms
2025-07-30 18:00:40.462 [DEBUG] [ExecutionEngine] ⚡ WebSocket深度获取: 306.58ms
2025-07-30 18:00:40.462 [INFO] [ExecutionEngine] 📊 [ORDERBOOK] 分析套利深度数据: ICNT-USDT
2025-07-30 18:00:40.462 [INFO] [ExecutionEngine]    现货深度: asks=10档, bids=10档
2025-07-30 18:00:40.462 [INFO] [ExecutionEngine]    期货深度: asks=30档, bids=30档
2025-07-30 18:00:40.462 [INFO] [ExecutionEngine] ✅ [ORDERBOOK] 套利数据可用: ICNT-USDT
2025-07-30 18:00:40.462 [INFO] [ExecutionEngine]    买现货卖期货: 现货asks=10, 期货bids=30
2025-07-30 18:00:40.467 [DEBUG] [ExecutionEngine] ✅ 执行前验证通过: ICNT-USDT 当前价差0.745% (验证耗时0.0ms)
2025-07-30 18:00:40.469 [INFO] [ExecutionEngine] ✅ 30档Order差价分析: 可执行价差0.745%, 滑点0.000%, 使用档位: 现货1, 期货1
2025-07-30 18:00:40.471 [INFO] [ExecutionEngine] ✅ 独立滑点风险控制通过: 总滑点0.000% 在可接受范围内
2025-07-30 18:00:40.472 [DEBUG] [ExecutionEngine] ⚡ 快速验证: 10.17ms
2025-07-30 18:00:40.472 [INFO] [ExecutionEngine] 🚀 启动并行差价锁定: 现货=153.30700000, 期货=153.30700000 (精度转换由各交易所统一处理)
2025-07-30 18:00:40.517 [DEBUG] [ExecutionEngine] 🔍 [ORDERBOOK] 验证market_data结构:
2025-07-30 18:00:40.518 [DEBUG] [ExecutionEngine]    market_data类型: <class 'core.opportunity_scanner.MarketData'>
2025-07-30 18:00:40.518 [DEBUG] [ExecutionEngine]    是否有orderbook属性: True
2025-07-30 18:00:40.518 [DEBUG] [ExecutionEngine]    orderbook类型: <class 'dict'>
2025-07-30 18:00:40.518 [DEBUG] [ExecutionEngine]    orderbook是否为dict: True
2025-07-30 18:00:40.518 [DEBUG] [ExecutionEngine] 📊 [ORDERBOOK] gate_spot_ICNT-USDT - asks=10, bids=10
2025-07-30 18:00:40.518 [INFO] [ExecutionEngine] ✅ [ORDERBOOK] 成功获取订单簿: gate_spot_ICNT-USDT
2025-07-30 18:00:40.518 [INFO] [ExecutionEngine]    数据质量: asks=10档, bids=10档
2025-07-30 18:00:40.518 [DEBUG] [ExecutionEngine] 🔍 [ORDERBOOK] 验证market_data结构:
2025-07-30 18:00:40.518 [DEBUG] [ExecutionEngine]    market_data类型: <class 'core.opportunity_scanner.MarketData'>
2025-07-30 18:00:40.518 [DEBUG] [ExecutionEngine]    是否有orderbook属性: True
2025-07-30 18:00:40.518 [DEBUG] [ExecutionEngine]    orderbook类型: <class 'dict'>
2025-07-30 18:00:40.519 [DEBUG] [ExecutionEngine]    orderbook是否为dict: True
2025-07-30 18:00:40.519 [DEBUG] [ExecutionEngine] 📊 [ORDERBOOK] bybit_futures_ICNT-USDT - asks=30, bids=30
2025-07-30 18:00:40.519 [INFO] [ExecutionEngine] ✅ [ORDERBOOK] 成功获取订单簿: bybit_futures_ICNT-USDT
2025-07-30 18:00:40.519 [INFO] [ExecutionEngine]    数据质量: asks=30档, bids=30档
2025-07-30 18:00:40.519 [INFO] [ExecutionEngine] 🔍 现货执行: gate ICNT-USDT 153.307
2025-07-30 18:00:40.519 [DEBUG] [ExecutionEngine] ✅ 使用预取的现货深度数据: ICNT-USDT
2025-07-30 18:00:40.519 [INFO] [ExecutionEngine] ✅ [ORDERBOOK] 现货数据可用: ICNT-USDT - asks=10, bids=10
2025-07-30 18:00:40.522 [INFO] [ExecutionEngine] 🔍 期货执行: bybit ICNT-USDT 153.307
2025-07-30 18:00:40.626 [INFO] [ExecutionEngine] 🔧 设置bybit杠杆: ICNT-USDT
2025-07-30 18:00:40.626 [INFO] [ExecutionEngine] ✅ 杠杆设置成功: bybit ICNT-USDT = 3x (缓存命中, 0.3ms)
2025-07-30 18:00:40.726 [INFO] [ExecutionEngine] ✅ [ORDERBOOK] 期货数据可用: ICNT-USDT - asks=30, bids=30
2025-07-30 18:00:41.166 [ERROR] [ExecutionEngine] ❌ 期货执行失败: OpeningResult(success=False, order_id=None, executed_quantity=0.0, executed_price=0.0, error_message="开仓失败: 无效的订单结果 - {'id': '', 'status': 'failed', 'error': 'Bybit API错误: 10001: Qty invalid'}", execution_time_ms=439.53847885131836, params_used=OpeningOrderParams(symbol='ICNT-USDT', side='sell', order_type='market', quantity='153.307', price=None, market_type='futures', original_quantity=153.307, original_price=None, step_size='0.001', price_step='0.01', exchange_name='bybit'))
2025-07-30 18:00:43.773 [INFO] [ExecutionEngine] ✅ 现货执行成功: OpeningResult(success=True, order_id='886613082958', executed_quantity=153.307, executed_price=0.2283, error_message=None, execution_time_ms=3252.580404281616, params_used=OpeningOrderParams(symbol='ICNT-USDT', side='buy', order_type='market', quantity='153.3070', price=None, market_type='spot', original_quantity=153.307, original_price=None, step_size='0.0001', price_step='0.01', exchange_name='gate'))
2025-07-30 18:00:43.850 [INFO] [ExecutionEngine] ⚡ 并行交易执行: 3377.71ms
2025-07-30 18:00:43.850 [INFO] [ExecutionEngine] 🎯 总执行时间: 3698.43ms (目标: <30ms)
2025-07-30 18:00:43.850 [WARNING] [ExecutionEngine] ⚠️ 超过30ms目标: 3698.43ms
2025-07-30 18:00:43.850 [ERROR] [ExecutionEngine] 🚨 现货成功但期货失败，执行紧急平仓现货仓位
2025-07-30 18:00:45.725 [ERROR] [ExecutionEngine] 🚨 开始紧急平仓现货仓位: ICNT-USDT
2025-07-30 18:00:45.725 [INFO] [ExecutionEngine] 🔍 紧急平仓获取WebSocket现货深度数据: ICNT-USDT
2025-07-30 18:00:45.725 [DEBUG] [ExecutionEngine] 🔍 [ORDERBOOK] 验证market_data结构:
2025-07-30 18:00:45.725 [DEBUG] [ExecutionEngine]    market_data类型: <class 'core.opportunity_scanner.MarketData'>
2025-07-30 18:00:45.725 [DEBUG] [ExecutionEngine]    是否有orderbook属性: True
2025-07-30 18:00:45.725 [DEBUG] [ExecutionEngine]    orderbook类型: <class 'dict'>
2025-07-30 18:00:45.725 [DEBUG] [ExecutionEngine]    orderbook是否为dict: True
2025-07-30 18:00:45.726 [DEBUG] [ExecutionEngine] 📊 [ORDERBOOK] gate_spot_ICNT-USDT - asks=10, bids=10
2025-07-30 18:00:45.726 [INFO] [ExecutionEngine] ✅ [ORDERBOOK] 成功获取订单簿: gate_spot_ICNT-USDT
2025-07-30 18:00:45.726 [INFO] [ExecutionEngine]    数据质量: asks=10档, bids=10档
2025-07-30 18:00:45.726 [INFO] [ExecutionEngine] ✅ 紧急平仓成功获取WebSocket深度数据: asks=10, bids=10
2025-07-30 18:00:45.726 [INFO] [ExecutionEngine] 🔍 等待现货订单完全成交: 886613082958
2025-07-30 18:00:47.570 [INFO] [ExecutionEngine] ✅ 现货订单已完全成交: 886613082958
2025-07-30 18:00:47.570 [INFO] [ExecutionEngine] ✅ 现货订单等待完成，开始紧急平仓
2025-07-30 18:00:47.570 [INFO] [ExecutionEngine] 🔧 通用安全平仓数量调整:
2025-07-30 18:00:47.570 [INFO] [ExecutionEngine]    买入数量: 153.30700000
2025-07-30 18:00:47.571 [INFO] [ExecutionEngine]    安全数量: 15.33070000
2025-07-30 18:00:47.571 [INFO] [ExecutionEngine]    使用方法: ExchangeParamAdapter.calculate_safe_amount
2025-07-30 18:00:50.725 [INFO] [ExecutionEngine] ✅ 紧急平仓现货成功: 886613122257
2025-07-30 18:00:50.725 [INFO] [ExecutionEngine] 🔥 并行执行结果: ❌失败 (总耗时: 3698.43ms)
2025-07-30 18:00:50.725 [INFO] [ExecutionEngine] 🎯 执行步骤C.1完成: 并行执行耗时 10574.06ms
2025-07-30 18:00:50.725 [ERROR] [ExecutionEngine] ❌ 执行步骤C.1失败: 并行执行失败，结束套利: ICNT-USDT
2025-07-30 18:00:50.726 [INFO] [ExecutionEngine] ✅ 执行失败，并行控制槽位已释放: ICNT-USDT
2025-07-30 18:00:51.770 [INFO] [ExecutionEngine] 🔧 重置ExecutionEngine状态...
2025-07-30 18:00:51.770 [WARNING] [ExecutionEngine] ⚠️ ExecutionEngine健康检查失败，执行强制重置
2025-07-30 18:00:51.770 [DEBUG] [ExecutionEngine] ✅ 执行锁已释放
2025-07-30 18:00:51.771 [WARNING] [ExecutionEngine] ⚠️ ExecutionEngine状态重置完成，但系统恢复验证失败
2025-07-30 18:01:14.442 [INFO] [ExecutionEngine] ================================================================================
2025-07-30 18:01:14.442 [INFO] [ExecutionEngine] 🚀 【套利执行开始】 SPK-USDT
2025-07-30 18:01:14.442 [INFO] [ExecutionEngine]    现货交易所: bybit
2025-07-30 18:01:14.442 [INFO] [ExecutionEngine]    期货交易所: gate
2025-07-30 18:01:14.442 [INFO] [ExecutionEngine]    基础数量: 323.18524370231285
2025-07-30 18:01:14.442 [INFO] [ExecutionEngine]    价差百分比: 0.64%
2025-07-30 18:01:14.442 [INFO] [ExecutionEngine] ================================================================================
2025-07-30 18:01:14.442 [INFO] [ExecutionEngine] 📊 EXECUTION_START | SPK-USDT | bybit -> gate | 差价=0.64%
2025-07-30 18:01:14.443 [INFO] [ExecutionEngine] 🔍 执行步骤A.0: 并行控制检查...
2025-07-30 18:01:14.443 [INFO] [ExecutionEngine] ✅ 并行控制检查通过，订单编号: ARB_SPK-USDT_1753891274
2025-07-30 18:01:14.443 [INFO] [ExecutionEngine] 🔍 执行步骤A.1: 更新执行状态...
2025-07-30 18:01:14.443 [INFO] [ExecutionEngine] ✅ 执行状态已更新为EXECUTING
2025-07-30 18:01:14.443 [INFO] [ExecutionEngine] 🔍 执行步骤A.2: 创建执行结果对象...
2025-07-30 18:01:14.443 [INFO] [ExecutionEngine] ✅ 执行结果对象创建完成
2025-07-30 18:01:14.443 [INFO] [ExecutionEngine] 🔍 执行步骤B.1: 开始98%对冲质量预检查...
2025-07-30 18:01:14.443 [INFO] [ExecutionEngine] 📊 6大缓存系统使用统计:
2025-07-30 18:01:14.443 [INFO] [ExecutionEngine]    - 余额缓存: 实时更新 (ArbitrageEngine)
2025-07-30 18:01:14.444 [INFO] [ExecutionEngine]    - 保证金缓存: 5分钟TTL (MarginCalculator)
2025-07-30 18:01:14.444 [INFO] [ExecutionEngine]    - 交易规则缓存: N/A个规则 (TradingRulesPreloader)
2025-07-30 18:01:14.444 [INFO] [ExecutionEngine]    - 精度缓存: 24小时TTL (TradingRulesPreloader)
2025-07-30 18:01:14.444 [INFO] [ExecutionEngine]    - 订单簿缓存: 实时更新 (WebSocket)
2025-07-30 18:01:14.444 [INFO] [ExecutionEngine]    - 对冲质量缓存: 10秒TTL (预检查)
2025-07-30 18:01:14.444 [INFO] [ExecutionEngine] 📈 缓存命中率: 100.0% (总条目: 0)
2025-07-30 18:01:14.444 [INFO] [ExecutionEngine] 🎯 统一对冲质量检查: 比例=0.9936 (✅通过)
2025-07-30 18:01:14.444 [INFO] [ExecutionEngine] ✅ 执行步骤B.1完成: 对冲质量检查通过
2025-07-30 18:01:14.444 [INFO] [ExecutionEngine] 🔍 执行步骤C.1: 开始极速并行执行...
2025-07-30 18:01:14.445 [DEBUG] [ExecutionEngine] 🔥 开始<30ms极速并行执行: 现货(bybit) + 期货(gate) SPK-USDT
2025-07-30 18:01:14.445 [INFO] [ExecutionEngine] 🔧 统一金额计算: $35.0 ÷ 0.108297 = 323.185243 代币
2025-07-30 18:01:14.445 [INFO] [ExecutionEngine] 🔥 智能协调开始: 原始数量=323.18524300币
2025-07-30 18:01:14.445 [INFO] [ExecutionEngine] 🔥 Gate期货整数截取: 323.185243 → 323币
2025-07-30 18:01:14.445 [INFO] [ExecutionEngine] 🎯 智能协调结果: 现货=323.185000币, 期货=323.000000币
2025-07-30 18:01:14.445 [INFO] [ExecutionEngine] 📊 对冲质量检查已在TradingRulesPreloader中完成（包含完美对冲逻辑）
2025-07-30 18:01:14.445 [INFO] [ExecutionEngine] ✅ 智能协调完成: 现货=323.185000币, 期货=323.000000币
2025-07-30 18:01:14.445 [INFO] [ExecutionEngine] 🔥 统一单位：返回币数量，各交易所在下单时自行转换
2025-07-30 18:01:14.446 [INFO] [ExecutionEngine] ✅ 统一架构：数量协调完成，精度转换由各交易所统一处理
2025-07-30 18:01:14.446 [INFO] [ExecutionEngine]    协调后数量: 现货=323.18500000, 期货=323.00000000
2025-07-30 18:01:14.446 [INFO] [ExecutionEngine]    对冲质量检查已在预检查阶段完成，符合MD文件统一计算要求
2025-07-30 18:01:14.446 [DEBUG] [ExecutionEngine] ⚡ 零延迟参数准备: 1.15ms
2025-07-30 18:01:14.483 [INFO] [ExecutionEngine] ✅ 开仓验证：数据快照验证通过，使用快照数据确保一致性
2025-07-30 18:01:14.484 [DEBUG] [ExecutionEngine] ✅ 快照详情: 快照降级使用: 5460.0ms > 800ms (标准阈值)
2025-07-30 18:01:14.484 [DEBUG] [ExecutionEngine] ✅ 使用数据快照，跳过新鲜度检查
2025-07-30 18:01:14.484 [DEBUG] [ExecutionEngine] ✅ 使用快照中的差价计算结果: 0.640%
2025-07-30 18:01:14.484 [DEBUG] [ExecutionEngine] ✅ 开仓验证通过: 当前价差0.640%
2025-07-30 18:01:14.484 [DEBUG] [ExecutionEngine] 🔍 [ORDERBOOK] 验证market_data结构:
2025-07-30 18:01:14.486 [DEBUG] [ExecutionEngine]    market_data类型: <class 'core.opportunity_scanner.MarketData'>
2025-07-30 18:01:14.486 [DEBUG] [ExecutionEngine]    是否有orderbook属性: True
2025-07-30 18:01:14.486 [DEBUG] [ExecutionEngine]    orderbook类型: <class 'dict'>
2025-07-30 18:01:14.486 [DEBUG] [ExecutionEngine]    orderbook是否为dict: True
2025-07-30 18:01:14.487 [DEBUG] [ExecutionEngine] 📊 [ORDERBOOK] bybit_spot_SPK-USDT - asks=30, bids=30
2025-07-30 18:01:14.487 [INFO] [ExecutionEngine] ✅ [ORDERBOOK] 成功获取订单簿: bybit_spot_SPK-USDT
2025-07-30 18:01:14.487 [INFO] [ExecutionEngine]    数据质量: asks=30档, bids=30档
2025-07-30 18:01:14.487 [DEBUG] [ExecutionEngine] 🔍 [ORDERBOOK] 验证market_data结构:
2025-07-30 18:01:14.487 [DEBUG] [ExecutionEngine]    market_data类型: <class 'core.opportunity_scanner.MarketData'>
2025-07-30 18:01:14.487 [DEBUG] [ExecutionEngine]    是否有orderbook属性: True
2025-07-30 18:01:14.487 [DEBUG] [ExecutionEngine]    orderbook类型: <class 'dict'>
2025-07-30 18:01:14.488 [DEBUG] [ExecutionEngine]    orderbook是否为dict: True
2025-07-30 18:01:14.488 [DEBUG] [ExecutionEngine] 📊 [ORDERBOOK] gate_futures_SPK-USDT - asks=20, bids=20
2025-07-30 18:01:14.488 [INFO] [ExecutionEngine] ✅ [ORDERBOOK] 成功获取订单簿: gate_futures_SPK-USDT
2025-07-30 18:01:14.488 [INFO] [ExecutionEngine]    数据质量: asks=20档, bids=20档
2025-07-30 18:01:14.606 [INFO] [ExecutionEngine] ✅ 使用WebSocket实时订单簿数据，时间差：290.0ms，数据年龄：spot=476.9ms, futures=186.9ms
2025-07-30 18:01:14.607 [DEBUG] [ExecutionEngine] ⚡ WebSocket深度获取: 160.54ms
2025-07-30 18:01:14.607 [INFO] [ExecutionEngine] 📊 [ORDERBOOK] 分析套利深度数据: SPK-USDT
2025-07-30 18:01:14.607 [INFO] [ExecutionEngine]    现货深度: asks=30档, bids=30档
2025-07-30 18:01:14.607 [INFO] [ExecutionEngine]    期货深度: asks=20档, bids=20档
2025-07-30 18:01:14.607 [INFO] [ExecutionEngine] ✅ [ORDERBOOK] 套利数据可用: SPK-USDT
2025-07-30 18:01:14.607 [INFO] [ExecutionEngine]    买现货卖期货: 现货asks=30, 期货bids=20
2025-07-30 18:01:14.608 [DEBUG] [ExecutionEngine] ✅ 执行前验证通过: SPK-USDT 当前价差0.640% (验证耗时0.0ms)
2025-07-30 18:01:14.608 [INFO] [ExecutionEngine] ✅ 30档Order差价分析: 可执行价差1.129%, 滑点0.108%, 使用档位: 现货2, 期货8
2025-07-30 18:01:14.608 [WARNING] [ExecutionEngine] 🚫 独立滑点风险控制拒绝交易: 总滑点0.108% 超过阈值
2025-07-30 18:01:14.608 [WARNING] [ExecutionEngine] 🚫 风险控制决策: 超过滑点阈值，拒绝交易而非修改执行价格
2025-07-30 18:01:14.609 [INFO] [ExecutionEngine] 🎯 执行步骤C.1完成: 并行执行耗时 163.95ms
2025-07-30 18:01:14.609 [ERROR] [ExecutionEngine] ❌ 执行步骤C.1失败: 并行执行失败，结束套利: SPK-USDT
2025-07-30 18:01:14.609 [INFO] [ExecutionEngine] ✅ 执行失败，并行控制槽位已释放: SPK-USDT
2025-07-30 18:01:15.683 [INFO] [ExecutionEngine] 🔧 重置ExecutionEngine状态...
2025-07-30 18:01:15.684 [WARNING] [ExecutionEngine] ⚠️ ExecutionEngine健康检查失败，执行强制重置
2025-07-30 18:01:15.684 [DEBUG] [ExecutionEngine] ✅ 执行锁已释放
2025-07-30 18:01:15.684 [WARNING] [ExecutionEngine] ⚠️ ExecutionEngine状态重置完成，但系统恢复验证失败
2025-07-30 18:01:37.854 [INFO] [ExecutionEngine] ================================================================================
2025-07-30 18:01:37.854 [INFO] [ExecutionEngine] 🚀 【套利执行开始】 SPK-USDT
2025-07-30 18:01:37.854 [INFO] [ExecutionEngine]    现货交易所: bybit
2025-07-30 18:01:37.854 [INFO] [ExecutionEngine]    期货交易所: okx
2025-07-30 18:01:37.854 [INFO] [ExecutionEngine]    基础数量: 321.99506459624206
2025-07-30 18:01:37.855 [INFO] [ExecutionEngine]    价差百分比: 0.74%
2025-07-30 18:01:37.855 [INFO] [ExecutionEngine] ================================================================================
2025-07-30 18:01:37.855 [INFO] [ExecutionEngine] 📊 EXECUTION_START | SPK-USDT | bybit -> okx | 差价=0.74%
2025-07-30 18:01:37.855 [INFO] [ExecutionEngine] 🔍 执行步骤A.0: 并行控制检查...
2025-07-30 18:01:37.855 [INFO] [ExecutionEngine] ✅ 并行控制检查通过，订单编号: ARB_SPK-USDT_1753891297
2025-07-30 18:01:37.855 [INFO] [ExecutionEngine] 🔍 执行步骤A.1: 更新执行状态...
2025-07-30 18:01:37.855 [INFO] [ExecutionEngine] ✅ 执行状态已更新为EXECUTING
2025-07-30 18:01:37.855 [INFO] [ExecutionEngine] 🔍 执行步骤A.2: 创建执行结果对象...
2025-07-30 18:01:37.855 [INFO] [ExecutionEngine] ✅ 执行结果对象创建完成
2025-07-30 18:01:37.856 [INFO] [ExecutionEngine] 🔍 执行步骤B.1: 开始98%对冲质量预检查...
2025-07-30 18:01:37.856 [INFO] [ExecutionEngine] 📊 6大缓存系统使用统计:
2025-07-30 18:01:37.856 [INFO] [ExecutionEngine]    - 余额缓存: 实时更新 (ArbitrageEngine)
2025-07-30 18:01:37.856 [INFO] [ExecutionEngine]    - 保证金缓存: 5分钟TTL (MarginCalculator)
2025-07-30 18:01:37.856 [INFO] [ExecutionEngine]    - 交易规则缓存: N/A个规则 (TradingRulesPreloader)
2025-07-30 18:01:37.856 [INFO] [ExecutionEngine]    - 精度缓存: 24小时TTL (TradingRulesPreloader)
2025-07-30 18:01:37.856 [INFO] [ExecutionEngine]    - 订单簿缓存: 实时更新 (WebSocket)
2025-07-30 18:01:37.856 [INFO] [ExecutionEngine]    - 对冲质量缓存: 10秒TTL (预检查)
2025-07-30 18:01:37.856 [INFO] [ExecutionEngine] 📈 缓存命中率: 100.0% (总条目: 0)
2025-07-30 18:01:37.856 [INFO] [ExecutionEngine] 🎯 统一对冲质量检查: 比例=0.9926 (✅通过)
2025-07-30 18:01:37.856 [INFO] [ExecutionEngine] ✅ 执行步骤B.1完成: 对冲质量检查通过
2025-07-30 18:01:37.856 [INFO] [ExecutionEngine] 🔍 执行步骤C.1: 开始极速并行执行...
2025-07-30 18:01:37.857 [DEBUG] [ExecutionEngine] 🔥 开始<30ms极速并行执行: 现货(bybit) + 期货(okx) SPK-USDT
2025-07-30 18:01:37.857 [INFO] [ExecutionEngine] 🔧 统一金额计算: $35.0 ÷ 0.108697 = 321.995064 代币
2025-07-30 18:01:37.857 [INFO] [ExecutionEngine] 🔥 智能协调开始: 原始数量=321.99506400币
2025-07-30 18:01:37.857 [INFO] [ExecutionEngine] 🔥 okx期货步长截取: 321.995064 → 321.99506币
2025-07-30 18:01:37.857 [INFO] [ExecutionEngine] 🎯 智能协调结果: 现货=321.995000币, 期货=321.995060币
2025-07-30 18:01:37.857 [INFO] [ExecutionEngine] 📊 对冲质量检查已在TradingRulesPreloader中完成（包含完美对冲逻辑）
2025-07-30 18:01:37.857 [INFO] [ExecutionEngine] ✅ 智能协调完成: 现货=321.995000币, 期货=321.995060币
2025-07-30 18:01:37.857 [INFO] [ExecutionEngine] 🔥 统一单位：返回币数量，各交易所在下单时自行转换
2025-07-30 18:01:37.858 [INFO] [ExecutionEngine] ✅ 统一架构：数量协调完成，精度转换由各交易所统一处理
2025-07-30 18:01:37.858 [INFO] [ExecutionEngine]    协调后数量: 现货=321.99500000, 期货=321.99506000
2025-07-30 18:01:37.858 [INFO] [ExecutionEngine]    对冲质量检查已在预检查阶段完成，符合MD文件统一计算要求
2025-07-30 18:01:37.858 [DEBUG] [ExecutionEngine] ⚡ 零延迟参数准备: 1.07ms
2025-07-30 18:01:37.877 [INFO] [ExecutionEngine] ✅ 开仓验证：数据快照验证通过，使用快照数据确保一致性
2025-07-30 18:01:37.878 [DEBUG] [ExecutionEngine] ✅ 快照详情: 快照降级使用: 1660.0ms > 800ms (标准阈值)
2025-07-30 18:01:37.878 [DEBUG] [ExecutionEngine] ✅ 使用数据快照，跳过新鲜度检查
2025-07-30 18:01:37.878 [DEBUG] [ExecutionEngine] ✅ 使用快照中的差价计算结果: 0.744%
2025-07-30 18:01:37.878 [DEBUG] [ExecutionEngine] ✅ 开仓验证通过: 当前价差0.744%
2025-07-30 18:01:37.878 [DEBUG] [ExecutionEngine] 🔍 [ORDERBOOK] 验证market_data结构:
2025-07-30 18:01:37.878 [DEBUG] [ExecutionEngine]    market_data类型: <class 'core.opportunity_scanner.MarketData'>
2025-07-30 18:01:37.878 [DEBUG] [ExecutionEngine]    是否有orderbook属性: True
2025-07-30 18:01:37.878 [DEBUG] [ExecutionEngine]    orderbook类型: <class 'dict'>
2025-07-30 18:01:37.878 [DEBUG] [ExecutionEngine]    orderbook是否为dict: True
2025-07-30 18:01:37.879 [DEBUG] [ExecutionEngine] 📊 [ORDERBOOK] bybit_spot_SPK-USDT - asks=30, bids=30
2025-07-30 18:01:37.879 [INFO] [ExecutionEngine] ✅ [ORDERBOOK] 成功获取订单簿: bybit_spot_SPK-USDT
2025-07-30 18:01:37.879 [INFO] [ExecutionEngine]    数据质量: asks=30档, bids=30档
2025-07-30 18:01:37.879 [DEBUG] [ExecutionEngine] 🔍 [ORDERBOOK] 验证market_data结构:
2025-07-30 18:01:37.879 [DEBUG] [ExecutionEngine]    market_data类型: <class 'core.opportunity_scanner.MarketData'>
2025-07-30 18:01:37.879 [DEBUG] [ExecutionEngine]    是否有orderbook属性: True
2025-07-30 18:01:37.879 [DEBUG] [ExecutionEngine]    orderbook类型: <class 'dict'>
2025-07-30 18:01:37.879 [DEBUG] [ExecutionEngine]    orderbook是否为dict: True
2025-07-30 18:01:37.880 [DEBUG] [ExecutionEngine] 📊 [ORDERBOOK] okx_futures_SPK-USDT - asks=30, bids=30
2025-07-30 18:01:37.880 [INFO] [ExecutionEngine] ✅ [ORDERBOOK] 成功获取订单簿: okx_futures_SPK-USDT
2025-07-30 18:01:37.880 [INFO] [ExecutionEngine]    数据质量: asks=30档, bids=30档
2025-07-30 18:01:37.961 [INFO] [ExecutionEngine] ✅ 使用WebSocket实时订单簿数据，时间差：86.0ms，数据年龄：spot=271.0ms, futures=357.0ms
2025-07-30 18:01:37.961 [DEBUG] [ExecutionEngine] ⚡ WebSocket深度获取: 102.71ms
2025-07-30 18:01:37.961 [INFO] [ExecutionEngine] 📊 [ORDERBOOK] 分析套利深度数据: SPK-USDT
2025-07-30 18:01:37.961 [INFO] [ExecutionEngine]    现货深度: asks=30档, bids=30档
2025-07-30 18:01:37.961 [INFO] [ExecutionEngine]    期货深度: asks=30档, bids=30档
2025-07-30 18:01:37.961 [INFO] [ExecutionEngine] ✅ [ORDERBOOK] 套利数据可用: SPK-USDT
2025-07-30 18:01:37.961 [INFO] [ExecutionEngine]    买现货卖期货: 现货asks=30, 期货bids=30
2025-07-30 18:01:37.962 [DEBUG] [ExecutionEngine] ✅ 执行前验证通过: SPK-USDT 当前价差0.744% (验证耗时0.0ms)
2025-07-30 18:01:37.962 [INFO] [ExecutionEngine] ✅ 30档Order差价分析: 可执行价差0.638%, 滑点0.034%, 使用档位: 现货1, 期货6
2025-07-30 18:01:37.962 [INFO] [ExecutionEngine] ✅ 独立滑点风险控制通过: 总滑点0.034% 在可接受范围内
2025-07-30 18:01:37.963 [DEBUG] [ExecutionEngine] ⚡ 快速验证: 1.85ms
2025-07-30 18:01:37.963 [INFO] [ExecutionEngine] 🚀 启动并行差价锁定: 现货=321.99500000, 期货=321.99506000 (精度转换由各交易所统一处理)
2025-07-30 18:01:38.003 [DEBUG] [ExecutionEngine] 🔍 [ORDERBOOK] 验证market_data结构:
2025-07-30 18:01:38.003 [DEBUG] [ExecutionEngine]    market_data类型: <class 'core.opportunity_scanner.MarketData'>
2025-07-30 18:01:38.003 [DEBUG] [ExecutionEngine]    是否有orderbook属性: True
2025-07-30 18:01:38.003 [DEBUG] [ExecutionEngine]    orderbook类型: <class 'dict'>
2025-07-30 18:01:38.003 [DEBUG] [ExecutionEngine]    orderbook是否为dict: True
2025-07-30 18:01:38.003 [DEBUG] [ExecutionEngine] 📊 [ORDERBOOK] bybit_spot_SPK-USDT - asks=30, bids=30
2025-07-30 18:01:38.003 [INFO] [ExecutionEngine] ✅ [ORDERBOOK] 成功获取订单簿: bybit_spot_SPK-USDT
2025-07-30 18:01:38.004 [INFO] [ExecutionEngine]    数据质量: asks=30档, bids=30档
2025-07-30 18:01:38.004 [DEBUG] [ExecutionEngine] 🔍 [ORDERBOOK] 验证market_data结构:
2025-07-30 18:01:38.004 [DEBUG] [ExecutionEngine]    market_data类型: <class 'core.opportunity_scanner.MarketData'>
2025-07-30 18:01:38.004 [DEBUG] [ExecutionEngine]    是否有orderbook属性: True
2025-07-30 18:01:38.004 [DEBUG] [ExecutionEngine]    orderbook类型: <class 'dict'>
2025-07-30 18:01:38.004 [DEBUG] [ExecutionEngine]    orderbook是否为dict: True
2025-07-30 18:01:38.004 [DEBUG] [ExecutionEngine] 📊 [ORDERBOOK] okx_futures_SPK-USDT - asks=30, bids=30
2025-07-30 18:01:38.004 [INFO] [ExecutionEngine] ✅ [ORDERBOOK] 成功获取订单簿: okx_futures_SPK-USDT
2025-07-30 18:01:38.004 [INFO] [ExecutionEngine]    数据质量: asks=30档, bids=30档
2025-07-30 18:01:38.004 [INFO] [ExecutionEngine] 🔍 现货执行: bybit SPK-USDT 321.995
2025-07-30 18:01:38.005 [DEBUG] [ExecutionEngine] ✅ 使用预取的现货深度数据: SPK-USDT
2025-07-30 18:01:38.005 [INFO] [ExecutionEngine] ✅ [ORDERBOOK] 现货数据可用: SPK-USDT - asks=30, bids=30
2025-07-30 18:01:38.007 [INFO] [ExecutionEngine] 🔍 期货执行: okx SPK-USDT 321.99506
2025-07-30 18:01:38.017 [INFO] [ExecutionEngine] 🔧 设置okx杠杆: SPK-USDT
2025-07-30 18:01:38.272 [ERROR] [ExecutionEngine] ❌ 现货执行失败: OpeningResult(success=False, order_id=None, executed_quantity=0.0, executed_price=0.0, error_message="开仓失败: 无效的订单结果 - {'id': '', 'status': 'failed', 'error': 'Bybit API错误: 170137: Order quantity has too many decimals.'}", execution_time_ms=267.10033416748047, params_used=OpeningOrderParams(symbol='SPK-USDT', side='buy', order_type='market', quantity='321.995', price=None, market_type='spot', original_quantity=321.995, original_price=None, step_size='0.001', price_step='0.01', exchange_name='bybit'))
2025-07-30 18:01:38.646 [INFO] [ExecutionEngine] ✅ 杠杆设置成功: okx SPK-USDT = 3x (API调用, 628.9ms)
2025-07-30 18:01:38.740 [INFO] [ExecutionEngine] ✅ [ORDERBOOK] 期货数据可用: SPK-USDT - asks=30, bids=30
2025-07-30 18:01:40.862 [INFO] [ExecutionEngine] ✅ 期货执行成功: OpeningResult(success=True, order_id='2730958318718476288', executed_quantity=321.99506, executed_price=0.1093603678929766, error_message=None, execution_time_ms=2120.9511756896973, params_used=OpeningOrderParams(symbol='SPK-USDT', side='sell', order_type='market', quantity='321.99506', price=None, market_type='futures', original_quantity=321.99506, original_price=None, step_size='0.00001', price_step='0.01', exchange_name='okx'))
2025-07-30 18:01:40.927 [INFO] [ExecutionEngine] ⚡ 并行交易执行: 2963.97ms
2025-07-30 18:01:40.927 [INFO] [ExecutionEngine] 🎯 总执行时间: 3070.18ms (目标: <30ms)
2025-07-30 18:01:40.927 [WARNING] [ExecutionEngine] ⚠️ 超过30ms目标: 3070.18ms
2025-07-30 18:01:40.927 [ERROR] [ExecutionEngine] 🚨 期货成功但现货失败，执行紧急平仓期货仓位
2025-07-30 18:01:42.714 [ERROR] [ExecutionEngine] 🚨 开始紧急平仓期货仓位: SPK-USDT
2025-07-30 18:01:44.462 [INFO] [ExecutionEngine] ✅ 紧急平仓期货成功: 2730958439547985920
2025-07-30 18:01:44.462 [INFO] [ExecutionEngine] 🔥 并行执行结果: ❌失败 (总耗时: 3070.18ms)
2025-07-30 18:01:44.462 [INFO] [ExecutionEngine] 🎯 执行步骤C.1完成: 并行执行耗时 6605.26ms
2025-07-30 18:01:44.462 [ERROR] [ExecutionEngine] ❌ 执行步骤C.1失败: 并行执行失败，结束套利: SPK-USDT
2025-07-30 18:01:44.462 [INFO] [ExecutionEngine] ✅ 执行失败，并行控制槽位已释放: SPK-USDT
2025-07-30 18:01:45.521 [INFO] [ExecutionEngine] 🔧 重置ExecutionEngine状态...
2025-07-30 18:01:45.521 [WARNING] [ExecutionEngine] ⚠️ ExecutionEngine健康检查失败，执行强制重置
2025-07-30 18:01:45.521 [DEBUG] [ExecutionEngine] ✅ 执行锁已释放
2025-07-30 18:01:45.521 [WARNING] [ExecutionEngine] ⚠️ ExecutionEngine状态重置完成，但系统恢复验证失败
