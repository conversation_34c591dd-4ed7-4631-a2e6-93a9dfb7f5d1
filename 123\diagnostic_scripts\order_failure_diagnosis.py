#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔥 订单失败精准诊断脚本
专门分析ICNT-USDT期货'Qty invalid'和SPK-USDT现货'Order quantity has too many decimals'错误
"""

import sys
import os
import json
import asyncio
import time
from decimal import Decimal, ROUND_DOWN
from typing import Dict, Any, Optional

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class OrderFailureDiagnosis:
    def __init__(self):
        self.results = {
            "diagnosis_time": time.strftime("%Y-%m-%d %H:%M:%S"),
            "test_cases": [],
            "root_causes": [],
            "recommendations": []
        }
        
    def analyze_icnt_usdt_futures_error(self):
        """分析ICNT-USDT期货'Qty invalid'错误"""
        print("🔍 分析ICNT-USDT期货'Qty invalid'错误...")
        
        test_case = {
            "symbol": "ICNT-USDT",
            "market_type": "futures",
            "exchange": "bybit",
            "original_quantity": 153.307,
            "error": "Bybit API错误: 10001: Qty invalid",
            "analysis": {}
        }
        
        # 分析数量精度问题
        quantity = 153.307
        
        # 检查常见的step_size值
        common_step_sizes = [0.001, 0.01, 0.1, 1.0, 10.0]
        
        for step_size in common_step_sizes:
            # 使用Decimal进行精确计算
            qty_decimal = Decimal(str(quantity))
            step_decimal = Decimal(str(step_size))
            
            # 检查是否是步长的整数倍
            remainder = qty_decimal % step_decimal
            is_valid = remainder == 0
            
            # 如果不是整数倍，计算正确的截取值
            if not is_valid:
                truncated = (qty_decimal // step_decimal) * step_decimal
                test_case["analysis"][f"step_size_{step_size}"] = {
                    "is_valid": False,
                    "remainder": float(remainder),
                    "should_be": float(truncated),
                    "difference": float(qty_decimal - truncated)
                }
            else:
                test_case["analysis"][f"step_size_{step_size}"] = {
                    "is_valid": True,
                    "remainder": 0.0
                }
        
        # 检查小数位数
        qty_str = str(quantity)
        if '.' in qty_str:
            decimal_places = len(qty_str.split('.')[1])
            test_case["analysis"]["decimal_places"] = decimal_places
            test_case["analysis"]["decimal_places_issue"] = decimal_places > 6
        
        # 检查是否为整数
        test_case["analysis"]["is_integer"] = quantity == int(quantity)
        
        self.results["test_cases"].append(test_case)
        
        # 根本原因分析
        if not any(analysis.get("is_valid", False) for analysis in test_case["analysis"].values() if isinstance(analysis, dict)):
            self.results["root_causes"].append({
                "symbol": "ICNT-USDT",
                "issue": "数量153.307不符合任何常见的step_size要求",
                "likely_cause": "step_size获取错误或数量计算错误"
            })
    
    def analyze_spk_usdt_spot_error(self):
        """分析SPK-USDT现货'Order quantity has too many decimals'错误"""
        print("🔍 分析SPK-USDT现货'Order quantity has too many decimals'错误...")
        
        test_case = {
            "symbol": "SPK-USDT",
            "market_type": "spot",
            "exchange": "bybit",
            "original_quantity": 321.995,
            "error": "Bybit API错误: 170137: Order quantity has too many decimals.",
            "analysis": {}
        }
        
        quantity = 321.995
        
        # 分析小数位数问题
        qty_str = str(quantity)
        if '.' in qty_str:
            decimal_places = len(qty_str.split('.')[1])
            test_case["analysis"]["decimal_places"] = decimal_places
            test_case["analysis"]["decimal_places_issue"] = decimal_places > 6
            
            # 测试不同精度的截取
            for precision in range(1, 7):
                truncated = float(Decimal(str(quantity)).quantize(Decimal('0.' + '0' * precision), rounding=ROUND_DOWN))
                test_case["analysis"][f"precision_{precision}"] = {
                    "truncated_value": truncated,
                    "decimal_places": precision
                }
        
        # 检查常见的basePrecision值（Bybit现货特有）
        common_base_precisions = ["0.000001", "0.00001", "0.0001", "0.001", "0.01", "0.1", "1"]
        
        for base_precision in common_base_precisions:
            step_decimal = Decimal(base_precision)
            qty_decimal = Decimal(str(quantity))
            
            # 检查是否符合basePrecision要求
            remainder = qty_decimal % step_decimal
            is_valid = remainder == 0
            
            if not is_valid:
                truncated = (qty_decimal // step_decimal) * step_decimal
                test_case["analysis"][f"base_precision_{base_precision}"] = {
                    "is_valid": False,
                    "remainder": float(remainder),
                    "should_be": float(truncated)
                }
            else:
                test_case["analysis"][f"base_precision_{base_precision}"] = {
                    "is_valid": True
                }
        
        self.results["test_cases"].append(test_case)
        
        # 根本原因分析
        self.results["root_causes"].append({
            "symbol": "SPK-USDT",
            "issue": f"数量321.995有{decimal_places}位小数，超过Bybit现货允许的精度",
            "likely_cause": "basePrecision获取错误或精度截取失败"
        })
    
    def check_trading_rules_system(self):
        """检查交易规则系统"""
        print("🔍 检查交易规则系统...")
        
        try:
            # 检查交易规则预加载器
            from core.trading_rules_preloader import get_trading_rules_preloader
            preloader = get_trading_rules_preloader()
            
            # 检查ICNT-USDT期货规则
            icnt_rule = preloader.get_trading_rule("bybit", "ICNT-USDT", "futures")
            if icnt_rule:
                self.results["trading_rules"] = {
                    "ICNT-USDT_futures": {
                        "step_size": icnt_rule.qty_step,
                        "price_step": icnt_rule.price_step,
                        "min_amount": icnt_rule.min_amount,
                        "amount_precision": icnt_rule.amount_precision
                    }
                }
            else:
                self.results["trading_rules"] = {
                    "ICNT-USDT_futures": "规则未找到"
                }
            
            # 检查SPK-USDT现货规则
            spk_rule = preloader.get_trading_rule("bybit", "SPK-USDT", "spot")
            if spk_rule:
                self.results["trading_rules"]["SPK-USDT_spot"] = {
                    "step_size": spk_rule.qty_step,
                    "price_step": spk_rule.price_step,
                    "min_amount": spk_rule.min_amount,
                    "amount_precision": spk_rule.amount_precision
                }
            else:
                self.results["trading_rules"]["SPK-USDT_spot"] = "规则未找到"
                
        except Exception as e:
            self.results["trading_rules"] = f"检查失败: {e}"
    
    def generate_recommendations(self):
        """生成修复建议"""
        print("🔧 生成修复建议...")
        
        recommendations = [
            {
                "priority": "HIGH",
                "issue": "ICNT-USDT期货数量精度问题",
                "solution": "检查期货交易规则获取逻辑，确保正确获取qtyStep",
                "code_location": "trading_rules_preloader.py _get_bybit_trading_rule方法"
            },
            {
                "priority": "HIGH", 
                "issue": "SPK-USDT现货小数位过多",
                "solution": "检查现货交易规则获取逻辑，确保正确获取basePrecision",
                "code_location": "trading_rules_preloader.py _get_bybit_trading_rule方法"
            },
            {
                "priority": "MEDIUM",
                "issue": "数量格式化统一性",
                "solution": "确保format_amount_unified方法正确处理Bybit现货和期货的不同精度要求",
                "code_location": "trading_rules_preloader.py format_amount_unified方法"
            },
            {
                "priority": "MEDIUM",
                "issue": "错误重试机制",
                "solution": "在订单失败时，自动重新获取交易规则并重试",
                "code_location": "unified_opening_manager.py execute_opening_order方法"
            }
        ]
        
        self.results["recommendations"] = recommendations
    
    def run_diagnosis(self):
        """运行完整诊断"""
        print("🚀 开始订单失败精准诊断...")
        print("=" * 60)
        
        # 分析两个具体错误
        self.analyze_icnt_usdt_futures_error()
        self.analyze_spk_usdt_spot_error()
        
        # 检查交易规则系统
        self.check_trading_rules_system()
        
        # 生成修复建议
        self.generate_recommendations()
        
        # 保存结果
        output_file = "123/diagnostic_results/order_failure_diagnosis.json"
        os.makedirs(os.path.dirname(output_file), exist_ok=True)
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, indent=2, ensure_ascii=False)
        
        print(f"✅ 诊断完成，结果已保存到: {output_file}")
        
        # 输出关键发现
        print("\n🔍 关键发现:")
        for cause in self.results["root_causes"]:
            print(f"  ❌ {cause['symbol']}: {cause['issue']}")
            print(f"     可能原因: {cause['likely_cause']}")
        
        print("\n🔧 修复建议:")
        for rec in self.results["recommendations"]:
            print(f"  {rec['priority']}: {rec['issue']}")
            print(f"     解决方案: {rec['solution']}")
            print(f"     代码位置: {rec['code_location']}")
        
        return self.results

if __name__ == "__main__":
    diagnosis = OrderFailureDiagnosis()
    results = diagnosis.run_diagnosis()
