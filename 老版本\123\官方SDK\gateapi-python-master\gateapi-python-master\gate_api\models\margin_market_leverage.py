# coding: utf-8

"""
    Gate API v4

    Welcome to Gate.io API  APIv4 provides spot, margin and futures trading operations. There are public APIs to retrieve the real-time market statistics, and private APIs which needs authentication to trade on user's behalf.  # noqa: E501

    Contact: <EMAIL>
    Generated by: https://openapi-generator.tech
"""


import pprint
import re  # noqa: F401

import six

from gate_api.configuration import Configuration


class MarginMarketLeverage(object):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        'currency_pair': 'str',
        'leverage': 'str'
    }

    attribute_map = {
        'currency_pair': 'currency_pair',
        'leverage': 'leverage'
    }

    def __init__(self, currency_pair=None, leverage=None, local_vars_configuration=None):  # noqa: E501
        # type: (str, str, Configuration) -> None
        """MarginMarketLeverage - a model defined in OpenAPI"""  # noqa: E501
        if local_vars_configuration is None:
            local_vars_configuration = Configuration()
        self.local_vars_configuration = local_vars_configuration

        self._currency_pair = None
        self._leverage = None
        self.discriminator = None

        if currency_pair is not None:
            self.currency_pair = currency_pair
        self.leverage = leverage

    @property
    def currency_pair(self):
        """Gets the currency_pair of this MarginMarketLeverage.  # noqa: E501

        Currency pair  # noqa: E501

        :return: The currency_pair of this MarginMarketLeverage.  # noqa: E501
        :rtype: str
        """
        return self._currency_pair

    @currency_pair.setter
    def currency_pair(self, currency_pair):
        """Sets the currency_pair of this MarginMarketLeverage.

        Currency pair  # noqa: E501

        :param currency_pair: The currency_pair of this MarginMarketLeverage.  # noqa: E501
        :type: str
        """

        self._currency_pair = currency_pair

    @property
    def leverage(self):
        """Gets the leverage of this MarginMarketLeverage.  # noqa: E501

        Position leverage  # noqa: E501

        :return: The leverage of this MarginMarketLeverage.  # noqa: E501
        :rtype: str
        """
        return self._leverage

    @leverage.setter
    def leverage(self, leverage):
        """Sets the leverage of this MarginMarketLeverage.

        Position leverage  # noqa: E501

        :param leverage: The leverage of this MarginMarketLeverage.  # noqa: E501
        :type: str
        """
        if self.local_vars_configuration.client_side_validation and leverage is None:  # noqa: E501
            raise ValueError("Invalid value for `leverage`, must not be `None`")  # noqa: E501

        self._leverage = leverage

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.openapi_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, MarginMarketLeverage):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, MarginMarketLeverage):
            return True

        return self.to_dict() != other.to_dict()
