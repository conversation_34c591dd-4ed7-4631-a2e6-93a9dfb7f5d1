# coding: utf-8

"""
    Gate API v4

    Welcome to Gate.io API  APIv4 provides spot, margin and futures trading operations. There are public APIs to retrieve the real-time market statistics, and private APIs which needs authentication to trade on user's behalf.  # noqa: E501

    Contact: <EMAIL>
    Generated by: https://openapi-generator.tech
"""


import pprint
import re  # noqa: F401

import six

from gate_api.configuration import Configuration


class FuturesLimitRiskTiers(object):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        'tier': 'int',
        'risk_limit': 'str',
        'initial_rate': 'str',
        'maintenance_rate': 'str',
        'leverage_max': 'str',
        'contract': 'str'
    }

    attribute_map = {
        'tier': 'tier',
        'risk_limit': 'risk_limit',
        'initial_rate': 'initial_rate',
        'maintenance_rate': 'maintenance_rate',
        'leverage_max': 'leverage_max',
        'contract': 'contract'
    }

    def __init__(self, tier=None, risk_limit=None, initial_rate=None, maintenance_rate=None, leverage_max=None, contract=None, local_vars_configuration=None):  # noqa: E501
        # type: (int, str, str, str, str, str, Configuration) -> None
        """FuturesLimitRiskTiers - a model defined in OpenAPI"""  # noqa: E501
        if local_vars_configuration is None:
            local_vars_configuration = Configuration()
        self.local_vars_configuration = local_vars_configuration

        self._tier = None
        self._risk_limit = None
        self._initial_rate = None
        self._maintenance_rate = None
        self._leverage_max = None
        self._contract = None
        self.discriminator = None

        if tier is not None:
            self.tier = tier
        if risk_limit is not None:
            self.risk_limit = risk_limit
        if initial_rate is not None:
            self.initial_rate = initial_rate
        if maintenance_rate is not None:
            self.maintenance_rate = maintenance_rate
        if leverage_max is not None:
            self.leverage_max = leverage_max
        if contract is not None:
            self.contract = contract

    @property
    def tier(self):
        """Gets the tier of this FuturesLimitRiskTiers.  # noqa: E501

        Tier  # noqa: E501

        :return: The tier of this FuturesLimitRiskTiers.  # noqa: E501
        :rtype: int
        """
        return self._tier

    @tier.setter
    def tier(self, tier):
        """Sets the tier of this FuturesLimitRiskTiers.

        Tier  # noqa: E501

        :param tier: The tier of this FuturesLimitRiskTiers.  # noqa: E501
        :type: int
        """

        self._tier = tier

    @property
    def risk_limit(self):
        """Gets the risk_limit of this FuturesLimitRiskTiers.  # noqa: E501

        Position risk limit  # noqa: E501

        :return: The risk_limit of this FuturesLimitRiskTiers.  # noqa: E501
        :rtype: str
        """
        return self._risk_limit

    @risk_limit.setter
    def risk_limit(self, risk_limit):
        """Sets the risk_limit of this FuturesLimitRiskTiers.

        Position risk limit  # noqa: E501

        :param risk_limit: The risk_limit of this FuturesLimitRiskTiers.  # noqa: E501
        :type: str
        """

        self._risk_limit = risk_limit

    @property
    def initial_rate(self):
        """Gets the initial_rate of this FuturesLimitRiskTiers.  # noqa: E501

        Initial margin rate  # noqa: E501

        :return: The initial_rate of this FuturesLimitRiskTiers.  # noqa: E501
        :rtype: str
        """
        return self._initial_rate

    @initial_rate.setter
    def initial_rate(self, initial_rate):
        """Sets the initial_rate of this FuturesLimitRiskTiers.

        Initial margin rate  # noqa: E501

        :param initial_rate: The initial_rate of this FuturesLimitRiskTiers.  # noqa: E501
        :type: str
        """

        self._initial_rate = initial_rate

    @property
    def maintenance_rate(self):
        """Gets the maintenance_rate of this FuturesLimitRiskTiers.  # noqa: E501

        Maintenance margin rate  # noqa: E501

        :return: The maintenance_rate of this FuturesLimitRiskTiers.  # noqa: E501
        :rtype: str
        """
        return self._maintenance_rate

    @maintenance_rate.setter
    def maintenance_rate(self, maintenance_rate):
        """Sets the maintenance_rate of this FuturesLimitRiskTiers.

        Maintenance margin rate  # noqa: E501

        :param maintenance_rate: The maintenance_rate of this FuturesLimitRiskTiers.  # noqa: E501
        :type: str
        """

        self._maintenance_rate = maintenance_rate

    @property
    def leverage_max(self):
        """Gets the leverage_max of this FuturesLimitRiskTiers.  # noqa: E501

        Maximum leverage  # noqa: E501

        :return: The leverage_max of this FuturesLimitRiskTiers.  # noqa: E501
        :rtype: str
        """
        return self._leverage_max

    @leverage_max.setter
    def leverage_max(self, leverage_max):
        """Sets the leverage_max of this FuturesLimitRiskTiers.

        Maximum leverage  # noqa: E501

        :param leverage_max: The leverage_max of this FuturesLimitRiskTiers.  # noqa: E501
        :type: str
        """

        self._leverage_max = leverage_max

    @property
    def contract(self):
        """Gets the contract of this FuturesLimitRiskTiers.  # noqa: E501

        Markets, visible only during market pagination requests  # noqa: E501

        :return: The contract of this FuturesLimitRiskTiers.  # noqa: E501
        :rtype: str
        """
        return self._contract

    @contract.setter
    def contract(self, contract):
        """Sets the contract of this FuturesLimitRiskTiers.

        Markets, visible only during market pagination requests  # noqa: E501

        :param contract: The contract of this FuturesLimitRiskTiers.  # noqa: E501
        :type: str
        """

        self._contract = contract

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.openapi_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, FuturesLimitRiskTiers):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, FuturesLimitRiskTiers):
            return True

        return self.to_dict() != other.to_dict()
