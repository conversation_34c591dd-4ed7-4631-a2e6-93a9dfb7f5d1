# coding: utf-8

"""
    Gate API v4

    Welcome to Gate.io API  APIv4 provides spot, margin and futures trading operations. There are public APIs to retrieve the real-time market statistics, and private APIs which needs authentication to trade on user's behalf.  # noqa: E501

    Contact: <EMAIL>
    Generated by: https://openapi-generator.tech
"""


import pprint
import re  # noqa: F401

import six

from gate_api.configuration import Configuration


class FuturesOrderBook(object):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        'id': 'int',
        'current': 'float',
        'update': 'float',
        'asks': 'list[FuturesOrderBookItem]',
        'bids': 'list[FuturesOrderBookItem]'
    }

    attribute_map = {
        'id': 'id',
        'current': 'current',
        'update': 'update',
        'asks': 'asks',
        'bids': 'bids'
    }

    def __init__(self, id=None, current=None, update=None, asks=None, bids=None, local_vars_configuration=None):  # noqa: E501
        # type: (int, float, float, list[FuturesOrderBookItem], list[FuturesOrderBookItem], Configuration) -> None
        """FuturesOrderBook - a model defined in OpenAPI"""  # noqa: E501
        if local_vars_configuration is None:
            local_vars_configuration = Configuration()
        self.local_vars_configuration = local_vars_configuration

        self._id = None
        self._current = None
        self._update = None
        self._asks = None
        self._bids = None
        self.discriminator = None

        if id is not None:
            self.id = id
        if current is not None:
            self.current = current
        if update is not None:
            self.update = update
        self.asks = asks
        self.bids = bids

    @property
    def id(self):
        """Gets the id of this FuturesOrderBook.  # noqa: E501

        Order Book ID. Increases by 1 on every order book change. Set `with_id=true` to include this field in response  # noqa: E501

        :return: The id of this FuturesOrderBook.  # noqa: E501
        :rtype: int
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this FuturesOrderBook.

        Order Book ID. Increases by 1 on every order book change. Set `with_id=true` to include this field in response  # noqa: E501

        :param id: The id of this FuturesOrderBook.  # noqa: E501
        :type: int
        """

        self._id = id

    @property
    def current(self):
        """Gets the current of this FuturesOrderBook.  # noqa: E501

        Response data generation timestamp  # noqa: E501

        :return: The current of this FuturesOrderBook.  # noqa: E501
        :rtype: float
        """
        return self._current

    @current.setter
    def current(self, current):
        """Sets the current of this FuturesOrderBook.

        Response data generation timestamp  # noqa: E501

        :param current: The current of this FuturesOrderBook.  # noqa: E501
        :type: float
        """

        self._current = current

    @property
    def update(self):
        """Gets the update of this FuturesOrderBook.  # noqa: E501

        Order book changed timestamp  # noqa: E501

        :return: The update of this FuturesOrderBook.  # noqa: E501
        :rtype: float
        """
        return self._update

    @update.setter
    def update(self, update):
        """Sets the update of this FuturesOrderBook.

        Order book changed timestamp  # noqa: E501

        :param update: The update of this FuturesOrderBook.  # noqa: E501
        :type: float
        """

        self._update = update

    @property
    def asks(self):
        """Gets the asks of this FuturesOrderBook.  # noqa: E501

        Asks order depth  # noqa: E501

        :return: The asks of this FuturesOrderBook.  # noqa: E501
        :rtype: list[FuturesOrderBookItem]
        """
        return self._asks

    @asks.setter
    def asks(self, asks):
        """Sets the asks of this FuturesOrderBook.

        Asks order depth  # noqa: E501

        :param asks: The asks of this FuturesOrderBook.  # noqa: E501
        :type: list[FuturesOrderBookItem]
        """
        if self.local_vars_configuration.client_side_validation and asks is None:  # noqa: E501
            raise ValueError("Invalid value for `asks`, must not be `None`")  # noqa: E501

        self._asks = asks

    @property
    def bids(self):
        """Gets the bids of this FuturesOrderBook.  # noqa: E501

        Bids order depth  # noqa: E501

        :return: The bids of this FuturesOrderBook.  # noqa: E501
        :rtype: list[FuturesOrderBookItem]
        """
        return self._bids

    @bids.setter
    def bids(self, bids):
        """Sets the bids of this FuturesOrderBook.

        Bids order depth  # noqa: E501

        :param bids: The bids of this FuturesOrderBook.  # noqa: E501
        :type: list[FuturesOrderBookItem]
        """
        if self.local_vars_configuration.client_side_validation and bids is None:  # noqa: E501
            raise ValueError("Invalid value for `bids`, must not be `None`")  # noqa: E501

        self._bids = bids

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.openapi_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, FuturesOrderBook):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, FuturesOrderBook):
            return True

        return self.to_dict() != other.to_dict()
