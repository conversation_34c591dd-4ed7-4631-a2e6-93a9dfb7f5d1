# coding: utf-8

"""
    Gate API v4

    Welcome to Gate.io API  APIv4 provides spot, margin and futures trading operations. There are public APIs to retrieve the real-time market statistics, and private APIs which needs authentication to trade on user's behalf.  # noqa: E501

    Contact: <EMAIL>
    Generated by: https://openapi-generator.tech
"""


import pprint
import re  # noqa: F401

import six

from gate_api.configuration import Configuration


class FuturesInitialOrder(object):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        'contract': 'str',
        'size': 'int',
        'price': 'str',
        'close': 'bool',
        'tif': 'str',
        'text': 'str',
        'reduce_only': 'bool',
        'auto_size': 'str',
        'is_reduce_only': 'bool',
        'is_close': 'bool'
    }

    attribute_map = {
        'contract': 'contract',
        'size': 'size',
        'price': 'price',
        'close': 'close',
        'tif': 'tif',
        'text': 'text',
        'reduce_only': 'reduce_only',
        'auto_size': 'auto_size',
        'is_reduce_only': 'is_reduce_only',
        'is_close': 'is_close'
    }

    def __init__(self, contract=None, size=None, price=None, close=False, tif='gtc', text=None, reduce_only=False, auto_size=None, is_reduce_only=None, is_close=None, local_vars_configuration=None):  # noqa: E501
        # type: (str, int, str, bool, str, str, bool, str, bool, bool, Configuration) -> None
        """FuturesInitialOrder - a model defined in OpenAPI"""  # noqa: E501
        if local_vars_configuration is None:
            local_vars_configuration = Configuration()
        self.local_vars_configuration = local_vars_configuration

        self._contract = None
        self._size = None
        self._price = None
        self._close = None
        self._tif = None
        self._text = None
        self._reduce_only = None
        self._auto_size = None
        self._is_reduce_only = None
        self._is_close = None
        self.discriminator = None

        self.contract = contract
        if size is not None:
            self.size = size
        self.price = price
        if close is not None:
            self.close = close
        if tif is not None:
            self.tif = tif
        if text is not None:
            self.text = text
        if reduce_only is not None:
            self.reduce_only = reduce_only
        if auto_size is not None:
            self.auto_size = auto_size
        if is_reduce_only is not None:
            self.is_reduce_only = is_reduce_only
        if is_close is not None:
            self.is_close = is_close

    @property
    def contract(self):
        """Gets the contract of this FuturesInitialOrder.  # noqa: E501

        Futures contract  # noqa: E501

        :return: The contract of this FuturesInitialOrder.  # noqa: E501
        :rtype: str
        """
        return self._contract

    @contract.setter
    def contract(self, contract):
        """Sets the contract of this FuturesInitialOrder.

        Futures contract  # noqa: E501

        :param contract: The contract of this FuturesInitialOrder.  # noqa: E501
        :type: str
        """
        if self.local_vars_configuration.client_side_validation and contract is None:  # noqa: E501
            raise ValueError("Invalid value for `contract`, must not be `None`")  # noqa: E501

        self._contract = contract

    @property
    def size(self):
        """Gets the size of this FuturesInitialOrder.  # noqa: E501

        Represents the number of contracts that need to be closed, full closing: size=0 Partial closing: plan-close-short-position size>0  Partial closing: plan-close-long-position size<0  # noqa: E501

        :return: The size of this FuturesInitialOrder.  # noqa: E501
        :rtype: int
        """
        return self._size

    @size.setter
    def size(self, size):
        """Sets the size of this FuturesInitialOrder.

        Represents the number of contracts that need to be closed, full closing: size=0 Partial closing: plan-close-short-position size>0  Partial closing: plan-close-long-position size<0  # noqa: E501

        :param size: The size of this FuturesInitialOrder.  # noqa: E501
        :type: int
        """

        self._size = size

    @property
    def price(self):
        """Gets the price of this FuturesInitialOrder.  # noqa: E501

        Order price. Set to 0 to use market price  # noqa: E501

        :return: The price of this FuturesInitialOrder.  # noqa: E501
        :rtype: str
        """
        return self._price

    @price.setter
    def price(self, price):
        """Sets the price of this FuturesInitialOrder.

        Order price. Set to 0 to use market price  # noqa: E501

        :param price: The price of this FuturesInitialOrder.  # noqa: E501
        :type: str
        """
        if self.local_vars_configuration.client_side_validation and price is None:  # noqa: E501
            raise ValueError("Invalid value for `price`, must not be `None`")  # noqa: E501

        self._price = price

    @property
    def close(self):
        """Gets the close of this FuturesInitialOrder.  # noqa: E501

        When all positions are closed in a single position mode, it must be set to true to perform the closing operation When partially closed positions in single-store mode/double-store mode, you can not set close, or close=false  # noqa: E501

        :return: The close of this FuturesInitialOrder.  # noqa: E501
        :rtype: bool
        """
        return self._close

    @close.setter
    def close(self, close):
        """Sets the close of this FuturesInitialOrder.

        When all positions are closed in a single position mode, it must be set to true to perform the closing operation When partially closed positions in single-store mode/double-store mode, you can not set close, or close=false  # noqa: E501

        :param close: The close of this FuturesInitialOrder.  # noqa: E501
        :type: bool
        """

        self._close = close

    @property
    def tif(self):
        """Gets the tif of this FuturesInitialOrder.  # noqa: E501

        Time in force strategy, default is gtc, market order currently only supports ioc mode Market order currently only supports ioc mode  - gtc: GoodTillCancelled - ioc: ImmediateOrCancelled  # noqa: E501

        :return: The tif of this FuturesInitialOrder.  # noqa: E501
        :rtype: str
        """
        return self._tif

    @tif.setter
    def tif(self, tif):
        """Sets the tif of this FuturesInitialOrder.

        Time in force strategy, default is gtc, market order currently only supports ioc mode Market order currently only supports ioc mode  - gtc: GoodTillCancelled - ioc: ImmediateOrCancelled  # noqa: E501

        :param tif: The tif of this FuturesInitialOrder.  # noqa: E501
        :type: str
        """
        allowed_values = ["gtc", "ioc"]  # noqa: E501
        if self.local_vars_configuration.client_side_validation and tif not in allowed_values:  # noqa: E501
            raise ValueError(
                "Invalid value for `tif` ({0}), must be one of {1}"  # noqa: E501
                .format(tif, allowed_values)
            )

        self._tif = tif

    @property
    def text(self):
        """Gets the text of this FuturesInitialOrder.  # noqa: E501

        The source of the order, including: - web: web - api: api - app: app  # noqa: E501

        :return: The text of this FuturesInitialOrder.  # noqa: E501
        :rtype: str
        """
        return self._text

    @text.setter
    def text(self, text):
        """Sets the text of this FuturesInitialOrder.

        The source of the order, including: - web: web - api: api - app: app  # noqa: E501

        :param text: The text of this FuturesInitialOrder.  # noqa: E501
        :type: str
        """

        self._text = text

    @property
    def reduce_only(self):
        """Gets the reduce_only of this FuturesInitialOrder.  # noqa: E501

        When set to true, perform automatic position reduction operation. Set to true to ensure that the order will not open a new position, and is only used to close or reduce positions  # noqa: E501

        :return: The reduce_only of this FuturesInitialOrder.  # noqa: E501
        :rtype: bool
        """
        return self._reduce_only

    @reduce_only.setter
    def reduce_only(self, reduce_only):
        """Sets the reduce_only of this FuturesInitialOrder.

        When set to true, perform automatic position reduction operation. Set to true to ensure that the order will not open a new position, and is only used to close or reduce positions  # noqa: E501

        :param reduce_only: The reduce_only of this FuturesInitialOrder.  # noqa: E501
        :type: bool
        """

        self._reduce_only = reduce_only

    @property
    def auto_size(self):
        """Gets the auto_size of this FuturesInitialOrder.  # noqa: E501

        Do not set auto_size When the dual-position mode is closed all positions (size=0), auto_size, close_long, close_short, short When the double-storey mode partially closes the position (size ≠ 0), there is no need to set auto_size  # noqa: E501

        :return: The auto_size of this FuturesInitialOrder.  # noqa: E501
        :rtype: str
        """
        return self._auto_size

    @auto_size.setter
    def auto_size(self, auto_size):
        """Sets the auto_size of this FuturesInitialOrder.

        Do not set auto_size When the dual-position mode is closed all positions (size=0), auto_size, close_long, close_short, short When the double-storey mode partially closes the position (size ≠ 0), there is no need to set auto_size  # noqa: E501

        :param auto_size: The auto_size of this FuturesInitialOrder.  # noqa: E501
        :type: str
        """

        self._auto_size = auto_size

    @property
    def is_reduce_only(self):
        """Gets the is_reduce_only of this FuturesInitialOrder.  # noqa: E501

        Is the order reduce-only  # noqa: E501

        :return: The is_reduce_only of this FuturesInitialOrder.  # noqa: E501
        :rtype: bool
        """
        return self._is_reduce_only

    @is_reduce_only.setter
    def is_reduce_only(self, is_reduce_only):
        """Sets the is_reduce_only of this FuturesInitialOrder.

        Is the order reduce-only  # noqa: E501

        :param is_reduce_only: The is_reduce_only of this FuturesInitialOrder.  # noqa: E501
        :type: bool
        """

        self._is_reduce_only = is_reduce_only

    @property
    def is_close(self):
        """Gets the is_close of this FuturesInitialOrder.  # noqa: E501

        Is the order to close position  # noqa: E501

        :return: The is_close of this FuturesInitialOrder.  # noqa: E501
        :rtype: bool
        """
        return self._is_close

    @is_close.setter
    def is_close(self, is_close):
        """Sets the is_close of this FuturesInitialOrder.

        Is the order to close position  # noqa: E501

        :param is_close: The is_close of this FuturesInitialOrder.  # noqa: E501
        :type: bool
        """

        self._is_close = is_close

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.openapi_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, FuturesInitialOrder):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, FuturesInitialOrder):
            return True

        return self.to_dict() != other.to_dict()
