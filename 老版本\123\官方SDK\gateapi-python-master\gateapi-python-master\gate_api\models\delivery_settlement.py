# coding: utf-8

"""
    Gate API v4

    Welcome to Gate.io API  APIv4 provides spot, margin and futures trading operations. There are public APIs to retrieve the real-time market statistics, and private APIs which needs authentication to trade on user's behalf.  # noqa: E501

    Contact: <EMAIL>
    Generated by: https://openapi-generator.tech
"""


import pprint
import re  # noqa: F401

import six

from gate_api.configuration import Configuration


class DeliverySettlement(object):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        'time': 'int',
        'contract': 'str',
        'leverage': 'str',
        'size': 'int',
        'margin': 'str',
        'entry_price': 'str',
        'settle_price': 'str',
        'profit': 'str',
        'fee': 'str'
    }

    attribute_map = {
        'time': 'time',
        'contract': 'contract',
        'leverage': 'leverage',
        'size': 'size',
        'margin': 'margin',
        'entry_price': 'entry_price',
        'settle_price': 'settle_price',
        'profit': 'profit',
        'fee': 'fee'
    }

    def __init__(self, time=None, contract=None, leverage=None, size=None, margin=None, entry_price=None, settle_price=None, profit=None, fee=None, local_vars_configuration=None):  # noqa: E501
        # type: (int, str, str, int, str, str, str, str, str, Configuration) -> None
        """DeliverySettlement - a model defined in OpenAPI"""  # noqa: E501
        if local_vars_configuration is None:
            local_vars_configuration = Configuration()
        self.local_vars_configuration = local_vars_configuration

        self._time = None
        self._contract = None
        self._leverage = None
        self._size = None
        self._margin = None
        self._entry_price = None
        self._settle_price = None
        self._profit = None
        self._fee = None
        self.discriminator = None

        if time is not None:
            self.time = time
        if contract is not None:
            self.contract = contract
        if leverage is not None:
            self.leverage = leverage
        if size is not None:
            self.size = size
        if margin is not None:
            self.margin = margin
        if entry_price is not None:
            self.entry_price = entry_price
        if settle_price is not None:
            self.settle_price = settle_price
        if profit is not None:
            self.profit = profit
        if fee is not None:
            self.fee = fee

    @property
    def time(self):
        """Gets the time of this DeliverySettlement.  # noqa: E501

        Liquidation time  # noqa: E501

        :return: The time of this DeliverySettlement.  # noqa: E501
        :rtype: int
        """
        return self._time

    @time.setter
    def time(self, time):
        """Sets the time of this DeliverySettlement.

        Liquidation time  # noqa: E501

        :param time: The time of this DeliverySettlement.  # noqa: E501
        :type: int
        """

        self._time = time

    @property
    def contract(self):
        """Gets the contract of this DeliverySettlement.  # noqa: E501

        Futures contract  # noqa: E501

        :return: The contract of this DeliverySettlement.  # noqa: E501
        :rtype: str
        """
        return self._contract

    @contract.setter
    def contract(self, contract):
        """Sets the contract of this DeliverySettlement.

        Futures contract  # noqa: E501

        :param contract: The contract of this DeliverySettlement.  # noqa: E501
        :type: str
        """

        self._contract = contract

    @property
    def leverage(self):
        """Gets the leverage of this DeliverySettlement.  # noqa: E501

        Position leverage  # noqa: E501

        :return: The leverage of this DeliverySettlement.  # noqa: E501
        :rtype: str
        """
        return self._leverage

    @leverage.setter
    def leverage(self, leverage):
        """Sets the leverage of this DeliverySettlement.

        Position leverage  # noqa: E501

        :param leverage: The leverage of this DeliverySettlement.  # noqa: E501
        :type: str
        """

        self._leverage = leverage

    @property
    def size(self):
        """Gets the size of this DeliverySettlement.  # noqa: E501

        Position size  # noqa: E501

        :return: The size of this DeliverySettlement.  # noqa: E501
        :rtype: int
        """
        return self._size

    @size.setter
    def size(self, size):
        """Sets the size of this DeliverySettlement.

        Position size  # noqa: E501

        :param size: The size of this DeliverySettlement.  # noqa: E501
        :type: int
        """

        self._size = size

    @property
    def margin(self):
        """Gets the margin of this DeliverySettlement.  # noqa: E501

        Position margin  # noqa: E501

        :return: The margin of this DeliverySettlement.  # noqa: E501
        :rtype: str
        """
        return self._margin

    @margin.setter
    def margin(self, margin):
        """Sets the margin of this DeliverySettlement.

        Position margin  # noqa: E501

        :param margin: The margin of this DeliverySettlement.  # noqa: E501
        :type: str
        """

        self._margin = margin

    @property
    def entry_price(self):
        """Gets the entry_price of this DeliverySettlement.  # noqa: E501

        Average entry price  # noqa: E501

        :return: The entry_price of this DeliverySettlement.  # noqa: E501
        :rtype: str
        """
        return self._entry_price

    @entry_price.setter
    def entry_price(self, entry_price):
        """Sets the entry_price of this DeliverySettlement.

        Average entry price  # noqa: E501

        :param entry_price: The entry_price of this DeliverySettlement.  # noqa: E501
        :type: str
        """

        self._entry_price = entry_price

    @property
    def settle_price(self):
        """Gets the settle_price of this DeliverySettlement.  # noqa: E501

        Settled price  # noqa: E501

        :return: The settle_price of this DeliverySettlement.  # noqa: E501
        :rtype: str
        """
        return self._settle_price

    @settle_price.setter
    def settle_price(self, settle_price):
        """Sets the settle_price of this DeliverySettlement.

        Settled price  # noqa: E501

        :param settle_price: The settle_price of this DeliverySettlement.  # noqa: E501
        :type: str
        """

        self._settle_price = settle_price

    @property
    def profit(self):
        """Gets the profit of this DeliverySettlement.  # noqa: E501

        Profit  # noqa: E501

        :return: The profit of this DeliverySettlement.  # noqa: E501
        :rtype: str
        """
        return self._profit

    @profit.setter
    def profit(self, profit):
        """Sets the profit of this DeliverySettlement.

        Profit  # noqa: E501

        :param profit: The profit of this DeliverySettlement.  # noqa: E501
        :type: str
        """

        self._profit = profit

    @property
    def fee(self):
        """Gets the fee of this DeliverySettlement.  # noqa: E501

        Fee deducted  # noqa: E501

        :return: The fee of this DeliverySettlement.  # noqa: E501
        :rtype: str
        """
        return self._fee

    @fee.setter
    def fee(self, fee):
        """Sets the fee of this DeliverySettlement.

        Fee deducted  # noqa: E501

        :param fee: The fee of this DeliverySettlement.  # noqa: E501
        :type: str
        """

        self._fee = fee

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.openapi_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DeliverySettlement):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DeliverySettlement):
            return True

        return self.to_dict() != other.to_dict()
