# coding: utf-8

"""
    Gate API v4

    Welcome to Gate.io API  APIv4 provides spot, margin and futures trading operations. There are public APIs to retrieve the real-time market statistics, and private APIs which needs authentication to trade on user's behalf.  # noqa: E501

    Contact: <EMAIL>
    Generated by: https://openapi-generator.tech
"""


import pprint
import re  # noqa: F401

import six

from gate_api.configuration import Configuration


class FuturesBatchAmendOrderRequest(object):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        'order_id': 'int',
        'text': 'str',
        'size': 'int',
        'price': 'str',
        'amend_text': 'str'
    }

    attribute_map = {
        'order_id': 'order_id',
        'text': 'text',
        'size': 'size',
        'price': 'price',
        'amend_text': 'amend_text'
    }

    def __init__(self, order_id=None, text=None, size=None, price=None, amend_text=None, local_vars_configuration=None):  # noqa: E501
        # type: (int, str, int, str, str, Configuration) -> None
        """FuturesBatchAmendOrderRequest - a model defined in OpenAPI"""  # noqa: E501
        if local_vars_configuration is None:
            local_vars_configuration = Configuration()
        self.local_vars_configuration = local_vars_configuration

        self._order_id = None
        self._text = None
        self._size = None
        self._price = None
        self._amend_text = None
        self.discriminator = None

        if order_id is not None:
            self.order_id = order_id
        if text is not None:
            self.text = text
        if size is not None:
            self.size = size
        if price is not None:
            self.price = price
        if amend_text is not None:
            self.amend_text = amend_text

    @property
    def order_id(self):
        """Gets the order_id of this FuturesBatchAmendOrderRequest.  # noqa: E501

        Order id, order_id and text must contain at least one  # noqa: E501

        :return: The order_id of this FuturesBatchAmendOrderRequest.  # noqa: E501
        :rtype: int
        """
        return self._order_id

    @order_id.setter
    def order_id(self, order_id):
        """Sets the order_id of this FuturesBatchAmendOrderRequest.

        Order id, order_id and text must contain at least one  # noqa: E501

        :param order_id: The order_id of this FuturesBatchAmendOrderRequest.  # noqa: E501
        :type: int
        """

        self._order_id = order_id

    @property
    def text(self):
        """Gets the text of this FuturesBatchAmendOrderRequest.  # noqa: E501

        User-defined order text, at least one of order_id and text must be passed  # noqa: E501

        :return: The text of this FuturesBatchAmendOrderRequest.  # noqa: E501
        :rtype: str
        """
        return self._text

    @text.setter
    def text(self, text):
        """Sets the text of this FuturesBatchAmendOrderRequest.

        User-defined order text, at least one of order_id and text must be passed  # noqa: E501

        :param text: The text of this FuturesBatchAmendOrderRequest.  # noqa: E501
        :type: str
        """

        self._text = text

    @property
    def size(self):
        """Gets the size of this FuturesBatchAmendOrderRequest.  # noqa: E501

        The new order size, including the executed order size. - If it is less than or equal to the executed quantity, the order will be cancelled. - The new order direction must be consistent with the original one. - The size of the closing order cannot be modified. - For orders that only reduce positions, if the size is increased, other orders that only reduce positions may be kicked out. - If the price is not modified, reducing the size will not affect the depth of the queue, and increasing the size will place it at the end of the current price.  # noqa: E501

        :return: The size of this FuturesBatchAmendOrderRequest.  # noqa: E501
        :rtype: int
        """
        return self._size

    @size.setter
    def size(self, size):
        """Sets the size of this FuturesBatchAmendOrderRequest.

        The new order size, including the executed order size. - If it is less than or equal to the executed quantity, the order will be cancelled. - The new order direction must be consistent with the original one. - The size of the closing order cannot be modified. - For orders that only reduce positions, if the size is increased, other orders that only reduce positions may be kicked out. - If the price is not modified, reducing the size will not affect the depth of the queue, and increasing the size will place it at the end of the current price.  # noqa: E501

        :param size: The size of this FuturesBatchAmendOrderRequest.  # noqa: E501
        :type: int
        """

        self._size = size

    @property
    def price(self):
        """Gets the price of this FuturesBatchAmendOrderRequest.  # noqa: E501

        New order price.  # noqa: E501

        :return: The price of this FuturesBatchAmendOrderRequest.  # noqa: E501
        :rtype: str
        """
        return self._price

    @price.setter
    def price(self, price):
        """Sets the price of this FuturesBatchAmendOrderRequest.

        New order price.  # noqa: E501

        :param price: The price of this FuturesBatchAmendOrderRequest.  # noqa: E501
        :type: str
        """

        self._price = price

    @property
    def amend_text(self):
        """Gets the amend_text of this FuturesBatchAmendOrderRequest.  # noqa: E501

        Custom info during amending order  # noqa: E501

        :return: The amend_text of this FuturesBatchAmendOrderRequest.  # noqa: E501
        :rtype: str
        """
        return self._amend_text

    @amend_text.setter
    def amend_text(self, amend_text):
        """Sets the amend_text of this FuturesBatchAmendOrderRequest.

        Custom info during amending order  # noqa: E501

        :param amend_text: The amend_text of this FuturesBatchAmendOrderRequest.  # noqa: E501
        :type: str
        """

        self._amend_text = amend_text

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.openapi_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, FuturesBatchAmendOrderRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, FuturesBatchAmendOrderRequest):
            return True

        return self.to_dict() != other.to_dict()
