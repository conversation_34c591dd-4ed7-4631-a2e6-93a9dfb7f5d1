# CrossMarginAccountBook

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**id** | **str** | Balance change record ID | [optional] 
**time** | **int** | The timestamp of the change (in milliseconds) | [optional] 
**currency** | **str** | Currency changed | [optional] 
**change** | **str** | Amount changed. Positive value means transferring in, while negative out | [optional] 
**balance** | **str** | Balance after change | [optional] 
**type** | **str** | Account book type.  Please refer to [account book type](#accountbook-type) for more detail | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


