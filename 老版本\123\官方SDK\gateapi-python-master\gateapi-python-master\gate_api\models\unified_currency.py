# coding: utf-8

"""
    Gate API v4

    Welcome to Gate.io API  APIv4 provides spot, margin and futures trading operations. There are public APIs to retrieve the real-time market statistics, and private APIs which needs authentication to trade on user's behalf.  # noqa: E501

    Contact: <EMAIL>
    Generated by: https://openapi-generator.tech
"""


import pprint
import re  # noqa: F401

import six

from gate_api.configuration import Configuration


class UnifiedCurrency(object):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        'name': 'str',
        'prec': 'str',
        'min_borrow_amount': 'str',
        'user_max_borrow_amount': 'str',
        'total_max_borrow_amount': 'str',
        'loan_status': 'str'
    }

    attribute_map = {
        'name': 'name',
        'prec': 'prec',
        'min_borrow_amount': 'min_borrow_amount',
        'user_max_borrow_amount': 'user_max_borrow_amount',
        'total_max_borrow_amount': 'total_max_borrow_amount',
        'loan_status': 'loan_status'
    }

    def __init__(self, name=None, prec=None, min_borrow_amount=None, user_max_borrow_amount=None, total_max_borrow_amount=None, loan_status=None, local_vars_configuration=None):  # noqa: E501
        # type: (str, str, str, str, str, str, Configuration) -> None
        """UnifiedCurrency - a model defined in OpenAPI"""  # noqa: E501
        if local_vars_configuration is None:
            local_vars_configuration = Configuration()
        self.local_vars_configuration = local_vars_configuration

        self._name = None
        self._prec = None
        self._min_borrow_amount = None
        self._user_max_borrow_amount = None
        self._total_max_borrow_amount = None
        self._loan_status = None
        self.discriminator = None

        if name is not None:
            self.name = name
        if prec is not None:
            self.prec = prec
        if min_borrow_amount is not None:
            self.min_borrow_amount = min_borrow_amount
        if user_max_borrow_amount is not None:
            self.user_max_borrow_amount = user_max_borrow_amount
        if total_max_borrow_amount is not None:
            self.total_max_borrow_amount = total_max_borrow_amount
        if loan_status is not None:
            self.loan_status = loan_status

    @property
    def name(self):
        """Gets the name of this UnifiedCurrency.  # noqa: E501

        Currency name  # noqa: E501

        :return: The name of this UnifiedCurrency.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this UnifiedCurrency.

        Currency name  # noqa: E501

        :param name: The name of this UnifiedCurrency.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def prec(self):
        """Gets the prec of this UnifiedCurrency.  # noqa: E501

        Currency precision  # noqa: E501

        :return: The prec of this UnifiedCurrency.  # noqa: E501
        :rtype: str
        """
        return self._prec

    @prec.setter
    def prec(self, prec):
        """Sets the prec of this UnifiedCurrency.

        Currency precision  # noqa: E501

        :param prec: The prec of this UnifiedCurrency.  # noqa: E501
        :type: str
        """

        self._prec = prec

    @property
    def min_borrow_amount(self):
        """Gets the min_borrow_amount of this UnifiedCurrency.  # noqa: E501

        The minimum debit limit is the unit of currency  # noqa: E501

        :return: The min_borrow_amount of this UnifiedCurrency.  # noqa: E501
        :rtype: str
        """
        return self._min_borrow_amount

    @min_borrow_amount.setter
    def min_borrow_amount(self, min_borrow_amount):
        """Sets the min_borrow_amount of this UnifiedCurrency.

        The minimum debit limit is the unit of currency  # noqa: E501

        :param min_borrow_amount: The min_borrow_amount of this UnifiedCurrency.  # noqa: E501
        :type: str
        """

        self._min_borrow_amount = min_borrow_amount

    @property
    def user_max_borrow_amount(self):
        """Gets the user_max_borrow_amount of this UnifiedCurrency.  # noqa: E501

        The minimum debit limit is the unit of currency  # noqa: E501

        :return: The user_max_borrow_amount of this UnifiedCurrency.  # noqa: E501
        :rtype: str
        """
        return self._user_max_borrow_amount

    @user_max_borrow_amount.setter
    def user_max_borrow_amount(self, user_max_borrow_amount):
        """Sets the user_max_borrow_amount of this UnifiedCurrency.

        The minimum debit limit is the unit of currency  # noqa: E501

        :param user_max_borrow_amount: The user_max_borrow_amount of this UnifiedCurrency.  # noqa: E501
        :type: str
        """

        self._user_max_borrow_amount = user_max_borrow_amount

    @property
    def total_max_borrow_amount(self):
        """Gets the total_max_borrow_amount of this UnifiedCurrency.  # noqa: E501

        The maximum debit limit for the platform is USDT  # noqa: E501

        :return: The total_max_borrow_amount of this UnifiedCurrency.  # noqa: E501
        :rtype: str
        """
        return self._total_max_borrow_amount

    @total_max_borrow_amount.setter
    def total_max_borrow_amount(self, total_max_borrow_amount):
        """Sets the total_max_borrow_amount of this UnifiedCurrency.

        The maximum debit limit for the platform is USDT  # noqa: E501

        :param total_max_borrow_amount: The total_max_borrow_amount of this UnifiedCurrency.  # noqa: E501
        :type: str
        """

        self._total_max_borrow_amount = total_max_borrow_amount

    @property
    def loan_status(self):
        """Gets the loan_status of this UnifiedCurrency.  # noqa: E501

        Does the lending status  - `disable` : Loans are prohibited  - `enable`: Support lending  # noqa: E501

        :return: The loan_status of this UnifiedCurrency.  # noqa: E501
        :rtype: str
        """
        return self._loan_status

    @loan_status.setter
    def loan_status(self, loan_status):
        """Sets the loan_status of this UnifiedCurrency.

        Does the lending status  - `disable` : Loans are prohibited  - `enable`: Support lending  # noqa: E501

        :param loan_status: The loan_status of this UnifiedCurrency.  # noqa: E501
        :type: str
        """

        self._loan_status = loan_status

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.openapi_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, UnifiedCurrency):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, UnifiedCurrency):
            return True

        return self.to_dict() != other.to_dict()
