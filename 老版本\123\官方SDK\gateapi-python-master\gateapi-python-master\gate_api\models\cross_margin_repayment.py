# coding: utf-8

"""
    Gate API v4

    Welcome to Gate.io API  APIv4 provides spot, margin and futures trading operations. There are public APIs to retrieve the real-time market statistics, and private APIs which needs authentication to trade on user's behalf.  # noqa: E501

    Contact: <EMAIL>
    Generated by: https://openapi-generator.tech
"""


import pprint
import re  # noqa: F401

import six

from gate_api.configuration import Configuration


class CrossMarginRepayment(object):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        'id': 'str',
        'create_time': 'int',
        'loan_id': 'str',
        'currency': 'str',
        'principal': 'str',
        'interest': 'str',
        'repayment_type': 'str'
    }

    attribute_map = {
        'id': 'id',
        'create_time': 'create_time',
        'loan_id': 'loan_id',
        'currency': 'currency',
        'principal': 'principal',
        'interest': 'interest',
        'repayment_type': 'repayment_type'
    }

    def __init__(self, id=None, create_time=None, loan_id=None, currency=None, principal=None, interest=None, repayment_type=None, local_vars_configuration=None):  # noqa: E501
        # type: (str, int, str, str, str, str, str, Configuration) -> None
        """CrossMarginRepayment - a model defined in OpenAPI"""  # noqa: E501
        if local_vars_configuration is None:
            local_vars_configuration = Configuration()
        self.local_vars_configuration = local_vars_configuration

        self._id = None
        self._create_time = None
        self._loan_id = None
        self._currency = None
        self._principal = None
        self._interest = None
        self._repayment_type = None
        self.discriminator = None

        if id is not None:
            self.id = id
        if create_time is not None:
            self.create_time = create_time
        if loan_id is not None:
            self.loan_id = loan_id
        if currency is not None:
            self.currency = currency
        if principal is not None:
            self.principal = principal
        if interest is not None:
            self.interest = interest
        if repayment_type is not None:
            self.repayment_type = repayment_type

    @property
    def id(self):
        """Gets the id of this CrossMarginRepayment.  # noqa: E501

        Loan record ID  # noqa: E501

        :return: The id of this CrossMarginRepayment.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this CrossMarginRepayment.

        Loan record ID  # noqa: E501

        :param id: The id of this CrossMarginRepayment.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def create_time(self):
        """Gets the create_time of this CrossMarginRepayment.  # noqa: E501

        Repayment time  # noqa: E501

        :return: The create_time of this CrossMarginRepayment.  # noqa: E501
        :rtype: int
        """
        return self._create_time

    @create_time.setter
    def create_time(self, create_time):
        """Sets the create_time of this CrossMarginRepayment.

        Repayment time  # noqa: E501

        :param create_time: The create_time of this CrossMarginRepayment.  # noqa: E501
        :type: int
        """

        self._create_time = create_time

    @property
    def loan_id(self):
        """Gets the loan_id of this CrossMarginRepayment.  # noqa: E501

        Loan record ID  # noqa: E501

        :return: The loan_id of this CrossMarginRepayment.  # noqa: E501
        :rtype: str
        """
        return self._loan_id

    @loan_id.setter
    def loan_id(self, loan_id):
        """Sets the loan_id of this CrossMarginRepayment.

        Loan record ID  # noqa: E501

        :param loan_id: The loan_id of this CrossMarginRepayment.  # noqa: E501
        :type: str
        """

        self._loan_id = loan_id

    @property
    def currency(self):
        """Gets the currency of this CrossMarginRepayment.  # noqa: E501

        Currency name  # noqa: E501

        :return: The currency of this CrossMarginRepayment.  # noqa: E501
        :rtype: str
        """
        return self._currency

    @currency.setter
    def currency(self, currency):
        """Sets the currency of this CrossMarginRepayment.

        Currency name  # noqa: E501

        :param currency: The currency of this CrossMarginRepayment.  # noqa: E501
        :type: str
        """

        self._currency = currency

    @property
    def principal(self):
        """Gets the principal of this CrossMarginRepayment.  # noqa: E501

        Repaid principal  # noqa: E501

        :return: The principal of this CrossMarginRepayment.  # noqa: E501
        :rtype: str
        """
        return self._principal

    @principal.setter
    def principal(self, principal):
        """Sets the principal of this CrossMarginRepayment.

        Repaid principal  # noqa: E501

        :param principal: The principal of this CrossMarginRepayment.  # noqa: E501
        :type: str
        """

        self._principal = principal

    @property
    def interest(self):
        """Gets the interest of this CrossMarginRepayment.  # noqa: E501

        Repaid interest  # noqa: E501

        :return: The interest of this CrossMarginRepayment.  # noqa: E501
        :rtype: str
        """
        return self._interest

    @interest.setter
    def interest(self, interest):
        """Sets the interest of this CrossMarginRepayment.

        Repaid interest  # noqa: E501

        :param interest: The interest of this CrossMarginRepayment.  # noqa: E501
        :type: str
        """

        self._interest = interest

    @property
    def repayment_type(self):
        """Gets the repayment_type of this CrossMarginRepayment.  # noqa: E501

        Repayment type: none - no repayment type, manual_repay - manual repayment, auto_repay - automatic repayment, cancel_auto_repay - automatic repayment after cancellation  # noqa: E501

        :return: The repayment_type of this CrossMarginRepayment.  # noqa: E501
        :rtype: str
        """
        return self._repayment_type

    @repayment_type.setter
    def repayment_type(self, repayment_type):
        """Sets the repayment_type of this CrossMarginRepayment.

        Repayment type: none - no repayment type, manual_repay - manual repayment, auto_repay - automatic repayment, cancel_auto_repay - automatic repayment after cancellation  # noqa: E501

        :param repayment_type: The repayment_type of this CrossMarginRepayment.  # noqa: E501
        :type: str
        """

        self._repayment_type = repayment_type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.openapi_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CrossMarginRepayment):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CrossMarginRepayment):
            return True

        return self.to_dict() != other.to_dict()
