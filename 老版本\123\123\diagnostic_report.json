{"spread_calculation_accuracy": {"test_cases_count": 2, "results": [{"case_name": "正常期货溢价", "opening_calculation": {"success": true, "spread": 0.00198, "spot_price": 50000.0, "futures_price": 50099.0}, "closing_calculation": {"success": true, "spread": -0.00201597, "spot_price": 49999.0, "futures_price": 50100.0}}, {"case_name": "正常现货溢价", "opening_calculation": {"success": true, "spread": -0.00201597, "spot_price": 50100.0, "futures_price": 49999.0}, "closing_calculation": {"success": true, "spread": 0.00198, "spot_price": 50099.0, "futures_price": 50000.0}}], "summary": "差价计算精准性测试完成"}, "log_consistency": {"error": "日志文件不存在"}, "stuck_spread_analysis": {"engine_status": {"is_executing": false, "current_status": "ArbitrageStatus.IDLE", "current_session": false, "last_execution_time": null}, "scanner_status": {"error": "无法获取OpportunityScanner实例"}, "analysis_time": 1752311775.5016918}, "system_status": {"timestamp": 1752311775.502719, "websocket_connections": {}, "exchange_status": {}, "memory_usage": {}, "file_status": {"123/logs/websocket_prices.log": {"exists": false}, "123/core/unified_order_spread_calculator.py": {"exists": false}, "123/core/execution_engine.py": {"exists": false}, "123/core/opportunity_scanner.py": {"exists": false}}}, "recommendations": ["✅ 差价计算精准性测试通过", "✅ ArbitrageEngine状态正常"]}