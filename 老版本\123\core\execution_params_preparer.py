#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
执行参数准备器 - 负责套利执行前的参数准备和拆单计算
从ExecutionEngine中分离出来的专门模块
"""

import os
from typing import Dict, List, Optional, TYPE_CHECKING
from utils.logger import get_logger
# 🔥 删除硬编码精度导入：不再使用get_amount_precision
import asyncio
import logging
import time

if TYPE_CHECKING:
    from core.opportunity_scanner import ArbitrageOpportunity

logger = get_logger(__name__)


class ExecutionParamsPreparer:
    """🔥 新增：执行参数准备器 - 专门处理执行前的参数准备"""

    def __init__(self, config: Dict, exchanges: Dict, min_order_detector=None):
        self.logger = get_logger(self.__class__.__name__)
        self.config = config
        self.exchanges = exchanges
        self.min_order_detector = min_order_detector
        self.last_preparation_time = {}  # 记录上次准备时间，用于防止频繁调用
        self.preparation_cache = {}      # 参数缓存，避免重复计算

        # 🔥 统一获取TradingRulesPreloader实例，避免重复调用
        from core.trading_rules_preloader import get_trading_rules_preloader
        self.rules_preloader = get_trading_rules_preloader()

        # 🔥 删除重复配置读取：使用配置管理器
        self.max_preparation_time_ms = 25  # 参数准备最大时间25ms
        self.depth_cache_time_ms = 100     # 深度数据缓存时间100ms

        try:
            from config.settings import get_config
            settings = get_config()
            self.min_order_amount_usd = settings.MIN_ORDER_AMOUNT_USD
        except:
            self.min_order_amount_usd = float(self.config.get('MIN_ORDER_AMOUNT_USD', '50.0'))

        self.logger.info("ExecutionParamsPreparer initialized")

    async def prepare_execution_params(self,
                                     opportunity: "ArbitrageOpportunity",
                                     prefetched_spot_depth: Optional[Dict] = None,
                                     prefetched_futures_depth: Optional[Dict] = None) -> Optional[Dict]:
        """准备执行参数 - 支持预获取的深度数据"""
        start_time = time.time()
        try:
            # 🚨 严格验证base_amount - 防止None或无效值
            base_amount = opportunity.base_amount

            if base_amount is None:
                self.logger.error("ExecutionParamsPreparer: opportunity.base_amount为None")
                return None

            try:
                base_amount = float(base_amount)
                if base_amount <= 0 or base_amount != base_amount:  # 检查NaN
                    self.logger.error(f"ExecutionParamsPreparer: base_amount={base_amount} 无效（<=0或NaN）")
                    return None
            except (ValueError, TypeError, OverflowError) as e:
                self.logger.error(f"ExecutionParamsPreparer: base_amount无法转换为float {base_amount}, 错误: {e}")
                return None

            # 🔥 删除硬编码精度配置：直接使用原始数量，依赖重试机制
            symbol = opportunity.symbol
            spot_price = opportunity.exchange1_price
            
            if not spot_price or spot_price <= 0:
                self.logger.error(f"ExecutionParamsPreparer: 无效的spot_price: {spot_price}")
                return None

            # 🔥 删除硬编码精度配置：直接使用原始数量，依赖重试机制
            try:
                # 🔥 统一精度：价格使用8位小数
                self.logger.info(f"ExecutionParamsPreparer跳过精度格式化: {symbol} 价格=${spot_price:.8f} (使用重试机制)")

                # 🔥 修复：严格按照.env配置，不允许随意放大订单金额
                current_order_value = base_amount * spot_price
                min_order_usd = self.min_order_amount_usd

                # 🔥 统一精度：金额使用8位小数
                self.logger.info(f"🔍 订单金额检查: 当前=${current_order_value:.8f}, 配置最小=${min_order_usd:.8f}")

                # 🔥 关键修复：严格使用OpportunityScanner计算的数量，不进行放大
                # OpportunityScanner已经按照.env配置计算了正确的数量
                # ExecutionParamsPreparer不应该重新计算或放大数量
                if current_order_value >= min_order_usd * 0.95:  # 允许5%的误差
                    # 数量合理，直接使用
                    optimized_amount = base_amount
                    self.logger.info(f"✅ 使用OpportunityScanner计算的数量: {base_amount:.6f} (价值${current_order_value:.2f})")
                else:
                    # 数量过小，按配置调整（但这种情况不应该发生）
                    optimized_amount = min_order_usd / spot_price
                    self.logger.warning(f"⚠️ 数量过小，按配置调整: {base_amount:.6f} -> {optimized_amount:.6f}")
                    self.logger.warning(f"   这种情况不应该发生，请检查OpportunityScanner的计算逻辑")
                
                # 🔥 修复：简化API最小量检查，避免过度放大订单
                # 原则：严格按照.env配置，只在必要时进行最小调整

                try:
                    # 获取现货和期货的交易规则
                    spot_exchange = opportunity.buy_exchange
                    futures_exchange = opportunity.sell_exchange

                    # 🔥 使用实例变量，避免重复调用
                    # 获取现货交易规则 - 🔥 修复参数顺序
                    spot_rule = self.rules_preloader.get_trading_rule(spot_exchange, symbol, "spot")
                    futures_rule = self.rules_preloader.get_trading_rule(futures_exchange, symbol, "futures")

                    if spot_rule and futures_rule:
                        # 获取最严格的最小量要求
                        spot_min = float(spot_rule.min_qty)
                        futures_min = float(futures_rule.min_qty)
                        api_min_amount = max(spot_min, futures_min)

                        self.logger.info(f"🔍 {symbol}API最小量检查: 现货={spot_min}, 期货={futures_min}, 使用={api_min_amount}")

                        if optimized_amount >= api_min_amount:
                            # 数量合规，直接使用
                            final_amount = optimized_amount
                            self.logger.info(f"✅ 使用计算数量 {optimized_amount:.6f} (符合API最小量要求)")
                        else:
                            # 🔥 关键修复：只在必要时调整到API最小量，并警告用户
                            final_amount = api_min_amount
                            final_value = final_amount * spot_price
                            config_value = self.min_order_amount_usd

                            self.logger.warning(f"⚠️ API最小量调整: {optimized_amount:.6f} -> {final_amount:.6f}")
                            self.logger.warning(f"   订单价值: ${config_value:.2f} -> ${final_value:.2f}")

                            # 如果调整后的金额超过配置太多，记录警告
                            if final_value > config_value * 1.5:  # 超过50%
                                self.logger.error(f"❌ API最小量过大！调整后金额${final_value:.2f}超过配置${config_value:.2f}的50%")
                                self.logger.error(f"   建议检查交易所API返回的最小量是否正确")
                    else:
                        # 无法获取交易规则，使用计算数量
                        final_amount = optimized_amount
                        self.logger.warning(f"⚠️ 无法获取交易规则，使用计算数量: {optimized_amount:.6f}")

                except Exception as e:
                    # API获取失败时的降级策略：严格使用计算数量，不放大
                    self.logger.warning(f"API最小量获取失败，使用计算数量: {e}")
                    final_amount = optimized_amount
                    self.logger.info(f"🔧 降级策略: 使用计算数量 {final_amount:.6f} (不放大)")

                total_amount = final_amount

            except Exception as calc_error:
                self.logger.warning(f"ExecutionParamsPreparer统一精度配置失败，使用兜底处理: {calc_error}")
                # 兜底处理：基础调整
                min_order_amount_usd = self.min_order_amount_usd
                current_order_value = base_amount * spot_price
                
                if current_order_value < min_order_amount_usd:
                    adjusted_amount = min_order_amount_usd / spot_price
                    # 🔥 统一精度：金额使用8位小数
                    self.logger.warning(f"ExecutionParamsPreparer兜底调整: ${current_order_value:.8f} -> ${min_order_amount_usd:.8f}")
                    total_amount = adjusted_amount
                else:
                    total_amount = base_amount

            # 🔥 统一精度：数量和金额都使用8位小数
            self.logger.info(f"ExecutionParamsPreparer: 最终数量={total_amount:.8f}, 订单价值=${total_amount * spot_price:.8f}")

            # 🔥 修复：强制使用预获取的深度数据，删除重复获取逻辑
            if not prefetched_spot_depth or not prefetched_futures_depth:
                self.logger.error("❌ 预获取深度数据缺失，ExecutionEngine应该已经预获取！")
                return None

            self.logger.info("✅ 使用ExecutionEngine预获取的深度数据，避免重复API调用")
            spot_depth = prefetched_spot_depth
            futures_depth = prefetched_futures_depth

            # 🔥 完美修复：使用统一深度分析器，遵循"前10档实时分析"设计
            # 现货买入 → 检查现货卖单深度（asks）
            # 期货卖出 → 检查期货买单深度（bids）

            # 使用统一深度分析器进行双市场深度分析
            from core.unified_depth_analyzer import get_depth_analyzer
            depth_analyzer = get_depth_analyzer()

            spot_result, futures_result = depth_analyzer.analyze_dual_orderbook_depth(
                spot_depth, futures_depth, total_amount, total_amount, spot_price, spot_price
            )

            # 🔥 修复：严格深度验证，任何一个市场深度不足都跳过，遵循MD文档01设计
            if not spot_result.is_sufficient or not futures_result.is_sufficient:
                error_details = []
                if not spot_result.is_sufficient:
                    error_details.append(f"现货:{spot_result.error_message}")
                if not futures_result.is_sufficient:
                    error_details.append(f"期货:{futures_result.error_message}")

                self.logger.info(f"深度不足，跳过套利: {', '.join(error_details)}")
                return None

            # 🔥 强制单笔执行，遵循"删除拆单策略：强制单笔执行"设计
            split_amounts = [total_amount]  # 只有一个数量，强制单笔执行

            # 组装执行参数
            params = {
                'total_amount': total_amount,
                'split_amounts': split_amounts,
                'spot_price': opportunity.exchange1_price,
                'futures_price': opportunity.exchange2_price,
                'spot_depth': spot_depth,
                'futures_depth': futures_depth,
                'futures_exchange': opportunity.sell_exchange,
                'spot_exchange': opportunity.buy_exchange,
                'symbol': opportunity.symbol,
                'futures_amount': total_amount,
                'buy_market': opportunity.buy_market,
                'sell_market': opportunity.sell_market
            }

            self.logger.info(f"Execution params prepared: splits={len(split_amounts)}, total={total_amount:.6f}, value=${total_amount * spot_price:.2f}")
            return params

        except Exception as e:
            self.logger.error(f"Error preparing execution params: {e}")
            return None

    # 🔥 删除：_get_orderbook_depth方法已删除，避免重复API调用
    # ExecutionEngine已经预获取了深度数据，不需要重复获取

    # 🔥 完美修复：删除重复的深度分析逻辑，统一使用UnifiedDepthAnalyzer


if __name__ == '__main__':
    """单独运行测试"""
    async def test_params_preparer():
        print("🧪 测试执行参数准备器...")
        
        # 模拟配置和交易所
        config = {
            'MIN_ORDER_AMOUNT_USD': '90.0',
            'MAX_SINGLE_ORDER_USDT': '30.0'
        }
        exchanges = {}
        
        preparer = ExecutionParamsPreparer(config, exchanges)
        print("✅ 参数准备器初始化完成")

    asyncio.run(test_params_preparer()) 