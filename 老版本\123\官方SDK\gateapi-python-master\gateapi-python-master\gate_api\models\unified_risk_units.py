# coding: utf-8

"""
    Gate API v4

    Welcome to Gate.io API  APIv4 provides spot, margin and futures trading operations. There are public APIs to retrieve the real-time market statistics, and private APIs which needs authentication to trade on user's behalf.  # noqa: E501

    Contact: <EMAIL>
    Generated by: https://openapi-generator.tech
"""


import pprint
import re  # noqa: F401

import six

from gate_api.configuration import Configuration


class UnifiedRiskUnits(object):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        'user_id': 'int',
        'spot_hedge': 'bool',
        'risk_units': 'list[RiskUnits]'
    }

    attribute_map = {
        'user_id': 'user_id',
        'spot_hedge': 'spot_hedge',
        'risk_units': 'risk_units'
    }

    def __init__(self, user_id=None, spot_hedge=None, risk_units=None, local_vars_configuration=None):  # noqa: E501
        # type: (int, bool, list[RiskUnits], Configuration) -> None
        """UnifiedRiskUnits - a model defined in OpenAPI"""  # noqa: E501
        if local_vars_configuration is None:
            local_vars_configuration = Configuration()
        self.local_vars_configuration = local_vars_configuration

        self._user_id = None
        self._spot_hedge = None
        self._risk_units = None
        self.discriminator = None

        if user_id is not None:
            self.user_id = user_id
        if spot_hedge is not None:
            self.spot_hedge = spot_hedge
        if risk_units is not None:
            self.risk_units = risk_units

    @property
    def user_id(self):
        """Gets the user_id of this UnifiedRiskUnits.  # noqa: E501

        User ID  # noqa: E501

        :return: The user_id of this UnifiedRiskUnits.  # noqa: E501
        :rtype: int
        """
        return self._user_id

    @user_id.setter
    def user_id(self, user_id):
        """Sets the user_id of this UnifiedRiskUnits.

        User ID  # noqa: E501

        :param user_id: The user_id of this UnifiedRiskUnits.  # noqa: E501
        :type: int
        """

        self._user_id = user_id

    @property
    def spot_hedge(self):
        """Gets the spot_hedge of this UnifiedRiskUnits.  # noqa: E501

        Spot hedging status, true - enabled, false - not enabled.  # noqa: E501

        :return: The spot_hedge of this UnifiedRiskUnits.  # noqa: E501
        :rtype: bool
        """
        return self._spot_hedge

    @spot_hedge.setter
    def spot_hedge(self, spot_hedge):
        """Sets the spot_hedge of this UnifiedRiskUnits.

        Spot hedging status, true - enabled, false - not enabled.  # noqa: E501

        :param spot_hedge: The spot_hedge of this UnifiedRiskUnits.  # noqa: E501
        :type: bool
        """

        self._spot_hedge = spot_hedge

    @property
    def risk_units(self):
        """Gets the risk_units of this UnifiedRiskUnits.  # noqa: E501

        Risk unit  # noqa: E501

        :return: The risk_units of this UnifiedRiskUnits.  # noqa: E501
        :rtype: list[RiskUnits]
        """
        return self._risk_units

    @risk_units.setter
    def risk_units(self, risk_units):
        """Sets the risk_units of this UnifiedRiskUnits.

        Risk unit  # noqa: E501

        :param risk_units: The risk_units of this UnifiedRiskUnits.  # noqa: E501
        :type: list[RiskUnits]
        """

        self._risk_units = risk_units

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.openapi_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, UnifiedRiskUnits):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, UnifiedRiskUnits):
            return True

        return self.to_dict() != other.to_dict()
