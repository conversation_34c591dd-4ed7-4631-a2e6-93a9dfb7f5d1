# coding: utf-8

"""
    Gate API v4

    Welcome to Gate.io API  APIv4 provides spot, margin and futures trading operations. There are public APIs to retrieve the real-time market statistics, and private APIs which needs authentication to trade on user's behalf.  # noqa: E501

    Contact: <EMAIL>
    Generated by: https://openapi-generator.tech
"""


import pprint
import re  # noqa: F401

import six

from gate_api.configuration import Configuration


class FuturesAutoDeleverage(object):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        'time': 'int',
        'user': 'int',
        'order_id': 'int',
        'contract': 'str',
        'leverage': 'str',
        'cross_leverage_limit': 'str',
        'entry_price': 'str',
        'fill_price': 'str',
        'trade_size': 'int',
        'position_size': 'int'
    }

    attribute_map = {
        'time': 'time',
        'user': 'user',
        'order_id': 'order_id',
        'contract': 'contract',
        'leverage': 'leverage',
        'cross_leverage_limit': 'cross_leverage_limit',
        'entry_price': 'entry_price',
        'fill_price': 'fill_price',
        'trade_size': 'trade_size',
        'position_size': 'position_size'
    }

    def __init__(self, time=None, user=None, order_id=None, contract=None, leverage=None, cross_leverage_limit=None, entry_price=None, fill_price=None, trade_size=None, position_size=None, local_vars_configuration=None):  # noqa: E501
        # type: (int, int, int, str, str, str, str, str, int, int, Configuration) -> None
        """FuturesAutoDeleverage - a model defined in OpenAPI"""  # noqa: E501
        if local_vars_configuration is None:
            local_vars_configuration = Configuration()
        self.local_vars_configuration = local_vars_configuration

        self._time = None
        self._user = None
        self._order_id = None
        self._contract = None
        self._leverage = None
        self._cross_leverage_limit = None
        self._entry_price = None
        self._fill_price = None
        self._trade_size = None
        self._position_size = None
        self.discriminator = None

        if time is not None:
            self.time = time
        if user is not None:
            self.user = user
        if order_id is not None:
            self.order_id = order_id
        if contract is not None:
            self.contract = contract
        if leverage is not None:
            self.leverage = leverage
        if cross_leverage_limit is not None:
            self.cross_leverage_limit = cross_leverage_limit
        if entry_price is not None:
            self.entry_price = entry_price
        if fill_price is not None:
            self.fill_price = fill_price
        if trade_size is not None:
            self.trade_size = trade_size
        if position_size is not None:
            self.position_size = position_size

    @property
    def time(self):
        """Gets the time of this FuturesAutoDeleverage.  # noqa: E501

        Automatic deleveraging time  # noqa: E501

        :return: The time of this FuturesAutoDeleverage.  # noqa: E501
        :rtype: int
        """
        return self._time

    @time.setter
    def time(self, time):
        """Sets the time of this FuturesAutoDeleverage.

        Automatic deleveraging time  # noqa: E501

        :param time: The time of this FuturesAutoDeleverage.  # noqa: E501
        :type: int
        """

        self._time = time

    @property
    def user(self):
        """Gets the user of this FuturesAutoDeleverage.  # noqa: E501

        User ID  # noqa: E501

        :return: The user of this FuturesAutoDeleverage.  # noqa: E501
        :rtype: int
        """
        return self._user

    @user.setter
    def user(self, user):
        """Sets the user of this FuturesAutoDeleverage.

        User ID  # noqa: E501

        :param user: The user of this FuturesAutoDeleverage.  # noqa: E501
        :type: int
        """

        self._user = user

    @property
    def order_id(self):
        """Gets the order_id of this FuturesAutoDeleverage.  # noqa: E501

        Order ID. Order IDs before 2023-02-20 are null  # noqa: E501

        :return: The order_id of this FuturesAutoDeleverage.  # noqa: E501
        :rtype: int
        """
        return self._order_id

    @order_id.setter
    def order_id(self, order_id):
        """Sets the order_id of this FuturesAutoDeleverage.

        Order ID. Order IDs before 2023-02-20 are null  # noqa: E501

        :param order_id: The order_id of this FuturesAutoDeleverage.  # noqa: E501
        :type: int
        """

        self._order_id = order_id

    @property
    def contract(self):
        """Gets the contract of this FuturesAutoDeleverage.  # noqa: E501

        Futures contract  # noqa: E501

        :return: The contract of this FuturesAutoDeleverage.  # noqa: E501
        :rtype: str
        """
        return self._contract

    @contract.setter
    def contract(self, contract):
        """Sets the contract of this FuturesAutoDeleverage.

        Futures contract  # noqa: E501

        :param contract: The contract of this FuturesAutoDeleverage.  # noqa: E501
        :type: str
        """

        self._contract = contract

    @property
    def leverage(self):
        """Gets the leverage of this FuturesAutoDeleverage.  # noqa: E501

        Position leverage  # noqa: E501

        :return: The leverage of this FuturesAutoDeleverage.  # noqa: E501
        :rtype: str
        """
        return self._leverage

    @leverage.setter
    def leverage(self, leverage):
        """Sets the leverage of this FuturesAutoDeleverage.

        Position leverage  # noqa: E501

        :param leverage: The leverage of this FuturesAutoDeleverage.  # noqa: E501
        :type: str
        """

        self._leverage = leverage

    @property
    def cross_leverage_limit(self):
        """Gets the cross_leverage_limit of this FuturesAutoDeleverage.  # noqa: E501

        Cross margin leverage(valid only when `leverage` is 0)  # noqa: E501

        :return: The cross_leverage_limit of this FuturesAutoDeleverage.  # noqa: E501
        :rtype: str
        """
        return self._cross_leverage_limit

    @cross_leverage_limit.setter
    def cross_leverage_limit(self, cross_leverage_limit):
        """Sets the cross_leverage_limit of this FuturesAutoDeleverage.

        Cross margin leverage(valid only when `leverage` is 0)  # noqa: E501

        :param cross_leverage_limit: The cross_leverage_limit of this FuturesAutoDeleverage.  # noqa: E501
        :type: str
        """

        self._cross_leverage_limit = cross_leverage_limit

    @property
    def entry_price(self):
        """Gets the entry_price of this FuturesAutoDeleverage.  # noqa: E501

        Average entry price  # noqa: E501

        :return: The entry_price of this FuturesAutoDeleverage.  # noqa: E501
        :rtype: str
        """
        return self._entry_price

    @entry_price.setter
    def entry_price(self, entry_price):
        """Sets the entry_price of this FuturesAutoDeleverage.

        Average entry price  # noqa: E501

        :param entry_price: The entry_price of this FuturesAutoDeleverage.  # noqa: E501
        :type: str
        """

        self._entry_price = entry_price

    @property
    def fill_price(self):
        """Gets the fill_price of this FuturesAutoDeleverage.  # noqa: E501

        Average fill price  # noqa: E501

        :return: The fill_price of this FuturesAutoDeleverage.  # noqa: E501
        :rtype: str
        """
        return self._fill_price

    @fill_price.setter
    def fill_price(self, fill_price):
        """Sets the fill_price of this FuturesAutoDeleverage.

        Average fill price  # noqa: E501

        :param fill_price: The fill_price of this FuturesAutoDeleverage.  # noqa: E501
        :type: str
        """

        self._fill_price = fill_price

    @property
    def trade_size(self):
        """Gets the trade_size of this FuturesAutoDeleverage.  # noqa: E501

        Trading size  # noqa: E501

        :return: The trade_size of this FuturesAutoDeleverage.  # noqa: E501
        :rtype: int
        """
        return self._trade_size

    @trade_size.setter
    def trade_size(self, trade_size):
        """Sets the trade_size of this FuturesAutoDeleverage.

        Trading size  # noqa: E501

        :param trade_size: The trade_size of this FuturesAutoDeleverage.  # noqa: E501
        :type: int
        """

        self._trade_size = trade_size

    @property
    def position_size(self):
        """Gets the position_size of this FuturesAutoDeleverage.  # noqa: E501

        Positions after auto-deleveraging  # noqa: E501

        :return: The position_size of this FuturesAutoDeleverage.  # noqa: E501
        :rtype: int
        """
        return self._position_size

    @position_size.setter
    def position_size(self, position_size):
        """Sets the position_size of this FuturesAutoDeleverage.

        Positions after auto-deleveraging  # noqa: E501

        :param position_size: The position_size of this FuturesAutoDeleverage.  # noqa: E501
        :type: int
        """

        self._position_size = position_size

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.openapi_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, FuturesAutoDeleverage):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, FuturesAutoDeleverage):
            return True

        return self.to_dict() != other.to_dict()
