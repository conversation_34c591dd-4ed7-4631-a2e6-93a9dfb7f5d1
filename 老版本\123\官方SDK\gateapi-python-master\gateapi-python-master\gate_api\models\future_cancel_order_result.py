# coding: utf-8

"""
    Gate API v4

    Welcome to Gate.io API  APIv4 provides spot, margin and futures trading operations. There are public APIs to retrieve the real-time market statistics, and private APIs which needs authentication to trade on user's behalf.  # noqa: E501

    Contact: <EMAIL>
    Generated by: https://openapi-generator.tech
"""


import pprint
import re  # noqa: F401

import six

from gate_api.configuration import Configuration


class FutureCancelOrderResult(object):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        'id': 'str',
        'user_id': 'int',
        'succeeded': 'bool',
        'message': 'str'
    }

    attribute_map = {
        'id': 'id',
        'user_id': 'user_id',
        'succeeded': 'succeeded',
        'message': 'message'
    }

    def __init__(self, id=None, user_id=None, succeeded=None, message=None, local_vars_configuration=None):  # noqa: E501
        # type: (str, int, bool, str, Configuration) -> None
        """FutureCancelOrderResult - a model defined in OpenAPI"""  # noqa: E501
        if local_vars_configuration is None:
            local_vars_configuration = Configuration()
        self.local_vars_configuration = local_vars_configuration

        self._id = None
        self._user_id = None
        self._succeeded = None
        self._message = None
        self.discriminator = None

        if id is not None:
            self.id = id
        if user_id is not None:
            self.user_id = user_id
        if succeeded is not None:
            self.succeeded = succeeded
        if message is not None:
            self.message = message

    @property
    def id(self):
        """Gets the id of this FutureCancelOrderResult.  # noqa: E501

        Order ID  # noqa: E501

        :return: The id of this FutureCancelOrderResult.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this FutureCancelOrderResult.

        Order ID  # noqa: E501

        :param id: The id of this FutureCancelOrderResult.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def user_id(self):
        """Gets the user_id of this FutureCancelOrderResult.  # noqa: E501

        User ID  # noqa: E501

        :return: The user_id of this FutureCancelOrderResult.  # noqa: E501
        :rtype: int
        """
        return self._user_id

    @user_id.setter
    def user_id(self, user_id):
        """Sets the user_id of this FutureCancelOrderResult.

        User ID  # noqa: E501

        :param user_id: The user_id of this FutureCancelOrderResult.  # noqa: E501
        :type: int
        """

        self._user_id = user_id

    @property
    def succeeded(self):
        """Gets the succeeded of this FutureCancelOrderResult.  # noqa: E501

        Whether cancellation succeeded  # noqa: E501

        :return: The succeeded of this FutureCancelOrderResult.  # noqa: E501
        :rtype: bool
        """
        return self._succeeded

    @succeeded.setter
    def succeeded(self, succeeded):
        """Sets the succeeded of this FutureCancelOrderResult.

        Whether cancellation succeeded  # noqa: E501

        :param succeeded: The succeeded of this FutureCancelOrderResult.  # noqa: E501
        :type: bool
        """

        self._succeeded = succeeded

    @property
    def message(self):
        """Gets the message of this FutureCancelOrderResult.  # noqa: E501

        Error message when failed to cancel the order; empty if succeeded  # noqa: E501

        :return: The message of this FutureCancelOrderResult.  # noqa: E501
        :rtype: str
        """
        return self._message

    @message.setter
    def message(self, message):
        """Sets the message of this FutureCancelOrderResult.

        Error message when failed to cancel the order; empty if succeeded  # noqa: E501

        :param message: The message of this FutureCancelOrderResult.  # noqa: E501
        :type: str
        """

        self._message = message

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.openapi_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, FutureCancelOrderResult):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, FutureCancelOrderResult):
            return True

        return self.to_dict() != other.to_dict()
