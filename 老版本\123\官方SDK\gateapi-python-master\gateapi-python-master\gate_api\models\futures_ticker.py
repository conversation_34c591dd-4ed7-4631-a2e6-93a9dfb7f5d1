# coding: utf-8

"""
    Gate API v4

    Welcome to Gate.io API  APIv4 provides spot, margin and futures trading operations. There are public APIs to retrieve the real-time market statistics, and private APIs which needs authentication to trade on user's behalf.  # noqa: E501

    Contact: <EMAIL>
    Generated by: https://openapi-generator.tech
"""


import pprint
import re  # noqa: F401

import six

from gate_api.configuration import Configuration


class FuturesTicker(object):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        'contract': 'str',
        'last': 'str',
        'change_percentage': 'str',
        'total_size': 'str',
        'low_24h': 'str',
        'high_24h': 'str',
        'volume_24h': 'str',
        'volume_24h_btc': 'str',
        'volume_24h_usd': 'str',
        'volume_24h_base': 'str',
        'volume_24h_quote': 'str',
        'volume_24h_settle': 'str',
        'mark_price': 'str',
        'funding_rate': 'str',
        'funding_rate_indicative': 'str',
        'index_price': 'str',
        'quanto_base_rate': 'str',
        'basis_rate': 'str',
        'basis_value': 'str',
        'lowest_ask': 'str',
        'lowest_size': 'str',
        'highest_bid': 'str',
        'highest_size': 'str'
    }

    attribute_map = {
        'contract': 'contract',
        'last': 'last',
        'change_percentage': 'change_percentage',
        'total_size': 'total_size',
        'low_24h': 'low_24h',
        'high_24h': 'high_24h',
        'volume_24h': 'volume_24h',
        'volume_24h_btc': 'volume_24h_btc',
        'volume_24h_usd': 'volume_24h_usd',
        'volume_24h_base': 'volume_24h_base',
        'volume_24h_quote': 'volume_24h_quote',
        'volume_24h_settle': 'volume_24h_settle',
        'mark_price': 'mark_price',
        'funding_rate': 'funding_rate',
        'funding_rate_indicative': 'funding_rate_indicative',
        'index_price': 'index_price',
        'quanto_base_rate': 'quanto_base_rate',
        'basis_rate': 'basis_rate',
        'basis_value': 'basis_value',
        'lowest_ask': 'lowest_ask',
        'lowest_size': 'lowest_size',
        'highest_bid': 'highest_bid',
        'highest_size': 'highest_size'
    }

    def __init__(self, contract=None, last=None, change_percentage=None, total_size=None, low_24h=None, high_24h=None, volume_24h=None, volume_24h_btc=None, volume_24h_usd=None, volume_24h_base=None, volume_24h_quote=None, volume_24h_settle=None, mark_price=None, funding_rate=None, funding_rate_indicative=None, index_price=None, quanto_base_rate=None, basis_rate=None, basis_value=None, lowest_ask=None, lowest_size=None, highest_bid=None, highest_size=None, local_vars_configuration=None):  # noqa: E501
        # type: (str, str, str, str, str, str, str, str, str, str, str, str, str, str, str, str, str, str, str, str, str, str, str, Configuration) -> None
        """FuturesTicker - a model defined in OpenAPI"""  # noqa: E501
        if local_vars_configuration is None:
            local_vars_configuration = Configuration()
        self.local_vars_configuration = local_vars_configuration

        self._contract = None
        self._last = None
        self._change_percentage = None
        self._total_size = None
        self._low_24h = None
        self._high_24h = None
        self._volume_24h = None
        self._volume_24h_btc = None
        self._volume_24h_usd = None
        self._volume_24h_base = None
        self._volume_24h_quote = None
        self._volume_24h_settle = None
        self._mark_price = None
        self._funding_rate = None
        self._funding_rate_indicative = None
        self._index_price = None
        self._quanto_base_rate = None
        self._basis_rate = None
        self._basis_value = None
        self._lowest_ask = None
        self._lowest_size = None
        self._highest_bid = None
        self._highest_size = None
        self.discriminator = None

        if contract is not None:
            self.contract = contract
        if last is not None:
            self.last = last
        if change_percentage is not None:
            self.change_percentage = change_percentage
        if total_size is not None:
            self.total_size = total_size
        if low_24h is not None:
            self.low_24h = low_24h
        if high_24h is not None:
            self.high_24h = high_24h
        if volume_24h is not None:
            self.volume_24h = volume_24h
        if volume_24h_btc is not None:
            self.volume_24h_btc = volume_24h_btc
        if volume_24h_usd is not None:
            self.volume_24h_usd = volume_24h_usd
        if volume_24h_base is not None:
            self.volume_24h_base = volume_24h_base
        if volume_24h_quote is not None:
            self.volume_24h_quote = volume_24h_quote
        if volume_24h_settle is not None:
            self.volume_24h_settle = volume_24h_settle
        if mark_price is not None:
            self.mark_price = mark_price
        if funding_rate is not None:
            self.funding_rate = funding_rate
        if funding_rate_indicative is not None:
            self.funding_rate_indicative = funding_rate_indicative
        if index_price is not None:
            self.index_price = index_price
        if quanto_base_rate is not None:
            self.quanto_base_rate = quanto_base_rate
        if basis_rate is not None:
            self.basis_rate = basis_rate
        if basis_value is not None:
            self.basis_value = basis_value
        if lowest_ask is not None:
            self.lowest_ask = lowest_ask
        if lowest_size is not None:
            self.lowest_size = lowest_size
        if highest_bid is not None:
            self.highest_bid = highest_bid
        if highest_size is not None:
            self.highest_size = highest_size

    @property
    def contract(self):
        """Gets the contract of this FuturesTicker.  # noqa: E501

        Futures contract  # noqa: E501

        :return: The contract of this FuturesTicker.  # noqa: E501
        :rtype: str
        """
        return self._contract

    @contract.setter
    def contract(self, contract):
        """Sets the contract of this FuturesTicker.

        Futures contract  # noqa: E501

        :param contract: The contract of this FuturesTicker.  # noqa: E501
        :type: str
        """

        self._contract = contract

    @property
    def last(self):
        """Gets the last of this FuturesTicker.  # noqa: E501

        Last trading price  # noqa: E501

        :return: The last of this FuturesTicker.  # noqa: E501
        :rtype: str
        """
        return self._last

    @last.setter
    def last(self, last):
        """Sets the last of this FuturesTicker.

        Last trading price  # noqa: E501

        :param last: The last of this FuturesTicker.  # noqa: E501
        :type: str
        """

        self._last = last

    @property
    def change_percentage(self):
        """Gets the change_percentage of this FuturesTicker.  # noqa: E501

        Change percentage.  # noqa: E501

        :return: The change_percentage of this FuturesTicker.  # noqa: E501
        :rtype: str
        """
        return self._change_percentage

    @change_percentage.setter
    def change_percentage(self, change_percentage):
        """Sets the change_percentage of this FuturesTicker.

        Change percentage.  # noqa: E501

        :param change_percentage: The change_percentage of this FuturesTicker.  # noqa: E501
        :type: str
        """

        self._change_percentage = change_percentage

    @property
    def total_size(self):
        """Gets the total_size of this FuturesTicker.  # noqa: E501

        Contract total size  # noqa: E501

        :return: The total_size of this FuturesTicker.  # noqa: E501
        :rtype: str
        """
        return self._total_size

    @total_size.setter
    def total_size(self, total_size):
        """Sets the total_size of this FuturesTicker.

        Contract total size  # noqa: E501

        :param total_size: The total_size of this FuturesTicker.  # noqa: E501
        :type: str
        """

        self._total_size = total_size

    @property
    def low_24h(self):
        """Gets the low_24h of this FuturesTicker.  # noqa: E501

        Lowest trading price in recent 24h  # noqa: E501

        :return: The low_24h of this FuturesTicker.  # noqa: E501
        :rtype: str
        """
        return self._low_24h

    @low_24h.setter
    def low_24h(self, low_24h):
        """Sets the low_24h of this FuturesTicker.

        Lowest trading price in recent 24h  # noqa: E501

        :param low_24h: The low_24h of this FuturesTicker.  # noqa: E501
        :type: str
        """

        self._low_24h = low_24h

    @property
    def high_24h(self):
        """Gets the high_24h of this FuturesTicker.  # noqa: E501

        Highest trading price in recent 24h  # noqa: E501

        :return: The high_24h of this FuturesTicker.  # noqa: E501
        :rtype: str
        """
        return self._high_24h

    @high_24h.setter
    def high_24h(self, high_24h):
        """Sets the high_24h of this FuturesTicker.

        Highest trading price in recent 24h  # noqa: E501

        :param high_24h: The high_24h of this FuturesTicker.  # noqa: E501
        :type: str
        """

        self._high_24h = high_24h

    @property
    def volume_24h(self):
        """Gets the volume_24h of this FuturesTicker.  # noqa: E501

        Trade size in recent 24h  # noqa: E501

        :return: The volume_24h of this FuturesTicker.  # noqa: E501
        :rtype: str
        """
        return self._volume_24h

    @volume_24h.setter
    def volume_24h(self, volume_24h):
        """Sets the volume_24h of this FuturesTicker.

        Trade size in recent 24h  # noqa: E501

        :param volume_24h: The volume_24h of this FuturesTicker.  # noqa: E501
        :type: str
        """

        self._volume_24h = volume_24h

    @property
    def volume_24h_btc(self):
        """Gets the volume_24h_btc of this FuturesTicker.  # noqa: E501

        Trade volumes in recent 24h in BTC(deprecated, use `volume_24h_base`, `volume_24h_quote`, `volume_24h_settle` instead)  # noqa: E501

        :return: The volume_24h_btc of this FuturesTicker.  # noqa: E501
        :rtype: str
        """
        return self._volume_24h_btc

    @volume_24h_btc.setter
    def volume_24h_btc(self, volume_24h_btc):
        """Sets the volume_24h_btc of this FuturesTicker.

        Trade volumes in recent 24h in BTC(deprecated, use `volume_24h_base`, `volume_24h_quote`, `volume_24h_settle` instead)  # noqa: E501

        :param volume_24h_btc: The volume_24h_btc of this FuturesTicker.  # noqa: E501
        :type: str
        """

        self._volume_24h_btc = volume_24h_btc

    @property
    def volume_24h_usd(self):
        """Gets the volume_24h_usd of this FuturesTicker.  # noqa: E501

        Trade volumes in recent 24h in USD(deprecated, use `volume_24h_base`, `volume_24h_quote`, `volume_24h_settle` instead)  # noqa: E501

        :return: The volume_24h_usd of this FuturesTicker.  # noqa: E501
        :rtype: str
        """
        return self._volume_24h_usd

    @volume_24h_usd.setter
    def volume_24h_usd(self, volume_24h_usd):
        """Sets the volume_24h_usd of this FuturesTicker.

        Trade volumes in recent 24h in USD(deprecated, use `volume_24h_base`, `volume_24h_quote`, `volume_24h_settle` instead)  # noqa: E501

        :param volume_24h_usd: The volume_24h_usd of this FuturesTicker.  # noqa: E501
        :type: str
        """

        self._volume_24h_usd = volume_24h_usd

    @property
    def volume_24h_base(self):
        """Gets the volume_24h_base of this FuturesTicker.  # noqa: E501

        Trade volume in recent 24h, in base currency  # noqa: E501

        :return: The volume_24h_base of this FuturesTicker.  # noqa: E501
        :rtype: str
        """
        return self._volume_24h_base

    @volume_24h_base.setter
    def volume_24h_base(self, volume_24h_base):
        """Sets the volume_24h_base of this FuturesTicker.

        Trade volume in recent 24h, in base currency  # noqa: E501

        :param volume_24h_base: The volume_24h_base of this FuturesTicker.  # noqa: E501
        :type: str
        """

        self._volume_24h_base = volume_24h_base

    @property
    def volume_24h_quote(self):
        """Gets the volume_24h_quote of this FuturesTicker.  # noqa: E501

        Trade volume in recent 24h, in quote currency  # noqa: E501

        :return: The volume_24h_quote of this FuturesTicker.  # noqa: E501
        :rtype: str
        """
        return self._volume_24h_quote

    @volume_24h_quote.setter
    def volume_24h_quote(self, volume_24h_quote):
        """Sets the volume_24h_quote of this FuturesTicker.

        Trade volume in recent 24h, in quote currency  # noqa: E501

        :param volume_24h_quote: The volume_24h_quote of this FuturesTicker.  # noqa: E501
        :type: str
        """

        self._volume_24h_quote = volume_24h_quote

    @property
    def volume_24h_settle(self):
        """Gets the volume_24h_settle of this FuturesTicker.  # noqa: E501

        Trade volume in recent 24h, in settle currency  # noqa: E501

        :return: The volume_24h_settle of this FuturesTicker.  # noqa: E501
        :rtype: str
        """
        return self._volume_24h_settle

    @volume_24h_settle.setter
    def volume_24h_settle(self, volume_24h_settle):
        """Sets the volume_24h_settle of this FuturesTicker.

        Trade volume in recent 24h, in settle currency  # noqa: E501

        :param volume_24h_settle: The volume_24h_settle of this FuturesTicker.  # noqa: E501
        :type: str
        """

        self._volume_24h_settle = volume_24h_settle

    @property
    def mark_price(self):
        """Gets the mark_price of this FuturesTicker.  # noqa: E501

        Recent mark price  # noqa: E501

        :return: The mark_price of this FuturesTicker.  # noqa: E501
        :rtype: str
        """
        return self._mark_price

    @mark_price.setter
    def mark_price(self, mark_price):
        """Sets the mark_price of this FuturesTicker.

        Recent mark price  # noqa: E501

        :param mark_price: The mark_price of this FuturesTicker.  # noqa: E501
        :type: str
        """

        self._mark_price = mark_price

    @property
    def funding_rate(self):
        """Gets the funding_rate of this FuturesTicker.  # noqa: E501

        Funding rate  # noqa: E501

        :return: The funding_rate of this FuturesTicker.  # noqa: E501
        :rtype: str
        """
        return self._funding_rate

    @funding_rate.setter
    def funding_rate(self, funding_rate):
        """Sets the funding_rate of this FuturesTicker.

        Funding rate  # noqa: E501

        :param funding_rate: The funding_rate of this FuturesTicker.  # noqa: E501
        :type: str
        """

        self._funding_rate = funding_rate

    @property
    def funding_rate_indicative(self):
        """Gets the funding_rate_indicative of this FuturesTicker.  # noqa: E501

        Indicative Funding rate in next period. (deprecated. use `funding_rate`)  # noqa: E501

        :return: The funding_rate_indicative of this FuturesTicker.  # noqa: E501
        :rtype: str
        """
        return self._funding_rate_indicative

    @funding_rate_indicative.setter
    def funding_rate_indicative(self, funding_rate_indicative):
        """Sets the funding_rate_indicative of this FuturesTicker.

        Indicative Funding rate in next period. (deprecated. use `funding_rate`)  # noqa: E501

        :param funding_rate_indicative: The funding_rate_indicative of this FuturesTicker.  # noqa: E501
        :type: str
        """

        self._funding_rate_indicative = funding_rate_indicative

    @property
    def index_price(self):
        """Gets the index_price of this FuturesTicker.  # noqa: E501

        Index price  # noqa: E501

        :return: The index_price of this FuturesTicker.  # noqa: E501
        :rtype: str
        """
        return self._index_price

    @index_price.setter
    def index_price(self, index_price):
        """Sets the index_price of this FuturesTicker.

        Index price  # noqa: E501

        :param index_price: The index_price of this FuturesTicker.  # noqa: E501
        :type: str
        """

        self._index_price = index_price

    @property
    def quanto_base_rate(self):
        """Gets the quanto_base_rate of this FuturesTicker.  # noqa: E501

        Exchange rate of base currency and settlement currency in Quanto contract. Does not exists in contracts of other types  # noqa: E501

        :return: The quanto_base_rate of this FuturesTicker.  # noqa: E501
        :rtype: str
        """
        return self._quanto_base_rate

    @quanto_base_rate.setter
    def quanto_base_rate(self, quanto_base_rate):
        """Sets the quanto_base_rate of this FuturesTicker.

        Exchange rate of base currency and settlement currency in Quanto contract. Does not exists in contracts of other types  # noqa: E501

        :param quanto_base_rate: The quanto_base_rate of this FuturesTicker.  # noqa: E501
        :type: str
        """

        self._quanto_base_rate = quanto_base_rate

    @property
    def basis_rate(self):
        """Gets the basis_rate of this FuturesTicker.  # noqa: E501

        Basis rate  # noqa: E501

        :return: The basis_rate of this FuturesTicker.  # noqa: E501
        :rtype: str
        """
        return self._basis_rate

    @basis_rate.setter
    def basis_rate(self, basis_rate):
        """Sets the basis_rate of this FuturesTicker.

        Basis rate  # noqa: E501

        :param basis_rate: The basis_rate of this FuturesTicker.  # noqa: E501
        :type: str
        """

        self._basis_rate = basis_rate

    @property
    def basis_value(self):
        """Gets the basis_value of this FuturesTicker.  # noqa: E501

        Basis value  # noqa: E501

        :return: The basis_value of this FuturesTicker.  # noqa: E501
        :rtype: str
        """
        return self._basis_value

    @basis_value.setter
    def basis_value(self, basis_value):
        """Sets the basis_value of this FuturesTicker.

        Basis value  # noqa: E501

        :param basis_value: The basis_value of this FuturesTicker.  # noqa: E501
        :type: str
        """

        self._basis_value = basis_value

    @property
    def lowest_ask(self):
        """Gets the lowest_ask of this FuturesTicker.  # noqa: E501

        Recent lowest ask  # noqa: E501

        :return: The lowest_ask of this FuturesTicker.  # noqa: E501
        :rtype: str
        """
        return self._lowest_ask

    @lowest_ask.setter
    def lowest_ask(self, lowest_ask):
        """Sets the lowest_ask of this FuturesTicker.

        Recent lowest ask  # noqa: E501

        :param lowest_ask: The lowest_ask of this FuturesTicker.  # noqa: E501
        :type: str
        """

        self._lowest_ask = lowest_ask

    @property
    def lowest_size(self):
        """Gets the lowest_size of this FuturesTicker.  # noqa: E501

        The latest seller's lowest price order quantity  # noqa: E501

        :return: The lowest_size of this FuturesTicker.  # noqa: E501
        :rtype: str
        """
        return self._lowest_size

    @lowest_size.setter
    def lowest_size(self, lowest_size):
        """Sets the lowest_size of this FuturesTicker.

        The latest seller's lowest price order quantity  # noqa: E501

        :param lowest_size: The lowest_size of this FuturesTicker.  # noqa: E501
        :type: str
        """

        self._lowest_size = lowest_size

    @property
    def highest_bid(self):
        """Gets the highest_bid of this FuturesTicker.  # noqa: E501

        Recent highest bid  # noqa: E501

        :return: The highest_bid of this FuturesTicker.  # noqa: E501
        :rtype: str
        """
        return self._highest_bid

    @highest_bid.setter
    def highest_bid(self, highest_bid):
        """Sets the highest_bid of this FuturesTicker.

        Recent highest bid  # noqa: E501

        :param highest_bid: The highest_bid of this FuturesTicker.  # noqa: E501
        :type: str
        """

        self._highest_bid = highest_bid

    @property
    def highest_size(self):
        """Gets the highest_size of this FuturesTicker.  # noqa: E501

        The latest buyer's highest price order volume  # noqa: E501

        :return: The highest_size of this FuturesTicker.  # noqa: E501
        :rtype: str
        """
        return self._highest_size

    @highest_size.setter
    def highest_size(self, highest_size):
        """Sets the highest_size of this FuturesTicker.

        The latest buyer's highest price order volume  # noqa: E501

        :param highest_size: The highest_size of this FuturesTicker.  # noqa: E501
        :type: str
        """

        self._highest_size = highest_size

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.openapi_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, FuturesTicker):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, FuturesTicker):
            return True

        return self.to_dict() != other.to_dict()
