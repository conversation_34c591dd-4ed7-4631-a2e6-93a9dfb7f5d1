#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔥 API交易规则精准诊断脚本
直接调用Bybit API获取ICNT-USDT和SPK-USDT的真实交易规则
"""

import sys
import os
import json
import asyncio
import time
from typing import Dict, Any, Optional

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class APITradingRulesDiagnosis:
    def __init__(self):
        self.results = {
            "diagnosis_time": time.strftime("%Y-%m-%d %H:%M:%S"),
            "api_responses": {},
            "trading_rules": {},
            "precision_analysis": {},
            "fix_recommendations": []
        }
        
    async def get_bybit_instruments_info(self, symbol: str, category: str):
        """直接调用Bybit API获取交易对信息"""
        try:
            from exchanges.bybit_exchange import BybitExchange
            from config.exchange_config import ExchangeConfig
            import os

            # 从环境变量创建配置
            config = ExchangeConfig(
                api_key=os.getenv("BYBIT_API_KEY", ""),
                api_secret=os.getenv("BYBIT_API_SECRET", ""),
                api_passphrase="",
                is_unified_account=True
            )

            exchange = BybitExchange(config)

            # 调用API获取交易对信息
            response = await exchange.get_instruments_info(symbol, category)

            return response

        except Exception as e:
            return {"error": str(e)}
    
    async def analyze_icnt_usdt_futures(self):
        """分析ICNT-USDT期货交易规则"""
        print("🔍 分析ICNT-USDT期货交易规则...")
        
        # 获取API响应
        api_response = await self.get_bybit_instruments_info("ICNTUSDT", "linear")
        self.results["api_responses"]["ICNT-USDT_futures"] = api_response
        
        if "error" not in api_response and "result" in api_response:
            instruments = api_response["result"].get("list", [])
            if instruments:
                instrument = instruments[0]
                lot_size_filter = instrument.get("lotSizeFilter", {})
                price_filter = instrument.get("priceFilter", {})
                
                # 提取交易规则
                trading_rule = {
                    "symbol": instrument.get("symbol"),
                    "status": instrument.get("status"),
                    "qtyStep": lot_size_filter.get("qtyStep"),
                    "minOrderQty": lot_size_filter.get("minOrderQty"),
                    "maxOrderQty": lot_size_filter.get("maxOrderQty"),
                    "tickSize": price_filter.get("tickSize"),
                    "minPrice": price_filter.get("minPrice"),
                    "maxPrice": price_filter.get("maxPrice")
                }
                
                self.results["trading_rules"]["ICNT-USDT_futures"] = trading_rule
                
                # 分析153.307是否符合规则
                qty_step = float(lot_size_filter.get("qtyStep", "0.001"))
                test_quantity = 153.307
                
                from decimal import Decimal
                qty_decimal = Decimal(str(test_quantity))
                step_decimal = Decimal(str(qty_step))
                
                remainder = qty_decimal % step_decimal
                is_valid = remainder == 0
                
                if not is_valid:
                    correct_qty = float((qty_decimal // step_decimal) * step_decimal)
                else:
                    correct_qty = test_quantity
                
                self.results["precision_analysis"]["ICNT-USDT_futures"] = {
                    "original_quantity": test_quantity,
                    "qty_step": qty_step,
                    "is_valid": is_valid,
                    "remainder": float(remainder),
                    "correct_quantity": correct_qty,
                    "error_cause": "数量不是qtyStep的整数倍" if not is_valid else "数量符合规则"
                }
            else:
                self.results["trading_rules"]["ICNT-USDT_futures"] = "交易对不存在"
        else:
            self.results["trading_rules"]["ICNT-USDT_futures"] = api_response
    
    async def analyze_spk_usdt_spot(self):
        """分析SPK-USDT现货交易规则"""
        print("🔍 分析SPK-USDT现货交易规则...")
        
        # 获取API响应
        api_response = await self.get_bybit_instruments_info("SPKUSDT", "spot")
        self.results["api_responses"]["SPK-USDT_spot"] = api_response
        
        if "error" not in api_response and "result" in api_response:
            instruments = api_response["result"].get("list", [])
            if instruments:
                instrument = instruments[0]
                lot_size_filter = instrument.get("lotSizeFilter", {})
                price_filter = instrument.get("priceFilter", {})
                
                # 提取交易规则
                trading_rule = {
                    "symbol": instrument.get("symbol"),
                    "status": instrument.get("status"),
                    "basePrecision": lot_size_filter.get("basePrecision"),
                    "quotePrecision": lot_size_filter.get("quotePrecision"),
                    "minOrderQty": lot_size_filter.get("minOrderQty"),
                    "maxOrderQty": lot_size_filter.get("maxOrderQty"),
                    "minOrderAmt": lot_size_filter.get("minOrderAmt"),
                    "maxOrderAmt": lot_size_filter.get("maxOrderAmt"),
                    "tickSize": price_filter.get("tickSize"),
                    "minPrice": price_filter.get("minPrice"),
                    "maxPrice": price_filter.get("maxPrice")
                }
                
                self.results["trading_rules"]["SPK-USDT_spot"] = trading_rule
                
                # 分析321.995是否符合规则
                base_precision = lot_size_filter.get("basePrecision", "0.000001")
                test_quantity = 321.995
                
                from decimal import Decimal
                qty_decimal = Decimal(str(test_quantity))
                precision_decimal = Decimal(base_precision)
                
                remainder = qty_decimal % precision_decimal
                is_valid = remainder == 0
                
                if not is_valid:
                    correct_qty = float((qty_decimal // precision_decimal) * precision_decimal)
                else:
                    correct_qty = test_quantity
                
                # 检查小数位数
                qty_str = str(test_quantity)
                decimal_places = len(qty_str.split('.')[1]) if '.' in qty_str else 0
                
                # 计算basePrecision允许的最大小数位数
                precision_str = base_precision
                max_decimal_places = len(precision_str.split('.')[1]) if '.' in precision_str else 0
                
                self.results["precision_analysis"]["SPK-USDT_spot"] = {
                    "original_quantity": test_quantity,
                    "base_precision": base_precision,
                    "is_valid": is_valid,
                    "remainder": float(remainder),
                    "correct_quantity": correct_qty,
                    "decimal_places": decimal_places,
                    "max_allowed_decimal_places": max_decimal_places,
                    "decimal_places_exceeded": decimal_places > max_decimal_places,
                    "error_cause": "小数位数超过basePrecision允许的位数" if decimal_places > max_decimal_places else "数量符合规则"
                }
            else:
                self.results["trading_rules"]["SPK-USDT_spot"] = "交易对不存在"
        else:
            self.results["trading_rules"]["SPK-USDT_spot"] = api_response
    
    def generate_fix_recommendations(self):
        """生成具体的修复建议"""
        print("🔧 生成具体修复建议...")
        
        recommendations = []
        
        # 基于ICNT-USDT期货分析结果
        if "ICNT-USDT_futures" in self.results["precision_analysis"]:
            analysis = self.results["precision_analysis"]["ICNT-USDT_futures"]
            if not analysis["is_valid"]:
                recommendations.append({
                    "symbol": "ICNT-USDT",
                    "market_type": "futures",
                    "issue": f"数量{analysis['original_quantity']}不符合qtyStep={analysis['qty_step']}的要求",
                    "fix": f"应该使用{analysis['correct_quantity']}",
                    "code_fix": f"在trading_rules_preloader.py中确保期货使用qtyStep进行数量截取",
                    "priority": "HIGH"
                })
        
        # 基于SPK-USDT现货分析结果
        if "SPK-USDT_spot" in self.results["precision_analysis"]:
            analysis = self.results["precision_analysis"]["SPK-USDT_spot"]
            if analysis["decimal_places_exceeded"]:
                recommendations.append({
                    "symbol": "SPK-USDT",
                    "market_type": "spot",
                    "issue": f"数量{analysis['original_quantity']}有{analysis['decimal_places']}位小数，超过basePrecision={analysis['base_precision']}允许的{analysis['max_allowed_decimal_places']}位",
                    "fix": f"应该使用{analysis['correct_quantity']}",
                    "code_fix": f"在trading_rules_preloader.py中确保现货使用basePrecision进行数量截取",
                    "priority": "HIGH"
                })
        
        # 通用修复建议
        recommendations.append({
            "symbol": "通用",
            "market_type": "all",
            "issue": "交易规则获取和应用不一致",
            "fix": "统一现货和期货的精度处理逻辑",
            "code_fix": "修复trading_rules_preloader.py中的_get_bybit_trading_rule方法，确保正确区分现货basePrecision和期货qtyStep",
            "priority": "HIGH"
        })
        
        self.results["fix_recommendations"] = recommendations
    
    async def run_diagnosis(self):
        """运行完整的API诊断"""
        print("🚀 开始API交易规则精准诊断...")
        print("=" * 60)
        
        # 分析两个交易对的真实API规则
        await self.analyze_icnt_usdt_futures()
        await self.analyze_spk_usdt_spot()
        
        # 生成修复建议
        self.generate_fix_recommendations()
        
        # 保存结果
        output_file = "123/diagnostic_results/api_trading_rules_diagnosis.json"
        os.makedirs(os.path.dirname(output_file), exist_ok=True)
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, indent=2, ensure_ascii=False)
        
        print(f"✅ API诊断完成，结果已保存到: {output_file}")
        
        # 输出关键发现
        print("\n🔍 API交易规则:")
        for symbol, rule in self.results["trading_rules"].items():
            print(f"  📊 {symbol}:")
            if isinstance(rule, dict):
                for key, value in rule.items():
                    print(f"     {key}: {value}")
            else:
                print(f"     {rule}")
        
        print("\n🔍 精度分析:")
        for symbol, analysis in self.results["precision_analysis"].items():
            print(f"  ❌ {symbol}: {analysis['error_cause']}")
            if not analysis.get("is_valid", True):
                print(f"     原始数量: {analysis['original_quantity']}")
                print(f"     正确数量: {analysis['correct_quantity']}")
        
        print("\n🔧 修复建议:")
        for rec in self.results["fix_recommendations"]:
            print(f"  {rec['priority']}: {rec['symbol']} {rec['market_type']}")
            print(f"     问题: {rec['issue']}")
            print(f"     修复: {rec['fix']}")
            print(f"     代码修复: {rec['code_fix']}")
        
        return self.results

if __name__ == "__main__":
    async def main():
        diagnosis = APITradingRulesDiagnosis()
        results = await diagnosis.run_diagnosis()
    
    asyncio.run(main())
