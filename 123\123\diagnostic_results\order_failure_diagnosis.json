{"diagnosis_time": "2025-07-31 00:24:28", "test_cases": [{"symbol": "ICNT-USDT", "market_type": "futures", "exchange": "bybit", "original_quantity": 153.307, "error": "Bybit API错误: 10001: Qty invalid", "analysis": {"step_size_0.001": {"is_valid": true, "remainder": 0.0}, "step_size_0.01": {"is_valid": false, "remainder": 0.007, "should_be": 153.3, "difference": 0.007}, "step_size_0.1": {"is_valid": false, "remainder": 0.007, "should_be": 153.3, "difference": 0.007}, "step_size_1.0": {"is_valid": false, "remainder": 0.307, "should_be": 153.0, "difference": 0.307}, "step_size_10.0": {"is_valid": false, "remainder": 3.307, "should_be": 150.0, "difference": 3.307}, "decimal_places": 3, "decimal_places_issue": false, "is_integer": false}}, {"symbol": "SPK-USDT", "market_type": "spot", "exchange": "bybit", "original_quantity": 321.995, "error": "Bybit API错误: 170137: Order quantity has too many decimals.", "analysis": {"decimal_places": 3, "decimal_places_issue": false, "precision_1": {"truncated_value": 321.9, "decimal_places": 1}, "precision_2": {"truncated_value": 321.99, "decimal_places": 2}, "precision_3": {"truncated_value": 321.995, "decimal_places": 3}, "precision_4": {"truncated_value": 321.995, "decimal_places": 4}, "precision_5": {"truncated_value": 321.995, "decimal_places": 5}, "precision_6": {"truncated_value": 321.995, "decimal_places": 6}, "base_precision_0.000001": {"is_valid": true}, "base_precision_0.00001": {"is_valid": true}, "base_precision_0.0001": {"is_valid": true}, "base_precision_0.001": {"is_valid": true}, "base_precision_0.01": {"is_valid": false, "remainder": 0.005, "should_be": 321.99}, "base_precision_0.1": {"is_valid": false, "remainder": 0.095, "should_be": 321.9}, "base_precision_1": {"is_valid": false, "remainder": 0.995, "should_be": 321.0}}}], "root_causes": [{"symbol": "SPK-USDT", "issue": "数量321.995有3位小数，超过Bybit现货允许的精度", "likely_cause": "basePrecision获取错误或精度截取失败"}], "recommendations": [{"priority": "HIGH", "issue": "ICNT-USDT期货数量精度问题", "solution": "检查期货交易规则获取逻辑，确保正确获取qtyStep", "code_location": "trading_rules_preloader.py _get_bybit_trading_rule方法"}, {"priority": "HIGH", "issue": "SPK-USDT现货小数位过多", "solution": "检查现货交易规则获取逻辑，确保正确获取basePrecision", "code_location": "trading_rules_preloader.py _get_bybit_trading_rule方法"}, {"priority": "MEDIUM", "issue": "数量格式化统一性", "solution": "确保format_amount_unified方法正确处理Bybit现货和期货的不同精度要求", "code_location": "trading_rules_preloader.py format_amount_unified方法"}, {"priority": "MEDIUM", "issue": "错误重试机制", "solution": "在订单失败时，自动重新获取交易规则并重试", "code_location": "unified_opening_manager.py execute_opening_order方法"}], "trading_rules": "检查失败: 'TradingRule' object has no attribute 'min_amount'"}