# coding: utf-8

"""
    Gate API v4

    Welcome to Gate.io API  APIv4 provides spot, margin and futures trading operations. There are public APIs to retrieve the real-time market statistics, and private APIs which needs authentication to trade on user's behalf.  # noqa: E501

    Contact: <EMAIL>
    Generated by: https://openapi-generator.tech
"""


import pprint
import re  # noqa: F401

import six

from gate_api.configuration import Configuration


class CrossMarginBalance(object):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        'available': 'str',
        'freeze': 'str',
        'borrowed': 'str',
        'interest': 'str',
        'negative_liab': 'str',
        'futures_pos_liab': 'str',
        'equity': 'str',
        'total_freeze': 'str',
        'total_liab': 'str'
    }

    attribute_map = {
        'available': 'available',
        'freeze': 'freeze',
        'borrowed': 'borrowed',
        'interest': 'interest',
        'negative_liab': 'negative_liab',
        'futures_pos_liab': 'futures_pos_liab',
        'equity': 'equity',
        'total_freeze': 'total_freeze',
        'total_liab': 'total_liab'
    }

    def __init__(self, available=None, freeze=None, borrowed=None, interest=None, negative_liab=None, futures_pos_liab=None, equity=None, total_freeze=None, total_liab=None, local_vars_configuration=None):  # noqa: E501
        # type: (str, str, str, str, str, str, str, str, str, Configuration) -> None
        """CrossMarginBalance - a model defined in OpenAPI"""  # noqa: E501
        if local_vars_configuration is None:
            local_vars_configuration = Configuration()
        self.local_vars_configuration = local_vars_configuration

        self._available = None
        self._freeze = None
        self._borrowed = None
        self._interest = None
        self._negative_liab = None
        self._futures_pos_liab = None
        self._equity = None
        self._total_freeze = None
        self._total_liab = None
        self.discriminator = None

        if available is not None:
            self.available = available
        if freeze is not None:
            self.freeze = freeze
        if borrowed is not None:
            self.borrowed = borrowed
        if interest is not None:
            self.interest = interest
        if negative_liab is not None:
            self.negative_liab = negative_liab
        if futures_pos_liab is not None:
            self.futures_pos_liab = futures_pos_liab
        if equity is not None:
            self.equity = equity
        if total_freeze is not None:
            self.total_freeze = total_freeze
        if total_liab is not None:
            self.total_liab = total_liab

    @property
    def available(self):
        """Gets the available of this CrossMarginBalance.  # noqa: E501

        Available amount  # noqa: E501

        :return: The available of this CrossMarginBalance.  # noqa: E501
        :rtype: str
        """
        return self._available

    @available.setter
    def available(self, available):
        """Sets the available of this CrossMarginBalance.

        Available amount  # noqa: E501

        :param available: The available of this CrossMarginBalance.  # noqa: E501
        :type: str
        """

        self._available = available

    @property
    def freeze(self):
        """Gets the freeze of this CrossMarginBalance.  # noqa: E501

        Locked amount  # noqa: E501

        :return: The freeze of this CrossMarginBalance.  # noqa: E501
        :rtype: str
        """
        return self._freeze

    @freeze.setter
    def freeze(self, freeze):
        """Sets the freeze of this CrossMarginBalance.

        Locked amount  # noqa: E501

        :param freeze: The freeze of this CrossMarginBalance.  # noqa: E501
        :type: str
        """

        self._freeze = freeze

    @property
    def borrowed(self):
        """Gets the borrowed of this CrossMarginBalance.  # noqa: E501

        Borrowed amount  # noqa: E501

        :return: The borrowed of this CrossMarginBalance.  # noqa: E501
        :rtype: str
        """
        return self._borrowed

    @borrowed.setter
    def borrowed(self, borrowed):
        """Sets the borrowed of this CrossMarginBalance.

        Borrowed amount  # noqa: E501

        :param borrowed: The borrowed of this CrossMarginBalance.  # noqa: E501
        :type: str
        """

        self._borrowed = borrowed

    @property
    def interest(self):
        """Gets the interest of this CrossMarginBalance.  # noqa: E501

        Unpaid interests  # noqa: E501

        :return: The interest of this CrossMarginBalance.  # noqa: E501
        :rtype: str
        """
        return self._interest

    @interest.setter
    def interest(self, interest):
        """Sets the interest of this CrossMarginBalance.

        Unpaid interests  # noqa: E501

        :param interest: The interest of this CrossMarginBalance.  # noqa: E501
        :type: str
        """

        self._interest = interest

    @property
    def negative_liab(self):
        """Gets the negative_liab of this CrossMarginBalance.  # noqa: E501

        Negative Liabilities. Formula:Min[available+total+unrealized_pnl,0]  # noqa: E501

        :return: The negative_liab of this CrossMarginBalance.  # noqa: E501
        :rtype: str
        """
        return self._negative_liab

    @negative_liab.setter
    def negative_liab(self, negative_liab):
        """Sets the negative_liab of this CrossMarginBalance.

        Negative Liabilities. Formula:Min[available+total+unrealized_pnl,0]  # noqa: E501

        :param negative_liab: The negative_liab of this CrossMarginBalance.  # noqa: E501
        :type: str
        """

        self._negative_liab = negative_liab

    @property
    def futures_pos_liab(self):
        """Gets the futures_pos_liab of this CrossMarginBalance.  # noqa: E501

        Borrowing to Open Positions in Futures  # noqa: E501

        :return: The futures_pos_liab of this CrossMarginBalance.  # noqa: E501
        :rtype: str
        """
        return self._futures_pos_liab

    @futures_pos_liab.setter
    def futures_pos_liab(self, futures_pos_liab):
        """Sets the futures_pos_liab of this CrossMarginBalance.

        Borrowing to Open Positions in Futures  # noqa: E501

        :param futures_pos_liab: The futures_pos_liab of this CrossMarginBalance.  # noqa: E501
        :type: str
        """

        self._futures_pos_liab = futures_pos_liab

    @property
    def equity(self):
        """Gets the equity of this CrossMarginBalance.  # noqa: E501

        Equity. Formula: available + freeze - borrowed + futures account's total + unrealized_pnl  # noqa: E501

        :return: The equity of this CrossMarginBalance.  # noqa: E501
        :rtype: str
        """
        return self._equity

    @equity.setter
    def equity(self, equity):
        """Sets the equity of this CrossMarginBalance.

        Equity. Formula: available + freeze - borrowed + futures account's total + unrealized_pnl  # noqa: E501

        :param equity: The equity of this CrossMarginBalance.  # noqa: E501
        :type: str
        """

        self._equity = equity

    @property
    def total_freeze(self):
        """Gets the total_freeze of this CrossMarginBalance.  # noqa: E501

        Total freeze. Formula: freeze + position_initial_margin + order_margin  # noqa: E501

        :return: The total_freeze of this CrossMarginBalance.  # noqa: E501
        :rtype: str
        """
        return self._total_freeze

    @total_freeze.setter
    def total_freeze(self, total_freeze):
        """Sets the total_freeze of this CrossMarginBalance.

        Total freeze. Formula: freeze + position_initial_margin + order_margin  # noqa: E501

        :param total_freeze: The total_freeze of this CrossMarginBalance.  # noqa: E501
        :type: str
        """

        self._total_freeze = total_freeze

    @property
    def total_liab(self):
        """Gets the total_liab of this CrossMarginBalance.  # noqa: E501

        Total liabilities. Formula: Max[Abs[Min[quity - total_freeze,0], borrowed]] - futures_pos_liab  # noqa: E501

        :return: The total_liab of this CrossMarginBalance.  # noqa: E501
        :rtype: str
        """
        return self._total_liab

    @total_liab.setter
    def total_liab(self, total_liab):
        """Sets the total_liab of this CrossMarginBalance.

        Total liabilities. Formula: Max[Abs[Min[quity - total_freeze,0], borrowed]] - futures_pos_liab  # noqa: E501

        :param total_liab: The total_liab of this CrossMarginBalance.  # noqa: E501
        :type: str
        """

        self._total_liab = total_liab

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.openapi_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CrossMarginBalance):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CrossMarginBalance):
            return True

        return self.to_dict() != other.to_dict()
