# coding: utf-8

"""
    Gate API v4

    Welcome to Gate.io API  APIv4 provides spot, margin and futures trading operations. There are public APIs to retrieve the real-time market statistics, and private APIs which needs authentication to trade on user's behalf.  # noqa: E501

    Contact: <EMAIL>
    Generated by: https://openapi-generator.tech
"""


import pprint
import re  # noqa: F401

import six

from gate_api.configuration import Configuration


class UnifiedModeSet(object):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        'mode': 'str',
        'settings': 'UnifiedSettings'
    }

    attribute_map = {
        'mode': 'mode',
        'settings': 'settings'
    }

    def __init__(self, mode=None, settings=None, local_vars_configuration=None):  # noqa: E501
        # type: (str, UnifiedSettings, Configuration) -> None
        """UnifiedModeSet - a model defined in OpenAPI"""  # noqa: E501
        if local_vars_configuration is None:
            local_vars_configuration = Configuration()
        self.local_vars_configuration = local_vars_configuration

        self._mode = None
        self._settings = None
        self.discriminator = None

        self.mode = mode
        if settings is not None:
            self.settings = settings

    @property
    def mode(self):
        """Gets the mode of this UnifiedModeSet.  # noqa: E501

        Unified account mode:  - `classic`: Classic account mode - `multi_currency`: Multi-currency margin mode - `portfolio`: Portfolio margin mode - `single_currency`: Single Currency Margin Model  # noqa: E501

        :return: The mode of this UnifiedModeSet.  # noqa: E501
        :rtype: str
        """
        return self._mode

    @mode.setter
    def mode(self, mode):
        """Sets the mode of this UnifiedModeSet.

        Unified account mode:  - `classic`: Classic account mode - `multi_currency`: Multi-currency margin mode - `portfolio`: Portfolio margin mode - `single_currency`: Single Currency Margin Model  # noqa: E501

        :param mode: The mode of this UnifiedModeSet.  # noqa: E501
        :type: str
        """
        if self.local_vars_configuration.client_side_validation and mode is None:  # noqa: E501
            raise ValueError("Invalid value for `mode`, must not be `None`")  # noqa: E501

        self._mode = mode

    @property
    def settings(self):
        """Gets the settings of this UnifiedModeSet.  # noqa: E501


        :return: The settings of this UnifiedModeSet.  # noqa: E501
        :rtype: UnifiedSettings
        """
        return self._settings

    @settings.setter
    def settings(self, settings):
        """Sets the settings of this UnifiedModeSet.


        :param settings: The settings of this UnifiedModeSet.  # noqa: E501
        :type: UnifiedSettings
        """

        self._settings = settings

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.openapi_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, UnifiedModeSet):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, UnifiedModeSet):
            return True

        return self.to_dict() != other.to_dict()
