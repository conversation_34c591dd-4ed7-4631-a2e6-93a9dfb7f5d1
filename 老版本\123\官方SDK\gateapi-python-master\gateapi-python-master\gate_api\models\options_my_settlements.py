# coding: utf-8

"""
    Gate API v4

    Welcome to Gate.io API  APIv4 provides spot, margin and futures trading operations. There are public APIs to retrieve the real-time market statistics, and private APIs which needs authentication to trade on user's behalf.  # noqa: E501

    Contact: <EMAIL>
    Generated by: https://openapi-generator.tech
"""


import pprint
import re  # noqa: F401

import six

from gate_api.configuration import Configuration


class OptionsMySettlements(object):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        'time': 'float',
        'underlying': 'str',
        'contract': 'str',
        'strike_price': 'str',
        'settle_price': 'str',
        'size': 'int',
        'settle_profit': 'str',
        'fee': 'str',
        'realised_pnl': 'str'
    }

    attribute_map = {
        'time': 'time',
        'underlying': 'underlying',
        'contract': 'contract',
        'strike_price': 'strike_price',
        'settle_price': 'settle_price',
        'size': 'size',
        'settle_profit': 'settle_profit',
        'fee': 'fee',
        'realised_pnl': 'realised_pnl'
    }

    def __init__(self, time=None, underlying=None, contract=None, strike_price=None, settle_price=None, size=None, settle_profit=None, fee=None, realised_pnl=None, local_vars_configuration=None):  # noqa: E501
        # type: (float, str, str, str, str, int, str, str, str, Configuration) -> None
        """OptionsMySettlements - a model defined in OpenAPI"""  # noqa: E501
        if local_vars_configuration is None:
            local_vars_configuration = Configuration()
        self.local_vars_configuration = local_vars_configuration

        self._time = None
        self._underlying = None
        self._contract = None
        self._strike_price = None
        self._settle_price = None
        self._size = None
        self._settle_profit = None
        self._fee = None
        self._realised_pnl = None
        self.discriminator = None

        if time is not None:
            self.time = time
        if underlying is not None:
            self.underlying = underlying
        if contract is not None:
            self.contract = contract
        if strike_price is not None:
            self.strike_price = strike_price
        if settle_price is not None:
            self.settle_price = settle_price
        if size is not None:
            self.size = size
        if settle_profit is not None:
            self.settle_profit = settle_profit
        if fee is not None:
            self.fee = fee
        if realised_pnl is not None:
            self.realised_pnl = realised_pnl

    @property
    def time(self):
        """Gets the time of this OptionsMySettlements.  # noqa: E501

        Settlement time  # noqa: E501

        :return: The time of this OptionsMySettlements.  # noqa: E501
        :rtype: float
        """
        return self._time

    @time.setter
    def time(self, time):
        """Sets the time of this OptionsMySettlements.

        Settlement time  # noqa: E501

        :param time: The time of this OptionsMySettlements.  # noqa: E501
        :type: float
        """

        self._time = time

    @property
    def underlying(self):
        """Gets the underlying of this OptionsMySettlements.  # noqa: E501

        Underlying  # noqa: E501

        :return: The underlying of this OptionsMySettlements.  # noqa: E501
        :rtype: str
        """
        return self._underlying

    @underlying.setter
    def underlying(self, underlying):
        """Sets the underlying of this OptionsMySettlements.

        Underlying  # noqa: E501

        :param underlying: The underlying of this OptionsMySettlements.  # noqa: E501
        :type: str
        """

        self._underlying = underlying

    @property
    def contract(self):
        """Gets the contract of this OptionsMySettlements.  # noqa: E501

        Options contract name  # noqa: E501

        :return: The contract of this OptionsMySettlements.  # noqa: E501
        :rtype: str
        """
        return self._contract

    @contract.setter
    def contract(self, contract):
        """Sets the contract of this OptionsMySettlements.

        Options contract name  # noqa: E501

        :param contract: The contract of this OptionsMySettlements.  # noqa: E501
        :type: str
        """

        self._contract = contract

    @property
    def strike_price(self):
        """Gets the strike_price of this OptionsMySettlements.  # noqa: E501

        Strike price (quote currency)  # noqa: E501

        :return: The strike_price of this OptionsMySettlements.  # noqa: E501
        :rtype: str
        """
        return self._strike_price

    @strike_price.setter
    def strike_price(self, strike_price):
        """Sets the strike_price of this OptionsMySettlements.

        Strike price (quote currency)  # noqa: E501

        :param strike_price: The strike_price of this OptionsMySettlements.  # noqa: E501
        :type: str
        """

        self._strike_price = strike_price

    @property
    def settle_price(self):
        """Gets the settle_price of this OptionsMySettlements.  # noqa: E501

        Settlement price (quote currency)  # noqa: E501

        :return: The settle_price of this OptionsMySettlements.  # noqa: E501
        :rtype: str
        """
        return self._settle_price

    @settle_price.setter
    def settle_price(self, settle_price):
        """Sets the settle_price of this OptionsMySettlements.

        Settlement price (quote currency)  # noqa: E501

        :param settle_price: The settle_price of this OptionsMySettlements.  # noqa: E501
        :type: str
        """

        self._settle_price = settle_price

    @property
    def size(self):
        """Gets the size of this OptionsMySettlements.  # noqa: E501

        Size  # noqa: E501

        :return: The size of this OptionsMySettlements.  # noqa: E501
        :rtype: int
        """
        return self._size

    @size.setter
    def size(self, size):
        """Sets the size of this OptionsMySettlements.

        Size  # noqa: E501

        :param size: The size of this OptionsMySettlements.  # noqa: E501
        :type: int
        """

        self._size = size

    @property
    def settle_profit(self):
        """Gets the settle_profit of this OptionsMySettlements.  # noqa: E501

        Settlement profit (quote currency)  # noqa: E501

        :return: The settle_profit of this OptionsMySettlements.  # noqa: E501
        :rtype: str
        """
        return self._settle_profit

    @settle_profit.setter
    def settle_profit(self, settle_profit):
        """Sets the settle_profit of this OptionsMySettlements.

        Settlement profit (quote currency)  # noqa: E501

        :param settle_profit: The settle_profit of this OptionsMySettlements.  # noqa: E501
        :type: str
        """

        self._settle_profit = settle_profit

    @property
    def fee(self):
        """Gets the fee of this OptionsMySettlements.  # noqa: E501

        Fee (quote currency)  # noqa: E501

        :return: The fee of this OptionsMySettlements.  # noqa: E501
        :rtype: str
        """
        return self._fee

    @fee.setter
    def fee(self, fee):
        """Sets the fee of this OptionsMySettlements.

        Fee (quote currency)  # noqa: E501

        :param fee: The fee of this OptionsMySettlements.  # noqa: E501
        :type: str
        """

        self._fee = fee

    @property
    def realised_pnl(self):
        """Gets the realised_pnl of this OptionsMySettlements.  # noqa: E501

        The accumulated profit and loss of opening a position, including premium, fee, settlement profit, etc. (quote currency)  # noqa: E501

        :return: The realised_pnl of this OptionsMySettlements.  # noqa: E501
        :rtype: str
        """
        return self._realised_pnl

    @realised_pnl.setter
    def realised_pnl(self, realised_pnl):
        """Sets the realised_pnl of this OptionsMySettlements.

        The accumulated profit and loss of opening a position, including premium, fee, settlement profit, etc. (quote currency)  # noqa: E501

        :param realised_pnl: The realised_pnl of this OptionsMySettlements.  # noqa: E501
        :type: str
        """

        self._realised_pnl = realised_pnl

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.openapi_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, OptionsMySettlements):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, OptionsMySettlements):
            return True

        return self.to_dict() != other.to_dict()
