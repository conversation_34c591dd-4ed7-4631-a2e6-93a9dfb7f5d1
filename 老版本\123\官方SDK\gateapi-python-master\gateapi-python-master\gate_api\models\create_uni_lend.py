# coding: utf-8

"""
    Gate API v4

    Welcome to Gate.io API  APIv4 provides spot, margin and futures trading operations. There are public APIs to retrieve the real-time market statistics, and private APIs which needs authentication to trade on user's behalf.  # noqa: E501

    Contact: <EMAIL>
    Generated by: https://openapi-generator.tech
"""


import pprint
import re  # noqa: F401

import six

from gate_api.configuration import Configuration


class CreateUniLend(object):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        'currency': 'str',
        'amount': 'str',
        'type': 'str',
        'min_rate': 'str'
    }

    attribute_map = {
        'currency': 'currency',
        'amount': 'amount',
        'type': 'type',
        'min_rate': 'min_rate'
    }

    def __init__(self, currency=None, amount=None, type=None, min_rate=None, local_vars_configuration=None):  # noqa: E501
        # type: (str, str, str, str, Configuration) -> None
        """CreateUniLend - a model defined in OpenAPI"""  # noqa: E501
        if local_vars_configuration is None:
            local_vars_configuration = Configuration()
        self.local_vars_configuration = local_vars_configuration

        self._currency = None
        self._amount = None
        self._type = None
        self._min_rate = None
        self.discriminator = None

        self.currency = currency
        self.amount = amount
        self.type = type
        if min_rate is not None:
            self.min_rate = min_rate

    @property
    def currency(self):
        """Gets the currency of this CreateUniLend.  # noqa: E501

        Currency name  # noqa: E501

        :return: The currency of this CreateUniLend.  # noqa: E501
        :rtype: str
        """
        return self._currency

    @currency.setter
    def currency(self, currency):
        """Sets the currency of this CreateUniLend.

        Currency name  # noqa: E501

        :param currency: The currency of this CreateUniLend.  # noqa: E501
        :type: str
        """
        if self.local_vars_configuration.client_side_validation and currency is None:  # noqa: E501
            raise ValueError("Invalid value for `currency`, must not be `None`")  # noqa: E501

        self._currency = currency

    @property
    def amount(self):
        """Gets the amount of this CreateUniLend.  # noqa: E501

        The amount of currency could be lent  # noqa: E501

        :return: The amount of this CreateUniLend.  # noqa: E501
        :rtype: str
        """
        return self._amount

    @amount.setter
    def amount(self, amount):
        """Sets the amount of this CreateUniLend.

        The amount of currency could be lent  # noqa: E501

        :param amount: The amount of this CreateUniLend.  # noqa: E501
        :type: str
        """
        if self.local_vars_configuration.client_side_validation and amount is None:  # noqa: E501
            raise ValueError("Invalid value for `amount`, must not be `None`")  # noqa: E501

        self._amount = amount

    @property
    def type(self):
        """Gets the type of this CreateUniLend.  # noqa: E501

        type: lend - lend, redeem - redeem  # noqa: E501

        :return: The type of this CreateUniLend.  # noqa: E501
        :rtype: str
        """
        return self._type

    @type.setter
    def type(self, type):
        """Sets the type of this CreateUniLend.

        type: lend - lend, redeem - redeem  # noqa: E501

        :param type: The type of this CreateUniLend.  # noqa: E501
        :type: str
        """
        if self.local_vars_configuration.client_side_validation and type is None:  # noqa: E501
            raise ValueError("Invalid value for `type`, must not be `None`")  # noqa: E501
        allowed_values = ["lend", "redeem"]  # noqa: E501
        if self.local_vars_configuration.client_side_validation and type not in allowed_values:  # noqa: E501
            raise ValueError(
                "Invalid value for `type` ({0}), must be one of {1}"  # noqa: E501
                .format(type, allowed_values)
            )

        self._type = type

    @property
    def min_rate(self):
        """Gets the min_rate of this CreateUniLend.  # noqa: E501

        The minimum interest rate. If the value is too high, it might lead to the unsuccessful lending and no profit will be gained for that hour.   # noqa: E501

        :return: The min_rate of this CreateUniLend.  # noqa: E501
        :rtype: str
        """
        return self._min_rate

    @min_rate.setter
    def min_rate(self, min_rate):
        """Sets the min_rate of this CreateUniLend.

        The minimum interest rate. If the value is too high, it might lead to the unsuccessful lending and no profit will be gained for that hour.   # noqa: E501

        :param min_rate: The min_rate of this CreateUniLend.  # noqa: E501
        :type: str
        """

        self._min_rate = min_rate

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.openapi_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateUniLend):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateUniLend):
            return True

        return self.to_dict() != other.to_dict()
