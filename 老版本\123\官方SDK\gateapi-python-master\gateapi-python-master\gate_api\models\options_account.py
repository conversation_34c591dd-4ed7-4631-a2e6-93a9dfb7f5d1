# coding: utf-8

"""
    Gate API v4

    Welcome to Gate.io API  APIv4 provides spot, margin and futures trading operations. There are public APIs to retrieve the real-time market statistics, and private APIs which needs authentication to trade on user's behalf.  # noqa: E501

    Contact: <EMAIL>
    Generated by: https://openapi-generator.tech
"""


import pprint
import re  # noqa: F401

import six

from gate_api.configuration import Configuration


class OptionsAccount(object):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        'user': 'int',
        'total': 'str',
        'position_value': 'str',
        'equity': 'str',
        'short_enabled': 'bool',
        'mmp_enabled': 'bool',
        'liq_triggered': 'bool',
        'margin_mode': 'int',
        'unrealised_pnl': 'str',
        'init_margin': 'str',
        'maint_margin': 'str',
        'order_margin': 'str',
        'ask_order_margin': 'str',
        'bid_order_margin': 'str',
        'available': 'str',
        'point': 'str',
        'currency': 'str',
        'orders_limit': 'int',
        'position_notional_limit': 'int'
    }

    attribute_map = {
        'user': 'user',
        'total': 'total',
        'position_value': 'position_value',
        'equity': 'equity',
        'short_enabled': 'short_enabled',
        'mmp_enabled': 'mmp_enabled',
        'liq_triggered': 'liq_triggered',
        'margin_mode': 'margin_mode',
        'unrealised_pnl': 'unrealised_pnl',
        'init_margin': 'init_margin',
        'maint_margin': 'maint_margin',
        'order_margin': 'order_margin',
        'ask_order_margin': 'ask_order_margin',
        'bid_order_margin': 'bid_order_margin',
        'available': 'available',
        'point': 'point',
        'currency': 'currency',
        'orders_limit': 'orders_limit',
        'position_notional_limit': 'position_notional_limit'
    }

    def __init__(self, user=None, total=None, position_value=None, equity=None, short_enabled=None, mmp_enabled=None, liq_triggered=None, margin_mode=None, unrealised_pnl=None, init_margin=None, maint_margin=None, order_margin=None, ask_order_margin=None, bid_order_margin=None, available=None, point=None, currency=None, orders_limit=None, position_notional_limit=None, local_vars_configuration=None):  # noqa: E501
        # type: (int, str, str, str, bool, bool, bool, int, str, str, str, str, str, str, str, str, str, int, int, Configuration) -> None
        """OptionsAccount - a model defined in OpenAPI"""  # noqa: E501
        if local_vars_configuration is None:
            local_vars_configuration = Configuration()
        self.local_vars_configuration = local_vars_configuration

        self._user = None
        self._total = None
        self._position_value = None
        self._equity = None
        self._short_enabled = None
        self._mmp_enabled = None
        self._liq_triggered = None
        self._margin_mode = None
        self._unrealised_pnl = None
        self._init_margin = None
        self._maint_margin = None
        self._order_margin = None
        self._ask_order_margin = None
        self._bid_order_margin = None
        self._available = None
        self._point = None
        self._currency = None
        self._orders_limit = None
        self._position_notional_limit = None
        self.discriminator = None

        if user is not None:
            self.user = user
        if total is not None:
            self.total = total
        if position_value is not None:
            self.position_value = position_value
        if equity is not None:
            self.equity = equity
        if short_enabled is not None:
            self.short_enabled = short_enabled
        if mmp_enabled is not None:
            self.mmp_enabled = mmp_enabled
        if liq_triggered is not None:
            self.liq_triggered = liq_triggered
        if margin_mode is not None:
            self.margin_mode = margin_mode
        if unrealised_pnl is not None:
            self.unrealised_pnl = unrealised_pnl
        if init_margin is not None:
            self.init_margin = init_margin
        if maint_margin is not None:
            self.maint_margin = maint_margin
        if order_margin is not None:
            self.order_margin = order_margin
        if ask_order_margin is not None:
            self.ask_order_margin = ask_order_margin
        if bid_order_margin is not None:
            self.bid_order_margin = bid_order_margin
        if available is not None:
            self.available = available
        if point is not None:
            self.point = point
        if currency is not None:
            self.currency = currency
        if orders_limit is not None:
            self.orders_limit = orders_limit
        if position_notional_limit is not None:
            self.position_notional_limit = position_notional_limit

    @property
    def user(self):
        """Gets the user of this OptionsAccount.  # noqa: E501

        User ID  # noqa: E501

        :return: The user of this OptionsAccount.  # noqa: E501
        :rtype: int
        """
        return self._user

    @user.setter
    def user(self, user):
        """Sets the user of this OptionsAccount.

        User ID  # noqa: E501

        :param user: The user of this OptionsAccount.  # noqa: E501
        :type: int
        """

        self._user = user

    @property
    def total(self):
        """Gets the total of this OptionsAccount.  # noqa: E501

        Account balance  # noqa: E501

        :return: The total of this OptionsAccount.  # noqa: E501
        :rtype: str
        """
        return self._total

    @total.setter
    def total(self, total):
        """Sets the total of this OptionsAccount.

        Account balance  # noqa: E501

        :param total: The total of this OptionsAccount.  # noqa: E501
        :type: str
        """

        self._total = total

    @property
    def position_value(self):
        """Gets the position_value of this OptionsAccount.  # noqa: E501

        Position value, long position value is positive, short position value is negative  # noqa: E501

        :return: The position_value of this OptionsAccount.  # noqa: E501
        :rtype: str
        """
        return self._position_value

    @position_value.setter
    def position_value(self, position_value):
        """Sets the position_value of this OptionsAccount.

        Position value, long position value is positive, short position value is negative  # noqa: E501

        :param position_value: The position_value of this OptionsAccount.  # noqa: E501
        :type: str
        """

        self._position_value = position_value

    @property
    def equity(self):
        """Gets the equity of this OptionsAccount.  # noqa: E501

        Account equity, the sum of account balance and position value  # noqa: E501

        :return: The equity of this OptionsAccount.  # noqa: E501
        :rtype: str
        """
        return self._equity

    @equity.setter
    def equity(self, equity):
        """Sets the equity of this OptionsAccount.

        Account equity, the sum of account balance and position value  # noqa: E501

        :param equity: The equity of this OptionsAccount.  # noqa: E501
        :type: str
        """

        self._equity = equity

    @property
    def short_enabled(self):
        """Gets the short_enabled of this OptionsAccount.  # noqa: E501

        If the account is allowed to short  # noqa: E501

        :return: The short_enabled of this OptionsAccount.  # noqa: E501
        :rtype: bool
        """
        return self._short_enabled

    @short_enabled.setter
    def short_enabled(self, short_enabled):
        """Sets the short_enabled of this OptionsAccount.

        If the account is allowed to short  # noqa: E501

        :param short_enabled: The short_enabled of this OptionsAccount.  # noqa: E501
        :type: bool
        """

        self._short_enabled = short_enabled

    @property
    def mmp_enabled(self):
        """Gets the mmp_enabled of this OptionsAccount.  # noqa: E501

        Whether to enable MMP  # noqa: E501

        :return: The mmp_enabled of this OptionsAccount.  # noqa: E501
        :rtype: bool
        """
        return self._mmp_enabled

    @mmp_enabled.setter
    def mmp_enabled(self, mmp_enabled):
        """Sets the mmp_enabled of this OptionsAccount.

        Whether to enable MMP  # noqa: E501

        :param mmp_enabled: The mmp_enabled of this OptionsAccount.  # noqa: E501
        :type: bool
        """

        self._mmp_enabled = mmp_enabled

    @property
    def liq_triggered(self):
        """Gets the liq_triggered of this OptionsAccount.  # noqa: E501

        Whether to trigger position liquidation  # noqa: E501

        :return: The liq_triggered of this OptionsAccount.  # noqa: E501
        :rtype: bool
        """
        return self._liq_triggered

    @liq_triggered.setter
    def liq_triggered(self, liq_triggered):
        """Sets the liq_triggered of this OptionsAccount.

        Whether to trigger position liquidation  # noqa: E501

        :param liq_triggered: The liq_triggered of this OptionsAccount.  # noqa: E501
        :type: bool
        """

        self._liq_triggered = liq_triggered

    @property
    def margin_mode(self):
        """Gets the margin_mode of this OptionsAccount.  # noqa: E501

        ｜ 保证金模式： - 0：经典现货保证金模式 - 1：跨币种保证金模式 - 2：组合保证金模式  # noqa: E501

        :return: The margin_mode of this OptionsAccount.  # noqa: E501
        :rtype: int
        """
        return self._margin_mode

    @margin_mode.setter
    def margin_mode(self, margin_mode):
        """Sets the margin_mode of this OptionsAccount.

        ｜ 保证金模式： - 0：经典现货保证金模式 - 1：跨币种保证金模式 - 2：组合保证金模式  # noqa: E501

        :param margin_mode: The margin_mode of this OptionsAccount.  # noqa: E501
        :type: int
        """
        allowed_values = [0, 1, 2]  # noqa: E501
        if self.local_vars_configuration.client_side_validation and margin_mode not in allowed_values:  # noqa: E501
            raise ValueError(
                "Invalid value for `margin_mode` ({0}), must be one of {1}"  # noqa: E501
                .format(margin_mode, allowed_values)
            )

        self._margin_mode = margin_mode

    @property
    def unrealised_pnl(self):
        """Gets the unrealised_pnl of this OptionsAccount.  # noqa: E501

        Unrealized PNL  # noqa: E501

        :return: The unrealised_pnl of this OptionsAccount.  # noqa: E501
        :rtype: str
        """
        return self._unrealised_pnl

    @unrealised_pnl.setter
    def unrealised_pnl(self, unrealised_pnl):
        """Sets the unrealised_pnl of this OptionsAccount.

        Unrealized PNL  # noqa: E501

        :param unrealised_pnl: The unrealised_pnl of this OptionsAccount.  # noqa: E501
        :type: str
        """

        self._unrealised_pnl = unrealised_pnl

    @property
    def init_margin(self):
        """Gets the init_margin of this OptionsAccount.  # noqa: E501

        Initial position margin  # noqa: E501

        :return: The init_margin of this OptionsAccount.  # noqa: E501
        :rtype: str
        """
        return self._init_margin

    @init_margin.setter
    def init_margin(self, init_margin):
        """Sets the init_margin of this OptionsAccount.

        Initial position margin  # noqa: E501

        :param init_margin: The init_margin of this OptionsAccount.  # noqa: E501
        :type: str
        """

        self._init_margin = init_margin

    @property
    def maint_margin(self):
        """Gets the maint_margin of this OptionsAccount.  # noqa: E501

        Position maintenance margin  # noqa: E501

        :return: The maint_margin of this OptionsAccount.  # noqa: E501
        :rtype: str
        """
        return self._maint_margin

    @maint_margin.setter
    def maint_margin(self, maint_margin):
        """Sets the maint_margin of this OptionsAccount.

        Position maintenance margin  # noqa: E501

        :param maint_margin: The maint_margin of this OptionsAccount.  # noqa: E501
        :type: str
        """

        self._maint_margin = maint_margin

    @property
    def order_margin(self):
        """Gets the order_margin of this OptionsAccount.  # noqa: E501

        Order margin of unfinished orders  # noqa: E501

        :return: The order_margin of this OptionsAccount.  # noqa: E501
        :rtype: str
        """
        return self._order_margin

    @order_margin.setter
    def order_margin(self, order_margin):
        """Sets the order_margin of this OptionsAccount.

        Order margin of unfinished orders  # noqa: E501

        :param order_margin: The order_margin of this OptionsAccount.  # noqa: E501
        :type: str
        """

        self._order_margin = order_margin

    @property
    def ask_order_margin(self):
        """Gets the ask_order_margin of this OptionsAccount.  # noqa: E501

        Margin for outstanding sell orders  # noqa: E501

        :return: The ask_order_margin of this OptionsAccount.  # noqa: E501
        :rtype: str
        """
        return self._ask_order_margin

    @ask_order_margin.setter
    def ask_order_margin(self, ask_order_margin):
        """Sets the ask_order_margin of this OptionsAccount.

        Margin for outstanding sell orders  # noqa: E501

        :param ask_order_margin: The ask_order_margin of this OptionsAccount.  # noqa: E501
        :type: str
        """

        self._ask_order_margin = ask_order_margin

    @property
    def bid_order_margin(self):
        """Gets the bid_order_margin of this OptionsAccount.  # noqa: E501

        Margin for outstanding buy orders  # noqa: E501

        :return: The bid_order_margin of this OptionsAccount.  # noqa: E501
        :rtype: str
        """
        return self._bid_order_margin

    @bid_order_margin.setter
    def bid_order_margin(self, bid_order_margin):
        """Sets the bid_order_margin of this OptionsAccount.

        Margin for outstanding buy orders  # noqa: E501

        :param bid_order_margin: The bid_order_margin of this OptionsAccount.  # noqa: E501
        :type: str
        """

        self._bid_order_margin = bid_order_margin

    @property
    def available(self):
        """Gets the available of this OptionsAccount.  # noqa: E501

        Available balance to transfer out or trade  # noqa: E501

        :return: The available of this OptionsAccount.  # noqa: E501
        :rtype: str
        """
        return self._available

    @available.setter
    def available(self, available):
        """Sets the available of this OptionsAccount.

        Available balance to transfer out or trade  # noqa: E501

        :param available: The available of this OptionsAccount.  # noqa: E501
        :type: str
        """

        self._available = available

    @property
    def point(self):
        """Gets the point of this OptionsAccount.  # noqa: E501

        POINT amount  # noqa: E501

        :return: The point of this OptionsAccount.  # noqa: E501
        :rtype: str
        """
        return self._point

    @point.setter
    def point(self, point):
        """Sets the point of this OptionsAccount.

        POINT amount  # noqa: E501

        :param point: The point of this OptionsAccount.  # noqa: E501
        :type: str
        """

        self._point = point

    @property
    def currency(self):
        """Gets the currency of this OptionsAccount.  # noqa: E501

        Settle currency  # noqa: E501

        :return: The currency of this OptionsAccount.  # noqa: E501
        :rtype: str
        """
        return self._currency

    @currency.setter
    def currency(self, currency):
        """Sets the currency of this OptionsAccount.

        Settle currency  # noqa: E501

        :param currency: The currency of this OptionsAccount.  # noqa: E501
        :type: str
        """

        self._currency = currency

    @property
    def orders_limit(self):
        """Gets the orders_limit of this OptionsAccount.  # noqa: E501

        Maximum number of outstanding orders  # noqa: E501

        :return: The orders_limit of this OptionsAccount.  # noqa: E501
        :rtype: int
        """
        return self._orders_limit

    @orders_limit.setter
    def orders_limit(self, orders_limit):
        """Sets the orders_limit of this OptionsAccount.

        Maximum number of outstanding orders  # noqa: E501

        :param orders_limit: The orders_limit of this OptionsAccount.  # noqa: E501
        :type: int
        """

        self._orders_limit = orders_limit

    @property
    def position_notional_limit(self):
        """Gets the position_notional_limit of this OptionsAccount.  # noqa: E501

        Notional value upper limit, including the nominal value of positions and outstanding orders  # noqa: E501

        :return: The position_notional_limit of this OptionsAccount.  # noqa: E501
        :rtype: int
        """
        return self._position_notional_limit

    @position_notional_limit.setter
    def position_notional_limit(self, position_notional_limit):
        """Sets the position_notional_limit of this OptionsAccount.

        Notional value upper limit, including the nominal value of positions and outstanding orders  # noqa: E501

        :param position_notional_limit: The position_notional_limit of this OptionsAccount.  # noqa: E501
        :type: int
        """

        self._position_notional_limit = position_notional_limit

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.openapi_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, OptionsAccount):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, OptionsAccount):
            return True

        return self.to_dict() != other.to_dict()
