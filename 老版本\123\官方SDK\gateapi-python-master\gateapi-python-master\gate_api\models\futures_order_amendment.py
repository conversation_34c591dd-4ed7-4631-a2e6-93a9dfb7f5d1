# coding: utf-8

"""
    Gate API v4

    Welcome to Gate.io API  APIv4 provides spot, margin and futures trading operations. There are public APIs to retrieve the real-time market statistics, and private APIs which needs authentication to trade on user's behalf.  # noqa: E501

    Contact: <EMAIL>
    Generated by: https://openapi-generator.tech
"""


import pprint
import re  # noqa: F401

import six

from gate_api.configuration import Configuration


class FuturesOrderAmendment(object):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        'size': 'int',
        'price': 'str',
        'amend_text': 'str',
        'biz_info': 'str',
        'bbo': 'str'
    }

    attribute_map = {
        'size': 'size',
        'price': 'price',
        'amend_text': 'amend_text',
        'biz_info': 'biz_info',
        'bbo': 'bbo'
    }

    def __init__(self, size=None, price=None, amend_text=None, biz_info=None, bbo=None, local_vars_configuration=None):  # noqa: E501
        # type: (int, str, str, str, str, Configuration) -> None
        """FuturesOrderAmendment - a model defined in OpenAPI"""  # noqa: E501
        if local_vars_configuration is None:
            local_vars_configuration = Configuration()
        self.local_vars_configuration = local_vars_configuration

        self._size = None
        self._price = None
        self._amend_text = None
        self._biz_info = None
        self._bbo = None
        self.discriminator = None

        if size is not None:
            self.size = size
        if price is not None:
            self.price = price
        if amend_text is not None:
            self.amend_text = amend_text
        if biz_info is not None:
            self.biz_info = biz_info
        if bbo is not None:
            self.bbo = bbo

    @property
    def size(self):
        """Gets the size of this FuturesOrderAmendment.  # noqa: E501

        New order size, including filled part.  - If new size is less than or equal to filled size, the order will be cancelled. - Order side must be identical to the original one. - Close order size cannot be changed. - For reduce only orders, increasing size may leads to other reduce only orders being cancelled. - If price is not changed, decreasing size will not change its precedence in order book, while increasing will move it to the last at current price.  # noqa: E501

        :return: The size of this FuturesOrderAmendment.  # noqa: E501
        :rtype: int
        """
        return self._size

    @size.setter
    def size(self, size):
        """Sets the size of this FuturesOrderAmendment.

        New order size, including filled part.  - If new size is less than or equal to filled size, the order will be cancelled. - Order side must be identical to the original one. - Close order size cannot be changed. - For reduce only orders, increasing size may leads to other reduce only orders being cancelled. - If price is not changed, decreasing size will not change its precedence in order book, while increasing will move it to the last at current price.  # noqa: E501

        :param size: The size of this FuturesOrderAmendment.  # noqa: E501
        :type: int
        """

        self._size = size

    @property
    def price(self):
        """Gets the price of this FuturesOrderAmendment.  # noqa: E501

        New order price.  # noqa: E501

        :return: The price of this FuturesOrderAmendment.  # noqa: E501
        :rtype: str
        """
        return self._price

    @price.setter
    def price(self, price):
        """Sets the price of this FuturesOrderAmendment.

        New order price.  # noqa: E501

        :param price: The price of this FuturesOrderAmendment.  # noqa: E501
        :type: str
        """

        self._price = price

    @property
    def amend_text(self):
        """Gets the amend_text of this FuturesOrderAmendment.  # noqa: E501

        Custom info during amending order  # noqa: E501

        :return: The amend_text of this FuturesOrderAmendment.  # noqa: E501
        :rtype: str
        """
        return self._amend_text

    @amend_text.setter
    def amend_text(self, amend_text):
        """Sets the amend_text of this FuturesOrderAmendment.

        Custom info during amending order  # noqa: E501

        :param amend_text: The amend_text of this FuturesOrderAmendment.  # noqa: E501
        :type: str
        """

        self._amend_text = amend_text

    @property
    def biz_info(self):
        """Gets the biz_info of this FuturesOrderAmendment.  # noqa: E501

        Users can annotate this modification with information.  # noqa: E501

        :return: The biz_info of this FuturesOrderAmendment.  # noqa: E501
        :rtype: str
        """
        return self._biz_info

    @biz_info.setter
    def biz_info(self, biz_info):
        """Sets the biz_info of this FuturesOrderAmendment.

        Users can annotate this modification with information.  # noqa: E501

        :param biz_info: The biz_info of this FuturesOrderAmendment.  # noqa: E501
        :type: str
        """

        self._biz_info = biz_info

    @property
    def bbo(self):
        """Gets the bbo of this FuturesOrderAmendment.  # noqa: E501

        Users are able to modify the offer price manually.  # noqa: E501

        :return: The bbo of this FuturesOrderAmendment.  # noqa: E501
        :rtype: str
        """
        return self._bbo

    @bbo.setter
    def bbo(self, bbo):
        """Sets the bbo of this FuturesOrderAmendment.

        Users are able to modify the offer price manually.  # noqa: E501

        :param bbo: The bbo of this FuturesOrderAmendment.  # noqa: E501
        :type: str
        """

        self._bbo = bbo

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.openapi_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, FuturesOrderAmendment):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, FuturesOrderAmendment):
            return True

        return self.to_dict() != other.to_dict()
