# 📊 通用期货溢价套利系统 - 数据结构定义（第一部分：基础类型）

## 📋 文档概述

本文档定义了通用期货溢价套利系统中所有数据类、配置类、枚举类和返回结果类的完整结构，确保数据传递的一致性和类型安全。

## 🔥 **新增统一模块数据结构（2025-06-29）**

### 📊 **订单簿验证相关数据结构**

#### **OrderbookValidationConfig**
```python
@dataclass
class OrderbookValidationConfig:
    """🔥 简化配置：遵循全流程工作流设计"""
    max_depth_levels: int = 10          # 前10档分析
```

#### **OrderbookValidationResult**
```python
@dataclass
class OrderbookValidationResult:
    """🔥 简化结果：只关心是否可用"""
    is_valid: bool                      # 验证是否通过
    error_message: str = ""             # 错误信息
    asks_count: int = 0                 # asks档位数量
    bids_count: int = 0                 # bids档位数量
```

### 📊 **数据格式化相关数据结构**

#### **OrderbookFormatConfig**
```python
@dataclass
class OrderbookFormatConfig:
    max_depth_levels: int = 10          # 最大深度档位
    include_mid_price: bool = True      # 是否包含中间价
    include_spread: bool = True         # 是否包含价差信息
    price_precision: int = 8            # 价格精度
    volume_precision: int = 6           # 数量精度
    sort_asks_asc: bool = True          # asks按价格升序排列
    sort_bids_desc: bool = True         # bids按价格降序排列
```

### 📊 **时间戳处理相关数据结构**

#### **TimestampSyncConfig**
```python
@dataclass
class TimestampSyncConfig:
    sync_interval_seconds: int = 300    # 时间同步间隔: 5分钟
    max_time_offset_ms: int = 5000      # 最大时间偏移容忍度: 5秒
    sync_timeout_seconds: int = 10      # 同步请求超时: 10秒
    enable_auto_sync: bool = True       # 是否启用自动同步
    fallback_to_local: bool = True      # 同步失败时是否回退到本地时间
```

### 📊 **深度分析相关数据结构**

#### **DepthAnalysisConfig**
```python
@dataclass
class DepthAnalysisConfig:
    max_depth_levels: int = 30          # 🔥 升级：最大分析深度档位（前30档分析）
    safety_margin: float = 0.90         # 安全边际 (90%)
    price_deviation_threshold: float = 0.1  # 价格偏差阈值 (10%)
    min_effective_levels: int = 0       # 🔥 修复：不要求最小档位，遵循"前30档实时分析"设计
    volume_threshold: float = 0.0001    # 最小有效数量阈值
```

#### **DepthAnalysisResult**
```python
@dataclass
class DepthAnalysisResult:
    """🔥 简化结果：只关心深度是否足够套利金额"""
    is_sufficient: bool                 # 深度是否足够套利金额
    total_volume: float = 0.0           # 总数量
    effective_volume: float = 0.0       # 有效数量（扣除安全边际）
    error_message: str = ""             # 错误信息（深度不足时的原因）
```

## 🎯 核心设计原则

- **类型安全**: 使用dataclass和类型注解
- **数据一致性**: 统一的字段命名和格式
- **可扩展性**: 支持新增字段而不破坏兼容性
- **序列化友好**: 支持JSON序列化和反序列化
- 🔥 **前10档实时分析**: 遵循MD文档设计，不要求最小深度，支持部分数据
- 🔥 **统一深度分析**: 使用UnifiedDepthAnalyzer消除重复计算逻辑

## 📋 1. 枚举类定义

### 1.1 交易相关枚举

```python
# 文件位置: exchanges/exchanges_base.py

from enum import Enum
from typing import Optional, Dict, Any, List
from dataclasses import dataclass, field
from decimal import Decimal
import time

class OrderSide(Enum):
    """订单方向枚举"""
    BUY = "buy"
    SELL = "sell"

class OrderType(Enum):
    """订单类型枚举 - 🔥 纯市价单系统"""
    MARKET = "market"
    # LIMIT和LIMIT_MAKER已彻底删除

class AccountType(Enum):
    """账户类型枚举"""
    SPOT = "spot"           # 现货账户
    FUTURES = "futures"     # 期货账户
    UNIFIED = "unified"     # 统一账户 (Bybit/OKX)

# 注意：MarketType和PositionSide枚举在实际代码中不存在
# 实际使用字符串值：
# market_type: "spot" | "futures"
# position_side: "long" | "short" | "both"

class OrderStatus(Enum):
    """订单状态枚举"""
    PENDING = "pending"
    OPEN = "open"
    PARTIAL = "partial"
    FILLED = "filled"
    CANCELLED = "cancelled"
    FAILED = "failed"
```

### 1.2 系统状态枚举

```python
# 文件位置: core/arbitrage_engine.py
class ArbitrageStatus(Enum):
    """套利状态枚举"""
    IDLE = "idle"
    SCANNING = "scanning"
    PREPARING = "preparing"
    EXECUTING = "executing"
    WAITING_CONVERGENCE = "waiting_convergence"
    CLOSING = "closing"
    COMPLETED = "completed"
    ERROR = "error"

# 文件位置: monitoring/position_monitor.py
class PositionType(Enum):
    """仓位类型枚举"""
    SPOT = "spot"
    FUTURES = "futures"

class PositionStatus(Enum):
    """仓位状态枚举"""
    NORMAL = "normal"
    IMBALANCED = "imbalanced"
    MISSING = "missing"
    EXCESS = "excess"
    ERROR = "error"

# 注意：ConnectionStatus和CacheType枚举在实际代码中不存在
# 实际使用字符串值或其他方式处理连接状态和缓存类型
```

## 🏗️ 2. 配置类定义

### 2.1 交易所配置

```python
@dataclass
class ExchangeConfig:
    """交易所配置数据类"""
    name: str
    default_leverage: int
    max_leverage: float
    recv_window: str = "5000"
    min_order_amount_usd: float = 90.0
    api_url: Optional[str] = None
    rate_limit: int = 10  # 每秒请求限制
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "name": self.name,
            "default_leverage": self.default_leverage,
            "max_leverage": self.max_leverage,
            "recv_window": self.recv_window,
            "min_order_amount_usd": self.min_order_amount_usd,
            "api_url": self.api_url,
            "rate_limit": self.rate_limit
        }

@dataclass
class TradingRule:
    """交易规则数据类"""
    symbol: str
    exchange: str
    market_type: str  # spot/futures

    # 步长信息 - 🔥 使用Decimal确保精度
    qty_step: Decimal
    price_step: Decimal

    # 精度信息
    qty_precision: int
    price_precision: int

    # 限制信息 - 🔥 使用Decimal确保精度
    min_qty: Decimal
    max_qty: Decimal
    min_notional: Decimal

    # 元数据
    source: str
    timestamp: float

    def is_valid_quantity(self, qty: Decimal) -> bool:
        """检查数量是否有效"""
        if qty < self.min_qty or qty > self.max_qty:
            return False

        # 检查步长合规性
        remainder = qty % self.qty_step
        return abs(remainder) < Decimal('1e-12') or abs(remainder - self.qty_step) < Decimal('1e-12')

    def check_min_order_compliance(self, qty: Decimal, price: Decimal = None) -> tuple[bool, str]:
        """检查最小订单合规性"""
        # 检查最小数量
        if qty < self.min_qty:
            return False, f"数量{qty}小于最小值{self.min_qty}"

        # 检查最小名义价值（如果有价格）
        if price and self.min_notional:
            notional_value = qty * price
            if notional_value < self.min_notional:
                return False, f"订单价值{notional_value}小于最小值{self.min_notional}"

        return True, "合规"

    def truncate_quantity(self, qty: Decimal) -> Decimal:
        """精准截取数量（不四舍五入）"""
        truncated = (qty // self.qty_step) * self.qty_step
        # 确保不小于最小量
        if truncated < self.min_qty:
            # 向上调整到最小量的整数倍
            min_steps = (self.min_qty // self.qty_step) + (1 if self.min_qty % self.qty_step > 0 else 0)
            truncated = min_steps * self.qty_step
        return truncated

    def truncate_price(self, price: Decimal) -> Decimal:
        """精准截取价格（不四舍五入）"""
        return (price // self.price_step) * self.price_step
```

### 2.2 缓存配置

```python
# 注意：CacheConfig类在实际代码中不存在
# 实际缓存配置通过环境变量和硬编码TTL值管理：
#
# 缓存TTL配置（在各自模块中）：
# - 交易规则缓存: 24小时 (TradingRulesPreloader)
# - 精度缓存: 1小时 (TradingRulesPreloader)
# - 对冲质量缓存: 10秒 (TradingRulesPreloader)
# - 余额缓存: 30秒 (UnifiedBalanceManager)
# - 保证金缓存: 5分钟 (MarginCalculator)
```

## 📊 3. 业务数据类

### 3.1 套利机会数据

```python
@dataclass
class ArbitrageOpportunity:
    """套利机会数据类"""
    symbol: str                   # 交易对（如BTC-USDT）
    base_amount: float            # 基准数量（如0.0001 BTC）

    # 交易所和价格信息
    exchange1_name: str           # 交易所1名称
    exchange1_market: str         # 交易所1市场类型（spot/futures）
    exchange1_price: float        # 交易所1价格
    exchange1_value: float        # 交易所1该数量的价值（USDT）

    exchange2_name: str           # 交易所2名称
    exchange2_market: str         # 交易所2市场类型（spot/futures）
    exchange2_price: float        # 交易所2价格
    exchange2_value: float        # 交易所2该数量的价值（USDT）

    # 套利数据
    spread_value: float           # 价差（USDT）
    spread_percent: float         # 价差百分比
    profit_estimate: float        # 预估利润（USDT）

    # 执行方向
    buy_exchange: str            # 买入交易所
    buy_market: str              # 买入市场类型
    sell_exchange: str           # 卖出交易所
    sell_market: str             # 卖出市场类型

    # 时间戳
    timestamp: int               # 发现时间戳

    def is_valid(self) -> bool:
        """检查机会是否有效"""
        return (
            self.spread_percent > 0.002 and  # 大于0.2%
            self.base_amount > 0 and
            self.profit_estimate > 0
        )

# 注意：ExecutionParams在实际代码中以字典形式存在，而非dataclass
# 实际使用的执行参数格式：
# params = {
#     'total_amount': float,
#     'split_amounts': List[float],
#     'spot_price': float,
#     'futures_price': float,
#     'spot_depth': Dict,
#     'futures_depth': Dict,
#     'futures_exchange': str,
#     'spot_exchange': str,
#     'symbol': str,
#     'futures_amount': float,
#     'buy_market': str,
#     'sell_market': str
# }
```

### 3.2 余额和持仓数据

```python
# 注意：BalanceInfo在实际代码中以字典形式存在，而非独立的dataclass
# 实际使用的余额数据格式：
# balance_data = {
#     "BTC": {"available": 1.0, "locked": 0.0, "total": 1.0},
#     "USDT": {"available": 5000.0, "locked": 0.0, "total": 5000.0}
# }
# 或简化格式：
# balance_data = {"BTC": 1.0, "USDT": 5000.0}

@dataclass
class PositionInfo:
    """持仓信息数据类"""
    exchange: str
    symbol: str
    position_type: str  # PositionType枚举值：SPOT/FUTURES
    side: str  # buy/sell, long/short
    quantity: float
    value: float  # USDT价值
    average_price: float
    unrealized_pnl: float
    fee_paid: float
    timestamp: int
    status: str = "NORMAL"  # PositionStatus枚举值

    def to_dict(self) -> Dict:
        """转换为字典格式"""
        return {
            "exchange": self.exchange,
            "symbol": self.symbol,
            "position_type": self.position_type,
            "side": self.side,
            "quantity": self.quantity,
            "value": self.value,
            "average_price": self.average_price,
            "unrealized_pnl": self.unrealized_pnl,
            "fee_paid": self.fee_paid,
            "timestamp": self.timestamp,
            "status": self.status
        }
```

---

**📝 注意**: 本文档为第一部分，描述基础类型和配置类。完整数据结构定义请参考其他部分文档。
