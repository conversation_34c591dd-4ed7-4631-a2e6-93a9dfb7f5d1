# CreateUniLoan

<PERSON> or repay
## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**currency** | **str** | Currency | 
**type** | **str** | type: borrow - borrow, repay - repay | 
**amount** | **str** | The amount of lending or repaying | 
**repaid_all** | **bool** | Full repayment.  Repay operation only.  If the value is &#x60;true&#x60;, the amount will be ignored and the loan will be repaid in full. | [optional] 
**currency_pair** | **str** | Currency pair | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


