# coding: utf-8

"""
    Gate API v4

    Welcome to Gate.io API  APIv4 provides spot, margin and futures trading operations. There are public APIs to retrieve the real-time market statistics, and private APIs which needs authentication to trade on user's behalf.  # noqa: E501

    Contact: <EMAIL>
    Generated by: https://openapi-generator.tech
"""


import pprint
import re  # noqa: F401

import six

from gate_api.configuration import Configuration


class UserTotalAmount(object):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        'borrow_amount': 'str',
        'collateral_amount': 'str'
    }

    attribute_map = {
        'borrow_amount': 'borrow_amount',
        'collateral_amount': 'collateral_amount'
    }

    def __init__(self, borrow_amount=None, collateral_amount=None, local_vars_configuration=None):  # noqa: E501
        # type: (str, str, Configuration) -> None
        """UserTotalAmount - a model defined in OpenAPI"""  # noqa: E501
        if local_vars_configuration is None:
            local_vars_configuration = Configuration()
        self.local_vars_configuration = local_vars_configuration

        self._borrow_amount = None
        self._collateral_amount = None
        self.discriminator = None

        if borrow_amount is not None:
            self.borrow_amount = borrow_amount
        if collateral_amount is not None:
            self.collateral_amount = collateral_amount

    @property
    def borrow_amount(self):
        """Gets the borrow_amount of this UserTotalAmount.  # noqa: E501

        Total borrowing amount, calculated in USDT  # noqa: E501

        :return: The borrow_amount of this UserTotalAmount.  # noqa: E501
        :rtype: str
        """
        return self._borrow_amount

    @borrow_amount.setter
    def borrow_amount(self, borrow_amount):
        """Sets the borrow_amount of this UserTotalAmount.

        Total borrowing amount, calculated in USDT  # noqa: E501

        :param borrow_amount: The borrow_amount of this UserTotalAmount.  # noqa: E501
        :type: str
        """

        self._borrow_amount = borrow_amount

    @property
    def collateral_amount(self):
        """Gets the collateral_amount of this UserTotalAmount.  # noqa: E501

        Total collateral amount, calculated in USDT  # noqa: E501

        :return: The collateral_amount of this UserTotalAmount.  # noqa: E501
        :rtype: str
        """
        return self._collateral_amount

    @collateral_amount.setter
    def collateral_amount(self, collateral_amount):
        """Sets the collateral_amount of this UserTotalAmount.

        Total collateral amount, calculated in USDT  # noqa: E501

        :param collateral_amount: The collateral_amount of this UserTotalAmount.  # noqa: E501
        :type: str
        """

        self._collateral_amount = collateral_amount

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.openapi_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, UserTotalAmount):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, UserTotalAmount):
            return True

        return self.to_dict() != other.to_dict()
