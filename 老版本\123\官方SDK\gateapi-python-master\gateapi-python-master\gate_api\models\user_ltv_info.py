# coding: utf-8

"""
    Gate API v4

    Welcome to Gate.io API  APIv4 provides spot, margin and futures trading operations. There are public APIs to retrieve the real-time market statistics, and private APIs which needs authentication to trade on user's behalf.  # noqa: E501

    Contact: <EMAIL>
    Generated by: https://openapi-generator.tech
"""


import pprint
import re  # noqa: F401

import six

from gate_api.configuration import Configuration


class UserLtvInfo(object):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        'collateral_currency': 'str',
        'borrow_currency': 'str',
        'init_ltv': 'str',
        'alert_ltv': 'str',
        'liquidate_ltv': 'str',
        'min_borrow_amount': 'str',
        'left_borrowable_amount': 'str'
    }

    attribute_map = {
        'collateral_currency': 'collateral_currency',
        'borrow_currency': 'borrow_currency',
        'init_ltv': 'init_ltv',
        'alert_ltv': 'alert_ltv',
        'liquidate_ltv': 'liquidate_ltv',
        'min_borrow_amount': 'min_borrow_amount',
        'left_borrowable_amount': 'left_borrowable_amount'
    }

    def __init__(self, collateral_currency=None, borrow_currency=None, init_ltv=None, alert_ltv=None, liquidate_ltv=None, min_borrow_amount=None, left_borrowable_amount=None, local_vars_configuration=None):  # noqa: E501
        # type: (str, str, str, str, str, str, str, Configuration) -> None
        """UserLtvInfo - a model defined in OpenAPI"""  # noqa: E501
        if local_vars_configuration is None:
            local_vars_configuration = Configuration()
        self.local_vars_configuration = local_vars_configuration

        self._collateral_currency = None
        self._borrow_currency = None
        self._init_ltv = None
        self._alert_ltv = None
        self._liquidate_ltv = None
        self._min_borrow_amount = None
        self._left_borrowable_amount = None
        self.discriminator = None

        if collateral_currency is not None:
            self.collateral_currency = collateral_currency
        if borrow_currency is not None:
            self.borrow_currency = borrow_currency
        if init_ltv is not None:
            self.init_ltv = init_ltv
        if alert_ltv is not None:
            self.alert_ltv = alert_ltv
        if liquidate_ltv is not None:
            self.liquidate_ltv = liquidate_ltv
        if min_borrow_amount is not None:
            self.min_borrow_amount = min_borrow_amount
        if left_borrowable_amount is not None:
            self.left_borrowable_amount = left_borrowable_amount

    @property
    def collateral_currency(self):
        """Gets the collateral_currency of this UserLtvInfo.  # noqa: E501

        Collateral  # noqa: E501

        :return: The collateral_currency of this UserLtvInfo.  # noqa: E501
        :rtype: str
        """
        return self._collateral_currency

    @collateral_currency.setter
    def collateral_currency(self, collateral_currency):
        """Sets the collateral_currency of this UserLtvInfo.

        Collateral  # noqa: E501

        :param collateral_currency: The collateral_currency of this UserLtvInfo.  # noqa: E501
        :type: str
        """

        self._collateral_currency = collateral_currency

    @property
    def borrow_currency(self):
        """Gets the borrow_currency of this UserLtvInfo.  # noqa: E501

        Borrowed currency  # noqa: E501

        :return: The borrow_currency of this UserLtvInfo.  # noqa: E501
        :rtype: str
        """
        return self._borrow_currency

    @borrow_currency.setter
    def borrow_currency(self, borrow_currency):
        """Sets the borrow_currency of this UserLtvInfo.

        Borrowed currency  # noqa: E501

        :param borrow_currency: The borrow_currency of this UserLtvInfo.  # noqa: E501
        :type: str
        """

        self._borrow_currency = borrow_currency

    @property
    def init_ltv(self):
        """Gets the init_ltv of this UserLtvInfo.  # noqa: E501

        The initial collateralization rate  # noqa: E501

        :return: The init_ltv of this UserLtvInfo.  # noqa: E501
        :rtype: str
        """
        return self._init_ltv

    @init_ltv.setter
    def init_ltv(self, init_ltv):
        """Sets the init_ltv of this UserLtvInfo.

        The initial collateralization rate  # noqa: E501

        :param init_ltv: The init_ltv of this UserLtvInfo.  # noqa: E501
        :type: str
        """

        self._init_ltv = init_ltv

    @property
    def alert_ltv(self):
        """Gets the alert_ltv of this UserLtvInfo.  # noqa: E501

        Warning collateralization ratio  # noqa: E501

        :return: The alert_ltv of this UserLtvInfo.  # noqa: E501
        :rtype: str
        """
        return self._alert_ltv

    @alert_ltv.setter
    def alert_ltv(self, alert_ltv):
        """Sets the alert_ltv of this UserLtvInfo.

        Warning collateralization ratio  # noqa: E501

        :param alert_ltv: The alert_ltv of this UserLtvInfo.  # noqa: E501
        :type: str
        """

        self._alert_ltv = alert_ltv

    @property
    def liquidate_ltv(self):
        """Gets the liquidate_ltv of this UserLtvInfo.  # noqa: E501

        The liquidation collateralization rate  # noqa: E501

        :return: The liquidate_ltv of this UserLtvInfo.  # noqa: E501
        :rtype: str
        """
        return self._liquidate_ltv

    @liquidate_ltv.setter
    def liquidate_ltv(self, liquidate_ltv):
        """Sets the liquidate_ltv of this UserLtvInfo.

        The liquidation collateralization rate  # noqa: E501

        :param liquidate_ltv: The liquidate_ltv of this UserLtvInfo.  # noqa: E501
        :type: str
        """

        self._liquidate_ltv = liquidate_ltv

    @property
    def min_borrow_amount(self):
        """Gets the min_borrow_amount of this UserLtvInfo.  # noqa: E501

        Minimum borrowable amount for the loan currency  # noqa: E501

        :return: The min_borrow_amount of this UserLtvInfo.  # noqa: E501
        :rtype: str
        """
        return self._min_borrow_amount

    @min_borrow_amount.setter
    def min_borrow_amount(self, min_borrow_amount):
        """Sets the min_borrow_amount of this UserLtvInfo.

        Minimum borrowable amount for the loan currency  # noqa: E501

        :param min_borrow_amount: The min_borrow_amount of this UserLtvInfo.  # noqa: E501
        :type: str
        """

        self._min_borrow_amount = min_borrow_amount

    @property
    def left_borrowable_amount(self):
        """Gets the left_borrowable_amount of this UserLtvInfo.  # noqa: E501

        Remaining borrowable amount for the loan currency  # noqa: E501

        :return: The left_borrowable_amount of this UserLtvInfo.  # noqa: E501
        :rtype: str
        """
        return self._left_borrowable_amount

    @left_borrowable_amount.setter
    def left_borrowable_amount(self, left_borrowable_amount):
        """Sets the left_borrowable_amount of this UserLtvInfo.

        Remaining borrowable amount for the loan currency  # noqa: E501

        :param left_borrowable_amount: The left_borrowable_amount of this UserLtvInfo.  # noqa: E501
        :type: str
        """

        self._left_borrowable_amount = left_borrowable_amount

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.openapi_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, UserLtvInfo):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, UserLtvInfo):
            return True

        return self.to_dict() != other.to_dict()
