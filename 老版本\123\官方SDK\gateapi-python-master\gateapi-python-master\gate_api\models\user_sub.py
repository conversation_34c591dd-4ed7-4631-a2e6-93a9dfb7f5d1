# coding: utf-8

"""
    Gate API v4

    Welcome to Gate.io API  APIv4 provides spot, margin and futures trading operations. There are public APIs to retrieve the real-time market statistics, and private APIs which needs authentication to trade on user's behalf.  # noqa: E501

    Contact: <EMAIL>
    Generated by: https://openapi-generator.tech
"""


import pprint
import re  # noqa: F401

import six

from gate_api.configuration import Configuration


class UserSub(object):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        'uid': 'int',
        'belong': 'str',
        'type': 'int',
        'ref_uid': 'int'
    }

    attribute_map = {
        'uid': 'uid',
        'belong': 'belong',
        'type': 'type',
        'ref_uid': 'ref_uid'
    }

    def __init__(self, uid=None, belong=None, type=None, ref_uid=None, local_vars_configuration=None):  # noqa: E501
        # type: (int, str, int, int, Configuration) -> None
        """UserSub - a model defined in OpenAPI"""  # noqa: E501
        if local_vars_configuration is None:
            local_vars_configuration = Configuration()
        self.local_vars_configuration = local_vars_configuration

        self._uid = None
        self._belong = None
        self._type = None
        self._ref_uid = None
        self.discriminator = None

        if uid is not None:
            self.uid = uid
        if belong is not None:
            self.belong = belong
        if type is not None:
            self.type = type
        if ref_uid is not None:
            self.ref_uid = ref_uid

    @property
    def uid(self):
        """Gets the uid of this UserSub.  # noqa: E501

        User ID  # noqa: E501

        :return: The uid of this UserSub.  # noqa: E501
        :rtype: int
        """
        return self._uid

    @uid.setter
    def uid(self, uid):
        """Sets the uid of this UserSub.

        User ID  # noqa: E501

        :param uid: The uid of this UserSub.  # noqa: E501
        :type: int
        """

        self._uid = uid

    @property
    def belong(self):
        """Gets the belong of this UserSub.  # noqa: E501

        The system to which the user belongs (partner referral). If empty, it means not belonging to any system.  # noqa: E501

        :return: The belong of this UserSub.  # noqa: E501
        :rtype: str
        """
        return self._belong

    @belong.setter
    def belong(self, belong):
        """Sets the belong of this UserSub.

        The system to which the user belongs (partner referral). If empty, it means not belonging to any system.  # noqa: E501

        :param belong: The belong of this UserSub.  # noqa: E501
        :type: str
        """

        self._belong = belong

    @property
    def type(self):
        """Gets the type of this UserSub.  # noqa: E501

        Type (0-not in the system 1-direct subordinate agent 2-indirect subordinate agent 3-direct direct customer 4-indirect direct customer 5-ordinary user)  # noqa: E501

        :return: The type of this UserSub.  # noqa: E501
        :rtype: int
        """
        return self._type

    @type.setter
    def type(self, type):
        """Sets the type of this UserSub.

        Type (0-not in the system 1-direct subordinate agent 2-indirect subordinate agent 3-direct direct customer 4-indirect direct customer 5-ordinary user)  # noqa: E501

        :param type: The type of this UserSub.  # noqa: E501
        :type: int
        """

        self._type = type

    @property
    def ref_uid(self):
        """Gets the ref_uid of this UserSub.  # noqa: E501

        Inviter user ID  # noqa: E501

        :return: The ref_uid of this UserSub.  # noqa: E501
        :rtype: int
        """
        return self._ref_uid

    @ref_uid.setter
    def ref_uid(self, ref_uid):
        """Sets the ref_uid of this UserSub.

        Inviter user ID  # noqa: E501

        :param ref_uid: The ref_uid of this UserSub.  # noqa: E501
        :type: int
        """

        self._ref_uid = ref_uid

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.openapi_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, UserSub):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, UserSub):
            return True

        return self.to_dict() != other.to_dict()
