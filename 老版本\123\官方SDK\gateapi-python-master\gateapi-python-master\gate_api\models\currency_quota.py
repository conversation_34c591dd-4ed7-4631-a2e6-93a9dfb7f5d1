# coding: utf-8

"""
    Gate API v4

    Welcome to Gate.io API  APIv4 provides spot, margin and futures trading operations. There are public APIs to retrieve the real-time market statistics, and private APIs which needs authentication to trade on user's behalf.  # noqa: E501

    Contact: <EMAIL>
    Generated by: https://openapi-generator.tech
"""


import pprint
import re  # noqa: F401

import six

from gate_api.configuration import Configuration


class CurrencyQuota(object):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        'currency': 'str',
        'index_price': 'str',
        'min_quota': 'str',
        'left_quota': 'str',
        'left_quote_usdt': 'str'
    }

    attribute_map = {
        'currency': 'currency',
        'index_price': 'index_price',
        'min_quota': 'min_quota',
        'left_quota': 'left_quota',
        'left_quote_usdt': 'left_quote_usdt'
    }

    def __init__(self, currency=None, index_price=None, min_quota=None, left_quota=None, left_quote_usdt=None, local_vars_configuration=None):  # noqa: E501
        # type: (str, str, str, str, str, Configuration) -> None
        """CurrencyQuota - a model defined in OpenAPI"""  # noqa: E501
        if local_vars_configuration is None:
            local_vars_configuration = Configuration()
        self.local_vars_configuration = local_vars_configuration

        self._currency = None
        self._index_price = None
        self._min_quota = None
        self._left_quota = None
        self._left_quote_usdt = None
        self.discriminator = None

        if currency is not None:
            self.currency = currency
        if index_price is not None:
            self.index_price = index_price
        if min_quota is not None:
            self.min_quota = min_quota
        if left_quota is not None:
            self.left_quota = left_quota
        if left_quote_usdt is not None:
            self.left_quote_usdt = left_quote_usdt

    @property
    def currency(self):
        """Gets the currency of this CurrencyQuota.  # noqa: E501

        Currency  # noqa: E501

        :return: The currency of this CurrencyQuota.  # noqa: E501
        :rtype: str
        """
        return self._currency

    @currency.setter
    def currency(self, currency):
        """Sets the currency of this CurrencyQuota.

        Currency  # noqa: E501

        :param currency: The currency of this CurrencyQuota.  # noqa: E501
        :type: str
        """

        self._currency = currency

    @property
    def index_price(self):
        """Gets the index_price of this CurrencyQuota.  # noqa: E501

        Currency Index Price  # noqa: E501

        :return: The index_price of this CurrencyQuota.  # noqa: E501
        :rtype: str
        """
        return self._index_price

    @index_price.setter
    def index_price(self, index_price):
        """Sets the index_price of this CurrencyQuota.

        Currency Index Price  # noqa: E501

        :param index_price: The index_price of this CurrencyQuota.  # noqa: E501
        :type: str
        """

        self._index_price = index_price

    @property
    def min_quota(self):
        """Gets the min_quota of this CurrencyQuota.  # noqa: E501

        Minimum borrowing/collateral quota for the currency  # noqa: E501

        :return: The min_quota of this CurrencyQuota.  # noqa: E501
        :rtype: str
        """
        return self._min_quota

    @min_quota.setter
    def min_quota(self, min_quota):
        """Sets the min_quota of this CurrencyQuota.

        Minimum borrowing/collateral quota for the currency  # noqa: E501

        :param min_quota: The min_quota of this CurrencyQuota.  # noqa: E501
        :type: str
        """

        self._min_quota = min_quota

    @property
    def left_quota(self):
        """Gets the left_quota of this CurrencyQuota.  # noqa: E501

        Remaining borrowing/collateral limit for the currency  # noqa: E501

        :return: The left_quota of this CurrencyQuota.  # noqa: E501
        :rtype: str
        """
        return self._left_quota

    @left_quota.setter
    def left_quota(self, left_quota):
        """Sets the left_quota of this CurrencyQuota.

        Remaining borrowing/collateral limit for the currency  # noqa: E501

        :param left_quota: The left_quota of this CurrencyQuota.  # noqa: E501
        :type: str
        """

        self._left_quota = left_quota

    @property
    def left_quote_usdt(self):
        """Gets the left_quote_usdt of this CurrencyQuota.  # noqa: E501

        Remaining currency limit converted to USDT  # noqa: E501

        :return: The left_quote_usdt of this CurrencyQuota.  # noqa: E501
        :rtype: str
        """
        return self._left_quote_usdt

    @left_quote_usdt.setter
    def left_quote_usdt(self, left_quote_usdt):
        """Sets the left_quote_usdt of this CurrencyQuota.

        Remaining currency limit converted to USDT  # noqa: E501

        :param left_quote_usdt: The left_quote_usdt of this CurrencyQuota.  # noqa: E501
        :type: str
        """

        self._left_quote_usdt = left_quote_usdt

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.openapi_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CurrencyQuota):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CurrencyQuota):
            return True

        return self.to_dict() != other.to_dict()
