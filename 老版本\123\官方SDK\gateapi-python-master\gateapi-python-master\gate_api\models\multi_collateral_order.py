# coding: utf-8

"""
    Gate API v4

    Welcome to Gate.io API  APIv4 provides spot, margin and futures trading operations. There are public APIs to retrieve the real-time market statistics, and private APIs which needs authentication to trade on user's behalf.  # noqa: E501

    Contact: <EMAIL>
    Generated by: https://openapi-generator.tech
"""


import pprint
import re  # noqa: F401

import six

from gate_api.configuration import Configuration


class MultiCollateralOrder(object):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        'order_id': 'str',
        'order_type': 'str',
        'fixed_type': 'str',
        'fixed_rate': 'str',
        'expire_time': 'int',
        'auto_renew': 'bool',
        'auto_repay': 'bool',
        'current_ltv': 'str',
        'status': 'str',
        'borrow_time': 'int',
        'total_left_repay_usdt': 'str',
        'total_left_collateral_usdt': 'str',
        'borrow_currencies': 'list[BorrowCurrencyInfo]',
        'collateral_currencies': 'list[CollateralCurrencyInfo]'
    }

    attribute_map = {
        'order_id': 'order_id',
        'order_type': 'order_type',
        'fixed_type': 'fixed_type',
        'fixed_rate': 'fixed_rate',
        'expire_time': 'expire_time',
        'auto_renew': 'auto_renew',
        'auto_repay': 'auto_repay',
        'current_ltv': 'current_ltv',
        'status': 'status',
        'borrow_time': 'borrow_time',
        'total_left_repay_usdt': 'total_left_repay_usdt',
        'total_left_collateral_usdt': 'total_left_collateral_usdt',
        'borrow_currencies': 'borrow_currencies',
        'collateral_currencies': 'collateral_currencies'
    }

    def __init__(self, order_id=None, order_type=None, fixed_type=None, fixed_rate=None, expire_time=None, auto_renew=None, auto_repay=None, current_ltv=None, status=None, borrow_time=None, total_left_repay_usdt=None, total_left_collateral_usdt=None, borrow_currencies=None, collateral_currencies=None, local_vars_configuration=None):  # noqa: E501
        # type: (str, str, str, str, int, bool, bool, str, str, int, str, str, list[BorrowCurrencyInfo], list[CollateralCurrencyInfo], Configuration) -> None
        """MultiCollateralOrder - a model defined in OpenAPI"""  # noqa: E501
        if local_vars_configuration is None:
            local_vars_configuration = Configuration()
        self.local_vars_configuration = local_vars_configuration

        self._order_id = None
        self._order_type = None
        self._fixed_type = None
        self._fixed_rate = None
        self._expire_time = None
        self._auto_renew = None
        self._auto_repay = None
        self._current_ltv = None
        self._status = None
        self._borrow_time = None
        self._total_left_repay_usdt = None
        self._total_left_collateral_usdt = None
        self._borrow_currencies = None
        self._collateral_currencies = None
        self.discriminator = None

        if order_id is not None:
            self.order_id = order_id
        if order_type is not None:
            self.order_type = order_type
        if fixed_type is not None:
            self.fixed_type = fixed_type
        if fixed_rate is not None:
            self.fixed_rate = fixed_rate
        if expire_time is not None:
            self.expire_time = expire_time
        if auto_renew is not None:
            self.auto_renew = auto_renew
        if auto_repay is not None:
            self.auto_repay = auto_repay
        if current_ltv is not None:
            self.current_ltv = current_ltv
        if status is not None:
            self.status = status
        if borrow_time is not None:
            self.borrow_time = borrow_time
        if total_left_repay_usdt is not None:
            self.total_left_repay_usdt = total_left_repay_usdt
        if total_left_collateral_usdt is not None:
            self.total_left_collateral_usdt = total_left_collateral_usdt
        if borrow_currencies is not None:
            self.borrow_currencies = borrow_currencies
        if collateral_currencies is not None:
            self.collateral_currencies = collateral_currencies

    @property
    def order_id(self):
        """Gets the order_id of this MultiCollateralOrder.  # noqa: E501

        Order ID  # noqa: E501

        :return: The order_id of this MultiCollateralOrder.  # noqa: E501
        :rtype: str
        """
        return self._order_id

    @order_id.setter
    def order_id(self, order_id):
        """Sets the order_id of this MultiCollateralOrder.

        Order ID  # noqa: E501

        :param order_id: The order_id of this MultiCollateralOrder.  # noqa: E501
        :type: str
        """

        self._order_id = order_id

    @property
    def order_type(self):
        """Gets the order_type of this MultiCollateralOrder.  # noqa: E501

        current - current, fixed - fixed  # noqa: E501

        :return: The order_type of this MultiCollateralOrder.  # noqa: E501
        :rtype: str
        """
        return self._order_type

    @order_type.setter
    def order_type(self, order_type):
        """Sets the order_type of this MultiCollateralOrder.

        current - current, fixed - fixed  # noqa: E501

        :param order_type: The order_type of this MultiCollateralOrder.  # noqa: E501
        :type: str
        """

        self._order_type = order_type

    @property
    def fixed_type(self):
        """Gets the fixed_type of this MultiCollateralOrder.  # noqa: E501

        Fixed interest rate loan periods: 7d - 7 days, 30d - 30 days.  # noqa: E501

        :return: The fixed_type of this MultiCollateralOrder.  # noqa: E501
        :rtype: str
        """
        return self._fixed_type

    @fixed_type.setter
    def fixed_type(self, fixed_type):
        """Sets the fixed_type of this MultiCollateralOrder.

        Fixed interest rate loan periods: 7d - 7 days, 30d - 30 days.  # noqa: E501

        :param fixed_type: The fixed_type of this MultiCollateralOrder.  # noqa: E501
        :type: str
        """

        self._fixed_type = fixed_type

    @property
    def fixed_rate(self):
        """Gets the fixed_rate of this MultiCollateralOrder.  # noqa: E501

        Fixed interest rate  # noqa: E501

        :return: The fixed_rate of this MultiCollateralOrder.  # noqa: E501
        :rtype: str
        """
        return self._fixed_rate

    @fixed_rate.setter
    def fixed_rate(self, fixed_rate):
        """Sets the fixed_rate of this MultiCollateralOrder.

        Fixed interest rate  # noqa: E501

        :param fixed_rate: The fixed_rate of this MultiCollateralOrder.  # noqa: E501
        :type: str
        """

        self._fixed_rate = fixed_rate

    @property
    def expire_time(self):
        """Gets the expire_time of this MultiCollateralOrder.  # noqa: E501

        Expiration time, timestamp, unit in seconds.  # noqa: E501

        :return: The expire_time of this MultiCollateralOrder.  # noqa: E501
        :rtype: int
        """
        return self._expire_time

    @expire_time.setter
    def expire_time(self, expire_time):
        """Sets the expire_time of this MultiCollateralOrder.

        Expiration time, timestamp, unit in seconds.  # noqa: E501

        :param expire_time: The expire_time of this MultiCollateralOrder.  # noqa: E501
        :type: int
        """

        self._expire_time = expire_time

    @property
    def auto_renew(self):
        """Gets the auto_renew of this MultiCollateralOrder.  # noqa: E501

        Fixed interest rate, automatic renewal  # noqa: E501

        :return: The auto_renew of this MultiCollateralOrder.  # noqa: E501
        :rtype: bool
        """
        return self._auto_renew

    @auto_renew.setter
    def auto_renew(self, auto_renew):
        """Sets the auto_renew of this MultiCollateralOrder.

        Fixed interest rate, automatic renewal  # noqa: E501

        :param auto_renew: The auto_renew of this MultiCollateralOrder.  # noqa: E501
        :type: bool
        """

        self._auto_renew = auto_renew

    @property
    def auto_repay(self):
        """Gets the auto_repay of this MultiCollateralOrder.  # noqa: E501

        Fixed interest rate, automatic repayment  # noqa: E501

        :return: The auto_repay of this MultiCollateralOrder.  # noqa: E501
        :rtype: bool
        """
        return self._auto_repay

    @auto_repay.setter
    def auto_repay(self, auto_repay):
        """Sets the auto_repay of this MultiCollateralOrder.

        Fixed interest rate, automatic repayment  # noqa: E501

        :param auto_repay: The auto_repay of this MultiCollateralOrder.  # noqa: E501
        :type: bool
        """

        self._auto_repay = auto_repay

    @property
    def current_ltv(self):
        """Gets the current_ltv of this MultiCollateralOrder.  # noqa: E501

        The current collateralization rate  # noqa: E501

        :return: The current_ltv of this MultiCollateralOrder.  # noqa: E501
        :rtype: str
        """
        return self._current_ltv

    @current_ltv.setter
    def current_ltv(self, current_ltv):
        """Sets the current_ltv of this MultiCollateralOrder.

        The current collateralization rate  # noqa: E501

        :param current_ltv: The current_ltv of this MultiCollateralOrder.  # noqa: E501
        :type: str
        """

        self._current_ltv = current_ltv

    @property
    def status(self):
        """Gets the status of this MultiCollateralOrder.  # noqa: E501

        Order status: - initial: Initial state after placing the order - collateral_deducted: Collateral deduction successful - collateral_returning: Loan failed - Collateral return pending - lent: Loan successful - repaying: Repayment in progress - liquidating: Liquidation in progress - finished: Order completed - closed_liquidated: Liquidation and repayment completed  # noqa: E501

        :return: The status of this MultiCollateralOrder.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this MultiCollateralOrder.

        Order status: - initial: Initial state after placing the order - collateral_deducted: Collateral deduction successful - collateral_returning: Loan failed - Collateral return pending - lent: Loan successful - repaying: Repayment in progress - liquidating: Liquidation in progress - finished: Order completed - closed_liquidated: Liquidation and repayment completed  # noqa: E501

        :param status: The status of this MultiCollateralOrder.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def borrow_time(self):
        """Gets the borrow_time of this MultiCollateralOrder.  # noqa: E501

        Borrowing time, timestamp in seconds  # noqa: E501

        :return: The borrow_time of this MultiCollateralOrder.  # noqa: E501
        :rtype: int
        """
        return self._borrow_time

    @borrow_time.setter
    def borrow_time(self, borrow_time):
        """Sets the borrow_time of this MultiCollateralOrder.

        Borrowing time, timestamp in seconds  # noqa: E501

        :param borrow_time: The borrow_time of this MultiCollateralOrder.  # noqa: E501
        :type: int
        """

        self._borrow_time = borrow_time

    @property
    def total_left_repay_usdt(self):
        """Gets the total_left_repay_usdt of this MultiCollateralOrder.  # noqa: E501

        Value of Left repay amount converted in USDT  # noqa: E501

        :return: The total_left_repay_usdt of this MultiCollateralOrder.  # noqa: E501
        :rtype: str
        """
        return self._total_left_repay_usdt

    @total_left_repay_usdt.setter
    def total_left_repay_usdt(self, total_left_repay_usdt):
        """Sets the total_left_repay_usdt of this MultiCollateralOrder.

        Value of Left repay amount converted in USDT  # noqa: E501

        :param total_left_repay_usdt: The total_left_repay_usdt of this MultiCollateralOrder.  # noqa: E501
        :type: str
        """

        self._total_left_repay_usdt = total_left_repay_usdt

    @property
    def total_left_collateral_usdt(self):
        """Gets the total_left_collateral_usdt of this MultiCollateralOrder.  # noqa: E501

        Value of Collateral amount in USDT  # noqa: E501

        :return: The total_left_collateral_usdt of this MultiCollateralOrder.  # noqa: E501
        :rtype: str
        """
        return self._total_left_collateral_usdt

    @total_left_collateral_usdt.setter
    def total_left_collateral_usdt(self, total_left_collateral_usdt):
        """Sets the total_left_collateral_usdt of this MultiCollateralOrder.

        Value of Collateral amount in USDT  # noqa: E501

        :param total_left_collateral_usdt: The total_left_collateral_usdt of this MultiCollateralOrder.  # noqa: E501
        :type: str
        """

        self._total_left_collateral_usdt = total_left_collateral_usdt

    @property
    def borrow_currencies(self):
        """Gets the borrow_currencies of this MultiCollateralOrder.  # noqa: E501

        Borrowing Currency List  # noqa: E501

        :return: The borrow_currencies of this MultiCollateralOrder.  # noqa: E501
        :rtype: list[BorrowCurrencyInfo]
        """
        return self._borrow_currencies

    @borrow_currencies.setter
    def borrow_currencies(self, borrow_currencies):
        """Sets the borrow_currencies of this MultiCollateralOrder.

        Borrowing Currency List  # noqa: E501

        :param borrow_currencies: The borrow_currencies of this MultiCollateralOrder.  # noqa: E501
        :type: list[BorrowCurrencyInfo]
        """

        self._borrow_currencies = borrow_currencies

    @property
    def collateral_currencies(self):
        """Gets the collateral_currencies of this MultiCollateralOrder.  # noqa: E501

        Collateral Currency List  # noqa: E501

        :return: The collateral_currencies of this MultiCollateralOrder.  # noqa: E501
        :rtype: list[CollateralCurrencyInfo]
        """
        return self._collateral_currencies

    @collateral_currencies.setter
    def collateral_currencies(self, collateral_currencies):
        """Sets the collateral_currencies of this MultiCollateralOrder.

        Collateral Currency List  # noqa: E501

        :param collateral_currencies: The collateral_currencies of this MultiCollateralOrder.  # noqa: E501
        :type: list[CollateralCurrencyInfo]
        """

        self._collateral_currencies = collateral_currencies

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.openapi_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, MultiCollateralOrder):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, MultiCollateralOrder):
            return True

        return self.to_dict() != other.to_dict()
