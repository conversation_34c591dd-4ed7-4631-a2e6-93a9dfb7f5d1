# coding: utf-8

"""
    Gate API v4

    Welcome to Gate.io API  APIv4 provides spot, margin and futures trading operations. There are public APIs to retrieve the real-time market statistics, and private APIs which needs authentication to trade on user's behalf.  # noqa: E501

    Contact: <EMAIL>
    Generated by: https://openapi-generator.tech
"""


import pprint
import re  # noqa: F401

import six

from gate_api.configuration import Configuration


class FuturesAccountHistory(object):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        'dnw': 'str',
        'pnl': 'str',
        'fee': 'str',
        'refr': 'str',
        'fund': 'str',
        'point_dnw': 'str',
        'point_fee': 'str',
        'point_refr': 'str',
        'bonus_dnw': 'str',
        'bonus_offset': 'str'
    }

    attribute_map = {
        'dnw': 'dnw',
        'pnl': 'pnl',
        'fee': 'fee',
        'refr': 'refr',
        'fund': 'fund',
        'point_dnw': 'point_dnw',
        'point_fee': 'point_fee',
        'point_refr': 'point_refr',
        'bonus_dnw': 'bonus_dnw',
        'bonus_offset': 'bonus_offset'
    }

    def __init__(self, dnw=None, pnl=None, fee=None, refr=None, fund=None, point_dnw=None, point_fee=None, point_refr=None, bonus_dnw=None, bonus_offset=None, local_vars_configuration=None):  # noqa: E501
        # type: (str, str, str, str, str, str, str, str, str, str, Configuration) -> None
        """FuturesAccountHistory - a model defined in OpenAPI"""  # noqa: E501
        if local_vars_configuration is None:
            local_vars_configuration = Configuration()
        self.local_vars_configuration = local_vars_configuration

        self._dnw = None
        self._pnl = None
        self._fee = None
        self._refr = None
        self._fund = None
        self._point_dnw = None
        self._point_fee = None
        self._point_refr = None
        self._bonus_dnw = None
        self._bonus_offset = None
        self.discriminator = None

        if dnw is not None:
            self.dnw = dnw
        if pnl is not None:
            self.pnl = pnl
        if fee is not None:
            self.fee = fee
        if refr is not None:
            self.refr = refr
        if fund is not None:
            self.fund = fund
        if point_dnw is not None:
            self.point_dnw = point_dnw
        if point_fee is not None:
            self.point_fee = point_fee
        if point_refr is not None:
            self.point_refr = point_refr
        if bonus_dnw is not None:
            self.bonus_dnw = bonus_dnw
        if bonus_offset is not None:
            self.bonus_offset = bonus_offset

    @property
    def dnw(self):
        """Gets the dnw of this FuturesAccountHistory.  # noqa: E501

        total amount of deposit and withdraw  # noqa: E501

        :return: The dnw of this FuturesAccountHistory.  # noqa: E501
        :rtype: str
        """
        return self._dnw

    @dnw.setter
    def dnw(self, dnw):
        """Sets the dnw of this FuturesAccountHistory.

        total amount of deposit and withdraw  # noqa: E501

        :param dnw: The dnw of this FuturesAccountHistory.  # noqa: E501
        :type: str
        """

        self._dnw = dnw

    @property
    def pnl(self):
        """Gets the pnl of this FuturesAccountHistory.  # noqa: E501

        total amount of trading profit and loss  # noqa: E501

        :return: The pnl of this FuturesAccountHistory.  # noqa: E501
        :rtype: str
        """
        return self._pnl

    @pnl.setter
    def pnl(self, pnl):
        """Sets the pnl of this FuturesAccountHistory.

        total amount of trading profit and loss  # noqa: E501

        :param pnl: The pnl of this FuturesAccountHistory.  # noqa: E501
        :type: str
        """

        self._pnl = pnl

    @property
    def fee(self):
        """Gets the fee of this FuturesAccountHistory.  # noqa: E501

        total amount of fee  # noqa: E501

        :return: The fee of this FuturesAccountHistory.  # noqa: E501
        :rtype: str
        """
        return self._fee

    @fee.setter
    def fee(self, fee):
        """Sets the fee of this FuturesAccountHistory.

        total amount of fee  # noqa: E501

        :param fee: The fee of this FuturesAccountHistory.  # noqa: E501
        :type: str
        """

        self._fee = fee

    @property
    def refr(self):
        """Gets the refr of this FuturesAccountHistory.  # noqa: E501

        total amount of referrer rebates  # noqa: E501

        :return: The refr of this FuturesAccountHistory.  # noqa: E501
        :rtype: str
        """
        return self._refr

    @refr.setter
    def refr(self, refr):
        """Sets the refr of this FuturesAccountHistory.

        total amount of referrer rebates  # noqa: E501

        :param refr: The refr of this FuturesAccountHistory.  # noqa: E501
        :type: str
        """

        self._refr = refr

    @property
    def fund(self):
        """Gets the fund of this FuturesAccountHistory.  # noqa: E501

        total amount of funding costs  # noqa: E501

        :return: The fund of this FuturesAccountHistory.  # noqa: E501
        :rtype: str
        """
        return self._fund

    @fund.setter
    def fund(self, fund):
        """Sets the fund of this FuturesAccountHistory.

        total amount of funding costs  # noqa: E501

        :param fund: The fund of this FuturesAccountHistory.  # noqa: E501
        :type: str
        """

        self._fund = fund

    @property
    def point_dnw(self):
        """Gets the point_dnw of this FuturesAccountHistory.  # noqa: E501

        total amount of point deposit and withdraw  # noqa: E501

        :return: The point_dnw of this FuturesAccountHistory.  # noqa: E501
        :rtype: str
        """
        return self._point_dnw

    @point_dnw.setter
    def point_dnw(self, point_dnw):
        """Sets the point_dnw of this FuturesAccountHistory.

        total amount of point deposit and withdraw  # noqa: E501

        :param point_dnw: The point_dnw of this FuturesAccountHistory.  # noqa: E501
        :type: str
        """

        self._point_dnw = point_dnw

    @property
    def point_fee(self):
        """Gets the point_fee of this FuturesAccountHistory.  # noqa: E501

        total amount of point fee  # noqa: E501

        :return: The point_fee of this FuturesAccountHistory.  # noqa: E501
        :rtype: str
        """
        return self._point_fee

    @point_fee.setter
    def point_fee(self, point_fee):
        """Sets the point_fee of this FuturesAccountHistory.

        total amount of point fee  # noqa: E501

        :param point_fee: The point_fee of this FuturesAccountHistory.  # noqa: E501
        :type: str
        """

        self._point_fee = point_fee

    @property
    def point_refr(self):
        """Gets the point_refr of this FuturesAccountHistory.  # noqa: E501

        total amount of referrer rebates of point fee  # noqa: E501

        :return: The point_refr of this FuturesAccountHistory.  # noqa: E501
        :rtype: str
        """
        return self._point_refr

    @point_refr.setter
    def point_refr(self, point_refr):
        """Sets the point_refr of this FuturesAccountHistory.

        total amount of referrer rebates of point fee  # noqa: E501

        :param point_refr: The point_refr of this FuturesAccountHistory.  # noqa: E501
        :type: str
        """

        self._point_refr = point_refr

    @property
    def bonus_dnw(self):
        """Gets the bonus_dnw of this FuturesAccountHistory.  # noqa: E501

        total amount of perpetual contract bonus transfer  # noqa: E501

        :return: The bonus_dnw of this FuturesAccountHistory.  # noqa: E501
        :rtype: str
        """
        return self._bonus_dnw

    @bonus_dnw.setter
    def bonus_dnw(self, bonus_dnw):
        """Sets the bonus_dnw of this FuturesAccountHistory.

        total amount of perpetual contract bonus transfer  # noqa: E501

        :param bonus_dnw: The bonus_dnw of this FuturesAccountHistory.  # noqa: E501
        :type: str
        """

        self._bonus_dnw = bonus_dnw

    @property
    def bonus_offset(self):
        """Gets the bonus_offset of this FuturesAccountHistory.  # noqa: E501

        total amount of perpetual contract bonus deduction  # noqa: E501

        :return: The bonus_offset of this FuturesAccountHistory.  # noqa: E501
        :rtype: str
        """
        return self._bonus_offset

    @bonus_offset.setter
    def bonus_offset(self, bonus_offset):
        """Sets the bonus_offset of this FuturesAccountHistory.

        total amount of perpetual contract bonus deduction  # noqa: E501

        :param bonus_offset: The bonus_offset of this FuturesAccountHistory.  # noqa: E501
        :type: str
        """

        self._bonus_offset = bonus_offset

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.openapi_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, FuturesAccountHistory):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, FuturesAccountHistory):
            return True

        return self.to_dict() != other.to_dict()
