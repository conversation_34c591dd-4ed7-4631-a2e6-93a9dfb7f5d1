# coding: utf-8

"""
    Gate API v4

    Welcome to Gate.io API  APIv4 provides spot, margin and futures trading operations. There are public APIs to retrieve the real-time market statistics, and private APIs which needs authentication to trade on user's behalf.  # noqa: E501

    Contact: <EMAIL>
    Generated by: https://openapi-generator.tech
"""


import pprint
import re  # noqa: F401

import six

from gate_api.configuration import Configuration


class LiquidateOrder(object):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        'text': 'str',
        'currency_pair': 'str',
        'amount': 'str',
        'price': 'str',
        'action_mode': 'str'
    }

    attribute_map = {
        'text': 'text',
        'currency_pair': 'currency_pair',
        'amount': 'amount',
        'price': 'price',
        'action_mode': 'action_mode'
    }

    def __init__(self, text=None, currency_pair=None, amount=None, price=None, action_mode=None, local_vars_configuration=None):  # noqa: E501
        # type: (str, str, str, str, str, Configuration) -> None
        """LiquidateOrder - a model defined in OpenAPI"""  # noqa: E501
        if local_vars_configuration is None:
            local_vars_configuration = Configuration()
        self.local_vars_configuration = local_vars_configuration

        self._text = None
        self._currency_pair = None
        self._amount = None
        self._price = None
        self._action_mode = None
        self.discriminator = None

        if text is not None:
            self.text = text
        self.currency_pair = currency_pair
        self.amount = amount
        self.price = price
        if action_mode is not None:
            self.action_mode = action_mode

    @property
    def text(self):
        """Gets the text of this LiquidateOrder.  # noqa: E501

        User defined information. If not empty, must follow the rules below:  1. prefixed with `t-` 2. no longer than 28 bytes without `t-` prefix 3. can only include 0-9, A-Z, a-z, underscore(_), hyphen(-) or dot(.)   # noqa: E501

        :return: The text of this LiquidateOrder.  # noqa: E501
        :rtype: str
        """
        return self._text

    @text.setter
    def text(self, text):
        """Sets the text of this LiquidateOrder.

        User defined information. If not empty, must follow the rules below:  1. prefixed with `t-` 2. no longer than 28 bytes without `t-` prefix 3. can only include 0-9, A-Z, a-z, underscore(_), hyphen(-) or dot(.)   # noqa: E501

        :param text: The text of this LiquidateOrder.  # noqa: E501
        :type: str
        """

        self._text = text

    @property
    def currency_pair(self):
        """Gets the currency_pair of this LiquidateOrder.  # noqa: E501

        Currency pair  # noqa: E501

        :return: The currency_pair of this LiquidateOrder.  # noqa: E501
        :rtype: str
        """
        return self._currency_pair

    @currency_pair.setter
    def currency_pair(self, currency_pair):
        """Sets the currency_pair of this LiquidateOrder.

        Currency pair  # noqa: E501

        :param currency_pair: The currency_pair of this LiquidateOrder.  # noqa: E501
        :type: str
        """
        if self.local_vars_configuration.client_side_validation and currency_pair is None:  # noqa: E501
            raise ValueError("Invalid value for `currency_pair`, must not be `None`")  # noqa: E501

        self._currency_pair = currency_pair

    @property
    def amount(self):
        """Gets the amount of this LiquidateOrder.  # noqa: E501

        Trade amount  # noqa: E501

        :return: The amount of this LiquidateOrder.  # noqa: E501
        :rtype: str
        """
        return self._amount

    @amount.setter
    def amount(self, amount):
        """Sets the amount of this LiquidateOrder.

        Trade amount  # noqa: E501

        :param amount: The amount of this LiquidateOrder.  # noqa: E501
        :type: str
        """
        if self.local_vars_configuration.client_side_validation and amount is None:  # noqa: E501
            raise ValueError("Invalid value for `amount`, must not be `None`")  # noqa: E501

        self._amount = amount

    @property
    def price(self):
        """Gets the price of this LiquidateOrder.  # noqa: E501

        Order price  # noqa: E501

        :return: The price of this LiquidateOrder.  # noqa: E501
        :rtype: str
        """
        return self._price

    @price.setter
    def price(self, price):
        """Sets the price of this LiquidateOrder.

        Order price  # noqa: E501

        :param price: The price of this LiquidateOrder.  # noqa: E501
        :type: str
        """
        if self.local_vars_configuration.client_side_validation and price is None:  # noqa: E501
            raise ValueError("Invalid value for `price`, must not be `None`")  # noqa: E501

        self._price = price

    @property
    def action_mode(self):
        """Gets the action_mode of this LiquidateOrder.  # noqa: E501

        Processing Mode:  Different fields are returned when placing an order based on action_mode. This field is only valid during the request, and it is not included in the response result ACK: Asynchronous mode, only returns key order fields RESULT: No clearing information FULL: Full mode (default)  # noqa: E501

        :return: The action_mode of this LiquidateOrder.  # noqa: E501
        :rtype: str
        """
        return self._action_mode

    @action_mode.setter
    def action_mode(self, action_mode):
        """Sets the action_mode of this LiquidateOrder.

        Processing Mode:  Different fields are returned when placing an order based on action_mode. This field is only valid during the request, and it is not included in the response result ACK: Asynchronous mode, only returns key order fields RESULT: No clearing information FULL: Full mode (default)  # noqa: E501

        :param action_mode: The action_mode of this LiquidateOrder.  # noqa: E501
        :type: str
        """

        self._action_mode = action_mode

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.openapi_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, LiquidateOrder):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, LiquidateOrder):
            return True

        return self.to_dict() != other.to_dict()
