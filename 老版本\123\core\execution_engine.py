#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔥 ExecutionEngine - 通用期货溢价套利执行引擎
按照全流程工作流.md设计，实现<30ms极速并行执行
支持所有代币，三个交易所统一接口，完整数据链路

核心功能：
1. 🚀 并行执行现货和期货交易 (<30ms目标)
2. 🎯 98%对冲质量预检查
3. 🔄 统一的三交易所接口
4. 📊 完整的数据链路和错误处理
5. 🌍 支持所有代币，零硬编码
"""

import asyncio
import time
import os

import logging
from typing import Dict, List, Optional, Any, Tuple
from enum import Enum
from dataclasses import dataclass, field
from decimal import Decimal

# 导入核心模块
from config.settings import Settings
from core.opportunity_scanner import ArbitrageOpportunity
from exchanges.exchanges_base import OrderStatus, OrderSide, OrderType
from core.unified_opening_manager import get_opening_manager, OpeningResult
from core.unified_closing_manager import get_closing_manager, ClosingResult
from core.trading_rules_preloader import get_trading_rules_preloader
from utils.margin_calculator import MarginCalculator
from utils.helpers import safe_float
from utils.logger import get_logger
# 🔥 修复：添加电报通知系统
from utils.notification import send_trading_error, send_system_error, NotificationLevel


class ExecutionStatus(Enum):
    """执行状态枚举"""
    IDLE = "idle"
    PREPARING = "preparing"
    EXECUTING = "executing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


@dataclass
class ExecutionResult:
    """执行结果数据类"""
    success: bool
    opportunity: Optional[ArbitrageOpportunity] = None
    spot_result: Optional[OpeningResult] = None
    futures_result: Optional[OpeningResult] = None
    start_time: Optional[float] = None
    end_time: Optional[float] = None
    execution_time: Optional[float] = None
    error_message: Optional[str] = None
    hedge_ratio: Optional[float] = None
    total_profit: Optional[float] = None
    # 🔥 修复：添加ArbitrageEngine期望的属性
    spot_orders: List = field(default_factory=list)
    futures_orders: List = field(default_factory=list)
    spot_filled: float = 0.0
    futures_filled: float = 0.0


class ExecutionEngine:
    """
    🔥 ExecutionEngine - 通用期货溢价套利执行引擎

    按照全流程工作流.md设计，实现：
    - <30ms极速并行执行
    - 98%对冲质量预检查
    - 三交易所统一接口
    - 支持所有代币，零硬编码
    """

    def __init__(self):
        """
        🔥 ExecutionEngine初始化
        创建专门的详细日志记录
        """
        # 🔥 创建专门的ExecutionEngine日志器
        self.logger = self._setup_detailed_logger()
        self.config = Settings()

        self.logger.info("=" * 80)
        self.logger.info("🚀 ExecutionEngine初始化开始")
        self.logger.info("=" * 80)

        # 交易所和交易器
        self.exchanges = {}
        self.spot_traders = {}
        self.futures_traders = {}

        # 执行状态
        self.current_status = ExecutionStatus.IDLE
        self.current_execution = None
        self.execution_lock = asyncio.Lock()

        # 统一管理器
        self.opening_manager = None
        self.closing_manager = None

        # 🔥 添加统一模块 - 按照三个交易所统一模块清单  
        self.rules_preloader = get_trading_rules_preloader()
        self.margin_calculator = None

        # 🔥 添加缺失的order_manager属性 - ArbitrageEngine需要
        self.order_manager = None

        # 🔥 WebSocket数据获取器 - 统一管理，避免重复导入
        self._opportunity_scanner = None

        # 🔥 删除重复缓存系统 - 按照全流程工作流.md要求
        # 统一使用：TradingRulesPreloader缓存系统，无重复链路

        # 性能统计
        self.execution_stats = {
            'total_executions': 0,
            'successful_executions': 0,
            'failed_executions': 0,
            'average_execution_time': 0.0,
            'fastest_execution': float('inf'),
            'slowest_execution': 0.0
        }

        self.logger.info("✅ ExecutionEngine初始化完成")

    def _get_websocket_orderbook(self, exchange_name: str, symbol: str, market_type: str) -> Optional[Dict]:
        """
        🔥 增强版本：带重试机制的WebSocket订单簿获取

        Args:
            exchange_name: 交易所名称
            symbol: 交易对
            market_type: 市场类型
            retry_count: 当前重试次数

        Returns:
            标准格式订单簿数据或None
        """
        try:
            # 🔥 导入统一模块
            from websocket.orderbook_validator import get_orderbook_validator
            from websocket.unified_data_formatter import get_orderbook_formatter

            # 初始化OpportunityScanner（避免重复实现）
            if not self._opportunity_scanner:
                from core.arbitrage_engine import get_arbitrage_engine
                engine = get_arbitrage_engine()
                if engine and hasattr(engine, 'opportunity_scanner') and engine.opportunity_scanner:
                    self._opportunity_scanner = engine.opportunity_scanner
                else:
                    self.logger.error("❌ [ORDERBOOK] OpportunityScanner未初始化")
                    return None

            # 🔥 使用与OpportunityScanner完全一致的key格式
            key = f"{exchange_name}_{market_type}_{symbol}"

            # 🔥 零缓存延迟：直接从OpportunityScanner的market_data读取
            with self._opportunity_scanner.data_lock:
                market_data = self._opportunity_scanner.market_data.get(key)

                if not market_data:
                    # 🔥 只在找不到数据时记录错误，减少日志量
                    available_keys = list(self._opportunity_scanner.market_data.keys())
                    similar_keys = [k for k in available_keys if symbol in k or exchange_name in k]

                    self.logger.error(f"❌ [ORDERBOOK] 未找到数据: {key}")
                    if similar_keys:
                        self.logger.error(f"   相似keys: {similar_keys[:3]}...")  # 只显示前3个
                    return None
                
                # 🔥 详细日志：验证market_data结构
                self.logger.debug(f"🔍 [ORDERBOOK] 验证market_data结构:")
                self.logger.debug(f"   market_data类型: {type(market_data)}")
                self.logger.debug(f"   是否有orderbook属性: {hasattr(market_data, 'orderbook')}")

                if not hasattr(market_data, 'orderbook'):
                    self.logger.error(f"❌ [ORDERBOOK] market_data缺少orderbook属性: {key}")
                    self.logger.error(f"   market_data属性: {dir(market_data)}")
                    return None

                orderbook = market_data.orderbook
                self.logger.debug(f"   orderbook类型: {type(orderbook)}")
                self.logger.debug(f"   orderbook是否为dict: {isinstance(orderbook, dict)}")

                if not isinstance(orderbook, dict):
                    self.logger.error(f"❌ [ORDERBOOK] orderbook不是dict类型: {key}")
                    self.logger.error(f"   orderbook内容: {orderbook}")
                    return None

                # 🔥 使用统一验证器验证数据 - 修复版本：支持部分数据
                validator = get_orderbook_validator()
                validation_result = validator.validate_orderbook_data(
                    orderbook, exchange_name, symbol, market_type
                )

                if not validation_result.is_valid:
                    # 🔥 关键修复：智能处理动态订单簿数据，特别是RESOLV-USDT等代币
                    error_msg = validation_result.error_message

                    # 🔥 智能重试机制：对于订单簿为空的情况，进行短暂重试
                    if "订单簿数据为空" in error_msg or "订单簿数据完全为空" in error_msg:
                        import time
                        max_retries = 2
                        retry_delay = 0.1  # 100ms

                        for retry in range(max_retries):
                            self.logger.debug(f"🔄 [ORDERBOOK] {key} 订单簿暂时为空，重试 {retry+1}/{max_retries}")
                            time.sleep(retry_delay)

                            # 重新获取数据
                            market_data_retry = self._opportunity_scanner.market_data.get(key)
                            if market_data_retry and hasattr(market_data_retry, 'orderbook'):
                                orderbook_retry = market_data_retry.orderbook
                                if isinstance(orderbook_retry, dict):
                                    validation_retry = validator.validate_orderbook_data(
                                        orderbook_retry, exchange_name, symbol, market_type
                                    )
                                    if validation_retry.is_valid:
                                        self.logger.info(f"✅ [ORDERBOOK] {key} 重试成功 (尝试 {retry+1})")
                                        # 更新当前数据
                                        orderbook = orderbook_retry
                                        validation_result = validation_retry
                                        break

                        # 如果重试后仍然失败，尝试使用价格数据构建基础订单簿
                        if not validation_result.is_valid:
                            if hasattr(market_data, 'price') and market_data.price > 0:
                                price = market_data.price
                                self.logger.warning(f"⚠️ [ORDERBOOK] {key} 订单簿持续为空，使用价格数据构建基础结构: ${price:.6f}")

                                # 🔥 构建最小可用订单簿结构
                                basic_orderbook = {
                                    'asks': [[price * 1.0005, 0.1]],  # 最小价差和数量
                                    'bids': [[price * 0.9995, 0.1]],
                                    'timestamp': market_data.timestamp,
                                    'symbol': symbol,
                                    'exchange': exchange_name,
                                    'synthetic': True,
                                    'source': 'price_fallback'
                                }

                                formatter = get_orderbook_formatter()
                                result = formatter.format_orderbook_data(
                                    asks=basic_orderbook['asks'],
                                    bids=basic_orderbook['bids'],
                                    symbol=symbol,
                                    exchange=exchange_name,
                                    market_type=market_type,
                                    timestamp=basic_orderbook['timestamp'],
                                    additional_fields={
                                        'asks_count': 1,
                                        'bids_count': 1,
                                        'synthetic': True,
                                        'source': 'price_fallback'
                                    }
                                )

                                self.logger.warning(f"⚠️ [ORDERBOOK] 使用合成订单簿数据: {key}")
                                return result
                            else:
                                self.logger.error(f"❌ [ORDERBOOK] {key} 无法获取有效数据")
                                return None
                    else:
                        # 其他验证错误，直接返回失败
                        self.logger.error(f"❌ [ORDERBOOK] {key} 数据验证失败: {error_msg}")
                        return None

                # 🔥 简化：只记录基本信息
                self.logger.debug(f"📊 [ORDERBOOK] {key} - asks={validation_result.asks_count}, bids={validation_result.bids_count}")

                # 🔥 使用统一格式化器处理数据
                formatter = get_orderbook_formatter()
                asks = orderbook.get('asks', [])
                bids = orderbook.get('bids', [])

                result = formatter.format_orderbook_data(
                    asks=asks,
                    bids=bids,
                    symbol=symbol,
                    exchange=exchange_name,
                    market_type=market_type,
                    timestamp=orderbook.get('timestamp'),
                    additional_fields={
                        'asks_count': validation_result.asks_count,
                        'bids_count': validation_result.bids_count
                    }
                )

                # 🔥 记录成功日志
                self.logger.info(f"✅ [ORDERBOOK] 成功获取订单簿: {key}")
                self.logger.info(f"   数据质量: asks={validation_result.asks_count}档, bids={validation_result.bids_count}档")
                # 🔥 修复：删除已删除的质量评分字段，遵循简化设计
                # self.logger.info(f"   数据延迟: {validation_result.data_age_ms/1000:.2f}秒, 质量评分: {validation_result.quality_score:.2f}")

                return result
                
        except Exception as e:
            self.logger.error(f"❌ [ORDERBOOK] 获取WebSocket订单簿数据异常: {e}", exc_info=True)
            self.logger.error(f"   异常类型: {type(e).__name__}")
            self.logger.error(f"   异常参数: exchange_name={exchange_name}, symbol={symbol}, market_type={market_type}")



            return None

    async def _get_websocket_orderbook_async(self, exchange_name: str, symbol: str, market_type: str) -> Optional[Dict]:
        """
        🔥 异步版本：零缓存延迟WebSocket订单簿获取
        """
        return self._get_websocket_orderbook(exchange_name, symbol, market_type)

    def _setup_detailed_logger(self):
        """
        🔥 设置详细的ExecutionEngine日志 - 使用统一日志系统
        """
        import logging

        # 🔥 使用统一日志系统，不再创建独立的日志配置
        logger = logging.getLogger("ExecutionEngine")
        logger.setLevel(logging.DEBUG)

        # 确保日志传播到根日志器，由统一日志系统处理
        logger.propagate = True

        # 记录日志系统启动
        logger.info(f"🔥 ExecutionEngine日志系统启动 - 使用统一日志配置")

        return logger

    async def initialize(self):
        """
        初始化执行引擎
        按照全流程工作流.md的启动阶段设计
        """
        try:
            self.logger.info("🚀 初始化ExecutionEngine...")

            # 1. 初始化交易所
            await self._initialize_exchanges()

            # 2. 初始化交易器
            await self._initialize_traders()

            # 3. 初始化统一管理器
            await self._initialize_managers()

            # 4. 初始化订单管理器
            await self._initialize_order_manager()

            # 🔥 关键修复：5. 预加载交易规则 - 按照全流程工作流.md
            await self._preload_trading_rules()

            self.logger.info("✅ ExecutionEngine初始化完成")
            return True

        except Exception as e:
            self.logger.error(f"❌ ExecutionEngine初始化失败: {e}")
            return False

    async def _initialize_exchanges(self):
        """
        🔥 步骤1: 使用传递的交易所实例 - 避免重复创建
        按照全流程工作流.md的启动阶段设计
        """
        try:
            self.logger.info("🚀 步骤1.1: 检查交易所实例...")

            # 🔥 修复：使用传递的交易所实例，避免重复创建
            if self.exchanges and len(self.exchanges) > 0:
                self.logger.info(f"✅ 使用传递的交易所实例: {list(self.exchanges.keys())}")

                # 验证交易所实例状态
                for name, exchange in self.exchanges.items():
                    if hasattr(exchange, 'is_initialized') and exchange.is_initialized:
                        self.logger.info(f"✅ {name}交易所已初始化")
                    else:
                        self.logger.info(f"🔍 {name}交易所需要初始化...")
                        try:
                            init_success = await exchange.initialize()
                            if init_success:
                                self.logger.info(f"✅ {name}交易所初始化完成")
                            else:
                                self.logger.warning(f"⚠️ {name}交易所初始化失败")
                        except Exception as e:
                            self.logger.warning(f"⚠️ {name}交易所初始化异常: {e}")

                self.logger.info(f"🎯 步骤1.2: 交易所验证完成，可用: {len(self.exchanges)}个")
                return

            # 🔥 如果没有传递交易所实例，则创建新的（兼容性保留）
            self.logger.warning("⚠️ 未传递交易所实例，创建新的交易所实例...")

            # 导入三个交易所
            from exchanges.gate_exchange import GateExchange
            from exchanges.bybit_exchange import BybitExchange
            from exchanges.okx_exchange import OKXExchange

            # 🔥 从环境变量获取API密钥（更可靠）
            self.logger.info("🔍 步骤1.3: 从环境变量获取API密钥...")

            # Gate.io配置
            gate_api_key = os.getenv("GATE_API_KEY", "")
            gate_api_secret = os.getenv("GATE_API_SECRET", "")

            # Bybit配置
            bybit_api_key = os.getenv("BYBIT_API_KEY", "")
            bybit_api_secret = os.getenv("BYBIT_API_SECRET", "")

            # OKX配置
            okx_api_key = os.getenv("OKX_API_KEY", "")
            okx_api_secret = os.getenv("OKX_API_SECRET", "")
            okx_passphrase = os.getenv("OKX_API_PASSPHRASE", "")

            self.logger.info(f"🔍 步骤1.4: API密钥状态检查:")
            self.logger.info(f"   Gate.io: {'✅有效' if gate_api_key and gate_api_secret else '❌缺失'}")
            self.logger.info(f"   Bybit: {'✅有效' if bybit_api_key and bybit_api_secret else '❌缺失'}")
            self.logger.info(f"   OKX: {'✅有效' if okx_api_key and okx_api_secret and okx_passphrase else '❌缺失'}")

            # 🔥 创建交易所实例（带API密钥）
            self.logger.info("🔍 步骤1.5: 创建交易所实例...")
            self.exchanges = {}

            # Gate.io
            if gate_api_key and gate_api_secret:
                self.logger.info("🔍 步骤1.5.1: 创建Gate.io实例...")
                self.exchanges['gate'] = GateExchange(gate_api_key, gate_api_secret)
                self.logger.info("✅ Gate.io实例创建成功")
            else:
                self.logger.warning("⚠️ Gate.io API密钥缺失，跳过初始化")

            # Bybit
            if bybit_api_key and bybit_api_secret:
                self.logger.info("🔍 步骤1.5.2: 创建Bybit实例...")
                self.exchanges['bybit'] = BybitExchange(bybit_api_key, bybit_api_secret)
                self.logger.info("✅ Bybit实例创建成功")
            else:
                self.logger.warning("⚠️ Bybit API密钥缺失，跳过初始化")

            # OKX
            if okx_api_key and okx_api_secret and okx_passphrase:
                self.logger.info("🔍 步骤1.5.3: 创建OKX实例...")
                self.exchanges['okx'] = OKXExchange(okx_api_key, okx_api_secret, okx_passphrase)
                self.logger.info("✅ OKX实例创建成功")
            else:
                self.logger.warning("⚠️ OKX API密钥缺失，跳过初始化")

            # 🔥 初始化交易所连接
            self.logger.info("🔍 步骤1.6: 初始化交易所连接...")
            initialized_count = 0

            for name, exchange in self.exchanges.items():
                try:
                    self.logger.info(f"🔍 步骤1.6.{initialized_count+1}: 初始化{name}交易所...")
                    init_success = await exchange.initialize()
                    if init_success:
                        self.logger.info(f"✅ {name}交易所初始化完成")
                        initialized_count += 1
                    else:
                        self.logger.error(f"❌ {name}交易所初始化失败")
                except Exception as e:
                    self.logger.error(f"❌ {name}交易所初始化异常: {e}")

            self.logger.info(f"🎯 步骤1.7: 交易所初始化完成，成功: {initialized_count}/{len(self.exchanges)}")

            if initialized_count == 0:
                raise Exception("所有交易所初始化失败")

        except Exception as e:
            self.logger.error(f"❌ 步骤1失败 - 交易所初始化失败: {e}")
            raise

    async def _initialize_traders(self):
        """
        🔥 步骤2: 初始化现货和期货交易器 - 统一接口
        按照三个交易所统一模块清单设计
        """
        try:
            self.logger.info("🚀 步骤2.1: 开始初始化交易器...")

            from trading.spot_trader import SpotTrader
            from trading.futures_trader import FuturesTrader

            self.logger.info("🔍 步骤2.2: 导入交易器模块完成")

            # 为每个交易所创建交易器
            trader_count = 0
            for name, exchange in self.exchanges.items():
                self.logger.info(f"🔍 步骤2.3.{trader_count+1}: 为{name}创建交易器...")

                self.spot_traders[name] = SpotTrader(exchange)
                self.logger.info(f"   ✅ {name}现货交易器创建完成")

                self.futures_traders[name] = FuturesTrader(exchange)
                self.logger.info(f"   ✅ {name}期货交易器创建完成")

                trader_count += 1

            self.logger.info(f"🎯 步骤2.4: 交易器初始化完成，共创建: {trader_count*2}个交易器")

        except Exception as e:
            self.logger.error(f"❌ 步骤2失败 - 交易器初始化失败: {e}")
            raise

    async def _initialize_managers(self):
        """
        🔥 步骤3: 初始化统一管理器 - 按照三个交易所统一模块清单
        确保开仓和平仓管理器正确初始化
        """
        try:
            self.logger.info("🚀 步骤3.1: 开始初始化统一管理器...")

            # 获取统一管理器实例
            self.logger.info("🔍 步骤3.2: 获取统一开仓管理器...")
            self.opening_manager = get_opening_manager()
            self.logger.info("✅ 统一开仓管理器获取成功")

            self.logger.info("🔍 步骤3.3: 获取统一平仓管理器...")
            self.closing_manager = get_closing_manager()
            self.logger.info("✅ 统一平仓管理器获取成功")

            # 🔥 新增：初始化统一模块 - 按照全流程工作流.md
            self.logger.info("🔍 步骤3.4: 获取交易规则预加载器...")
            self.rules_preloader = get_trading_rules_preloader()
            self.logger.info("✅ 交易规则预加载器获取成功")

            self.logger.info("🔍 步骤3.5: 初始化保证金计算器...")
            self.margin_calculator = MarginCalculator()
            self.logger.info("✅ 保证金计算器初始化成功")

            # 初始化管理器（如果有initialize方法）
            self.logger.info("🔍 步骤3.6: 检查开仓管理器初始化方法...")
            if hasattr(self.opening_manager, 'initialize'):
                await self.opening_manager.initialize(self.exchanges)
                self.logger.info("✅ 开仓管理器初始化完成")
            else:
                self.logger.info("ℹ️ 开仓管理器无需初始化（已就绪）")

            self.logger.info("🔍 步骤3.7: 检查平仓管理器初始化方法...")
            if hasattr(self.closing_manager, 'initialize'):
                await self.closing_manager.initialize(self.exchanges)
                self.logger.info("✅ 平仓管理器初始化完成")
            else:
                self.logger.info("ℹ️ 平仓管理器无需初始化（已就绪）")

            self.logger.info("🎯 步骤3.8: 统一管理器初始化完成")

        except Exception as e:
            self.logger.error(f"❌ 步骤3失败 - 统一管理器初始化失败: {e}")
            raise

    async def _initialize_order_manager(self):
        """
        🔥 步骤4: 初始化订单管理器 - ArbitrageEngine需要
        按照三个交易所统一模块清单设计
        """
        try:
            self.logger.info("🚀 步骤4.1: 开始初始化订单管理器...")

            # 导入OrderManager
            from trading.order_manager import OrderManager
            self.logger.info("🔍 步骤4.2: 导入OrderManager模块完成")

            # 创建OrderManager实例
            self.logger.info("🔍 步骤4.3: 创建OrderManager实例...")
            self.logger.info(f"🔍 现货交易器列表: {list(self.spot_traders.keys())}")
            self.logger.info(f"🔍 期货交易器列表: {list(self.futures_traders.keys())}")

            # 🔥 添加详细调试：逐个检查交易器状态
            for name, trader in self.spot_traders.items():
                self.logger.info(f"🔍 检查现货交易器 {name}: {type(trader).__name__}")

            for name, trader in self.futures_traders.items():
                self.logger.info(f"🔍 检查期货交易器 {name}: {type(trader).__name__}")

            self.logger.info("🔍 开始调用OrderManager构造函数...")
            try:
                self.order_manager = OrderManager(
                    spot_traders=self.spot_traders,
                    futures_traders=self.futures_traders
                )
                self.logger.info("✅ OrderManager实例创建成功")
            except Exception as e:
                self.logger.error(f"❌ OrderManager创建失败: {e}")
                import traceback
                self.logger.error(f"错误堆栈: {traceback.format_exc()}")
                raise

            self.logger.info("🎯 步骤4.4: 订单管理器初始化完成")

        except Exception as e:
            self.logger.error(f"❌ 步骤4失败 - 订单管理器初始化失败: {e}")
            raise

    async def _preload_trading_rules(self):
        """
        🔥 步骤5: 预加载交易规则 - 按照全流程工作流.md
        这是解决"参数准备失败"问题的关键步骤
        """
        try:
            self.logger.info("🚀 步骤5.1: 开始预加载交易规则...")

            # 检查交易规则预加载器是否已初始化
            if not self.rules_preloader:
                self.logger.error("❌ 交易规则预加载器未初始化")
                raise Exception("交易规则预加载器未初始化")

            # 🔥 关键调用：预加载所有交易规则
            self.logger.info("🔍 步骤5.2: 调用preload_all_trading_rules...")
            preload_success = await self.rules_preloader.preload_all_trading_rules(self.exchanges)

            if preload_success:
                self.logger.info("✅ 步骤5.3: 交易规则预加载成功")

                # 显示预加载统计信息
                stats = self.rules_preloader.get_stats()
                self.logger.info(f"🎯 预加载统计:")
                self.logger.info(f"   缓存规则数: {stats.get('cached_rules_count', 0)}")
                self.logger.info(f"   成功加载: {stats.get('successful_loads', 0)}")
                self.logger.info(f"   失败加载: {stats.get('failed_loads', 0)}")
                self.logger.info(f"   预加载耗时: {stats.get('preload_duration_ms', 0):.1f}ms")
            else:
                self.logger.error("❌ 步骤5.3: 交易规则预加载失败，拒绝执行")
                # 🔥 修复：严格拒绝执行，确保安全
                return False  # 预加载失败时拒绝执行，避免使用不完整的规则

            self.logger.info("🎯 步骤5.4: 交易规则预加载阶段完成")

        except Exception as e:
            self.logger.error(f"❌ 步骤5失败 - 交易规则预加载失败: {e}")
            # 🔥 重要：不抛出异常，允许系统继续运行
            # 系统可以在运行时动态加载交易规则
            self.logger.warning("⚠️ 交易规则预加载失败，系统将在运行时动态加载")

    async def implement_convergence_monitoring(self, opportunity: ArbitrageOpportunity) -> bool:
        """
        🔥 实现等待趋同监控功能 - 按照全流程工作流.md设计
        监控价差趋同至0.15%-0.2%，使用WebSocket实时数据
        
        Args:
            opportunity: 套利机会
            
        Returns:
            bool: 是否触发平仓条件
        """
        try:
            self.logger.info("=" * 80)
            self.logger.info(f"🔍 【开始价差趋同监控】 {opportunity.symbol}")
            self.logger.info("=" * 80)
            
            start_time = time.time()
            
            # 获取交易所对象
            spot_exchange_name = opportunity.buy_exchange
            futures_exchange_name = opportunity.sell_exchange
            symbol = opportunity.symbol
            
            spot_exchange = self.exchanges.get(spot_exchange_name)
            futures_exchange = self.exchanges.get(futures_exchange_name)
            
            if not spot_exchange or not futures_exchange:
                self.logger.error(f"❌ 交易所对象获取失败: {spot_exchange_name}, {futures_exchange_name}")
                return False
            
            # 导入ConvergenceMonitor
            from core.convergence_monitor import get_convergence_monitor
            convergence_monitor = get_convergence_monitor()
            
            if not convergence_monitor:
                self.logger.error(f"❌ ConvergenceMonitor获取失败")
                # 🔥 修复：ConvergenceMonitor未初始化时，尝试重新初始化
                from core.convergence_monitor import init_convergence_monitor
                
                # 获取OpportunityScanner实例
                opportunity_scanner = None
                if hasattr(self, 'exchanges') and self.exchanges:
                    # 尝试从套利引擎获取OpportunityScanner
                    try:
                        from core.arbitrage_engine import get_arbitrage_engine
                        engine = get_arbitrage_engine()
                        if engine and hasattr(engine, 'opportunity_scanner'):
                            opportunity_scanner = engine.opportunity_scanner
                            self.logger.info("✅ 从套利引擎获取OpportunityScanner")
                    except Exception as e:
                        self.logger.warning(f"⚠️ 从套利引擎获取OpportunityScanner失败: {e}")
                
                # 重新初始化ConvergenceMonitor
                config = {
                    "CLOSE_SPREAD_MIN": os.getenv("CLOSE_SPREAD_MIN", "-0.003"),  # 🔥 修复：现货溢价0.3%阈值
                    # 🔥 删除：CLOSE_SPREAD_MAX - 不再使用区间判断
                    "MAX_CONVERGENCE_WAIT": os.getenv("MAX_CONVERGENCE_WAIT", "1800")
                }
                
                convergence_monitor = init_convergence_monitor(config, self.exchanges, opportunity_scanner)
                
                if not convergence_monitor:
                    self.logger.error(f"❌ ConvergenceMonitor重新初始化失败，跳过趋同监控")
                    return False
                else:
                    self.logger.info("✅ ConvergenceMonitor重新初始化成功")
            
            # 🔥 修复：验证OpportunityScanner注入状态
            if convergence_monitor and hasattr(convergence_monitor, 'opportunity_scanner'):
                if not convergence_monitor.opportunity_scanner:
                    self.logger.warning(f"⚠️ ConvergenceMonitor的OpportunityScanner未注入，尝试注入")
                    # 尝试从套利引擎获取OpportunityScanner
                    try:
                        engine = get_arbitrage_engine()
                        if engine and hasattr(engine, 'opportunity_scanner'):
                            convergence_monitor.opportunity_scanner = engine.opportunity_scanner
                            self.logger.info("✅ OpportunityScanner注入成功")
                        else:
                            self.logger.warning(f"⚠️ 无法从套利引擎获取OpportunityScanner，跳过趋同监控")
                            return False
                    except Exception as e:
                        self.logger.warning(f"⚠️ OpportunityScanner注入失败: {e}，跳过趋同监控")
                        return False
            else:
                self.logger.warning(f"⚠️ ConvergenceMonitor未初始化，无法进行趋同监控")
                return False
            
            # 🔥 修复：从.env获取配置参数，不再硬编码（使用文件顶部的全局os导入）
            convergence_timeout = int(os.getenv("CONVERGENCE_TIMEOUT_SEC", "1800"))  # 默认30分钟
            close_spread_min = float(os.getenv("CLOSE_SPREAD_MIN", "-0.003"))  # 🔥 修复：现货溢价0.3%阈值
            min_profit_usdt = float(os.getenv("MIN_PROFIT_USDT", "5.0"))  # 默认5USDT
            max_wait_time = int(os.getenv("MAX_CONVERGENCE_WAIT", "1800"))  # 最大等待30分钟
            check_interval = float(os.getenv("CONVERGENCE_CHECK_INTERVAL", "0.5"))  # 检查间隔0.5秒，与.env保持一致

            # 🔥 修复：不再使用绝对值，直接使用配置的阈值
            # CLOSE_SPREAD_MIN=-0.001 表示当 current_spread <= -0.001 时平仓
            self.logger.info(f"🔍 价差趋同参数: 超时={convergence_timeout}秒, 平仓阈值={close_spread_min} (现货溢价≥{abs(close_spread_min)*100:.1f}%), 最小利润=${min_profit_usdt}")

            # 初始化趋同监控
            await convergence_monitor.start_monitoring(
                symbol=symbol,
                spot_exchange=spot_exchange,
                futures_exchange=futures_exchange,
                initial_spread=opportunity.spread_percent,
                target_spread=close_spread_min  # 🔥 修复：直接传递阈值，不使用绝对值
            )
            
            # 监控价差趋同
            self.logger.info(f"🔍 开始监控价差趋同: 初始价差={opportunity.spread_percent*100:.2f}%, 平仓阈值={close_spread_min*100:.1f}%")

            # 🚀 新增：详细监控开始日志
            self.logger.info(f"📊 [监控开始] {symbol} | "
                           f"开始时间={time.strftime('%H:%M:%S.%f', time.localtime(start_time))[:-3]} | "
                           f"超时时间={max_wait_time}秒 | "
                           f"检查间隔={check_interval}秒")

            # 设置超时时间
            end_time = start_time + max_wait_time

            triggered = False
            last_spread = opportunity.spread_percent
            last_log_time = start_time
            check_count = 0  # 🚀 新增：检查次数计数器
            
            # 循环监控价差
            while time.time() < end_time:
                check_count += 1  # 🚀 新增：检查次数递增
                loop_start = time.time()  # 🚀 新增：循环开始时间

                # 🔥 修复：使用ConvergenceMonitor的统一价差获取方法，避免重复计算
                current_spread = await convergence_monitor.get_current_spread(symbol)

                # 🔥 修复：不再需要复杂的价差比较逻辑
                # 直接使用ConvergenceMonitor的统一判断逻辑

                # 🔥 修复：价格获取失败检查
                if current_spread == 0:
                    self.logger.warning(f"⚠️ 价格获取失败，跳过本次检查")
                    await asyncio.sleep(check_interval)
                    continue

                # 🚨 修复：删除重复逻辑，统一使用ConvergenceMonitor.detect_convergence_signal()
                convergence_start = time.time()  # 🚀 新增：趋同检测开始时间
                is_converged = await convergence_monitor.detect_convergence_signal(symbol)
                convergence_time = (time.time() - convergence_start) * 1000  # 🚀 新增：趋同检测耗时

                # 🚀 新增：详细循环监控日志
                loop_time = (time.time() - loop_start) * 1000
                elapsed_total = time.time() - start_time

                if is_converged:
                    self.logger.info(f"✅ [趋同成功] {symbol} | "
                                   f"第{check_count}次检查 | "
                                   f"当前价差={current_spread*100:.3f}% | "
                                   f"趋同检测={convergence_time:.1f}ms | "
                                   f"循环耗时={loop_time:.1f}ms | "
                                   f"总监控时长={elapsed_total:.3f}s")
                    triggered = True
                    break
                else:
                    # 🚀 新增：更详细的等待日志（每5次检查记录一次，避免日志过多）
                    if check_count % 5 == 1 or check_count <= 3:
                        self.logger.info(f"📊 [继续监控] {symbol} | "
                                       f"第{check_count}次检查 | "
                                       f"当前价差={current_spread*100:.3f}% | "
                                       f"平仓阈值={close_spread_min*100:.1f}% | "
                                       f"循环耗时={loop_time:.1f}ms | "
                                       f"总时长={elapsed_total:.3f}s")
                
                # 定期记录日志，避免日志过多
                current_time = time.time()
                if current_time - last_log_time > 10.0:  # 每10秒记录一次详细日志
                    elapsed = current_time - start_time
                    remaining = end_time - current_time
                    self.logger.info(f"📊 价差趋同监控: 当前价差={current_spread*100:.2f}% (阈值={close_spread_min*100:.1f}%), "
                                    f"已等待{elapsed:.1f}秒, 剩余{remaining:.1f}秒")
                    last_log_time = current_time
                
                # 计算剩余时间
                remaining_time = end_time - time.time()
                if remaining_time <= 0:
                    self.logger.warning(f"⚠️ 价差趋同监控超时: {max_wait_time}秒")
                    break
                
                # 使用配置的检查间隔
                await asyncio.sleep(check_interval)
            
            # 检查最终状态
            monitoring_time = time.time() - start_time
            end_timestamp = time.strftime('%H:%M:%S.%f', time.localtime())[:-3]

            if triggered:
                self.logger.info("=" * 80)
                self.logger.info(f"✅ 【价差趋同完成】 触发平仓条件")
                self.logger.info(f"   监控耗时: {monitoring_time:.3f}秒")
                self.logger.info(f"   检查次数: {check_count}次")
                self.logger.info(f"   最终价差: {current_spread*100:.3f}%")
                self.logger.info(f"   结束时间: {end_timestamp}")
                self.logger.info(f"   平均检查间隔: {(monitoring_time/max(check_count,1)):.3f}秒/次")
                self.logger.info("=" * 80)
            else:
                self.logger.warning("=" * 80)
                self.logger.warning(f"⚠️ 【价差趋同未完成】 超时或手动中断")
                self.logger.warning(f"   监控耗时: {monitoring_time:.3f}秒")
                self.logger.warning(f"   检查次数: {check_count}次")
                self.logger.warning(f"   最终价差: {last_spread*100:.3f}%")
                self.logger.warning(f"   结束时间: {end_timestamp}")
                self.logger.warning("=" * 80)
            
            # 停止监控
            await convergence_monitor.stop_monitoring(symbol)
            
            return triggered
            
        except Exception as e:
            self.logger.error(f"❌ 价差趋同监控异常: {e}")
            return False

    async def execute_arbitrage(self, opportunity: ArbitrageOpportunity) -> ExecutionResult:
        """
        🔥 执行套利交易 - 核心方法
        按照全流程工作流.md的极速差价锁定设计

        Args:
            opportunity: 套利机会

        Returns:
            ExecutionResult: 执行结果
        """
        async with self.execution_lock:
            try:
                self.logger.info("=" * 80)
                self.logger.info(f"🚀 【套利执行开始】 {opportunity.symbol}")
                self.logger.info(f"   现货交易所: {opportunity.buy_exchange}")
                self.logger.info(f"   期货交易所: {opportunity.sell_exchange}")
                self.logger.info(f"   基础数量: {opportunity.base_amount}")
                self.logger.info(f"   价差百分比: {opportunity.spread_percent*100:.2f}%")
                self.logger.info("=" * 80)

                # 🔥 记录执行开始到统一日志系统
                self.logger.info(f"📊 EXECUTION_START | {opportunity.symbol} | "
                               f"{opportunity.buy_exchange} -> {opportunity.sell_exchange} | "
                               f"差价={opportunity.spread_percent*100:.2f}%")

                start_time = time.time()

                # 更新状态
                self.logger.info("🔍 执行步骤A.1: 更新执行状态...")
                self.current_status = ExecutionStatus.EXECUTING
                self.logger.info("✅ 执行状态已更新为EXECUTING")

                # 创建执行结果
                self.logger.info("🔍 执行步骤A.2: 创建执行结果对象...")
                self.current_execution = ExecutionResult(
                    opportunity=opportunity,
                    start_time=start_time,
                    success=False,
                    # 🔥 修复：确保ArbitrageEngine期望的属性始终存在
                    spot_orders=[],
                    futures_orders=[],
                    spot_filled=0.0,
                    futures_filled=0.0
                )
                self.logger.info("✅ 执行结果对象创建完成")

                # 🔥 关键步骤1: 98%对冲质量预检查
                self.logger.info("🔍 执行步骤B.1: 开始98%对冲质量预检查...")
                
                # 🔥 新增：记录6大缓存系统使用统计
                try:
                    preloader = get_trading_rules_preloader()
                    cache_stats = preloader.get_cache_stats() if hasattr(preloader, 'get_cache_stats') else {}
                    
                    self.logger.info("📊 6大缓存系统使用统计:")
                    self.logger.info(f"   - 余额缓存: 实时更新 (ArbitrageEngine)")
                    self.logger.info(f"   - 保证金缓存: 5分钟TTL (MarginCalculator)")
                    self.logger.info(f"   - 交易规则缓存: {cache_stats.get('rules_count', 'N/A')}个规则 (TradingRulesPreloader)")
                    self.logger.info(f"   - 精度缓存: 24小时TTL (TradingRulesPreloader)")
                    self.logger.info(f"   - 订单簿缓存: 实时更新 (WebSocket)")
                    self.logger.info(f"   - 对冲质量缓存: 10秒TTL (预检查)")
                    
                    # 🔥 修复：正确计算缓存命中率
                    hit_rate = cache_stats.get('cache_hit_rate', 0) * 100
                    total_entries = cache_stats.get('total_cache_entries', 0)
                    self.logger.info(f"📈 缓存命中率: {hit_rate:.1f}% (总条目: {total_entries})")
                except Exception as e:
                    self.logger.warning(f"⚠️ 获取缓存统计失败: {e}")
                    self.logger.info("📊 6大缓存系统: 统计获取失败，但系统正常运行")
                
                hedge_quality = await self._pre_check_hedge_quality(opportunity)
                if not hedge_quality:
                    self.logger.warning(f"❌ 执行步骤B.1失败: 对冲质量不足98%，跳过套利: {opportunity.symbol}")
                    self.current_execution.success = False
                    self.current_execution.error_message = "对冲质量不足98%"
                    self.current_status = ExecutionStatus.FAILED
                    
                    # 🔥 修复：发送对冲质量不足通知
                    await send_trading_error(
                        exchange=f"{opportunity.buy_exchange}+{opportunity.sell_exchange}",
                        symbol=opportunity.symbol,
                        error="对冲质量不足98%，跳过套利机会",
                        amount=f"{opportunity.base_amount:.6f}"
                    )
                    return self.current_execution
                self.logger.info("✅ 执行步骤B.1完成: 对冲质量检查通过")

                # 🔥 关键步骤2: 极速并行执行 (<30ms目标)
                self.logger.info("🔍 执行步骤C.1: 开始极速并行执行...")
                parallel_start = time.time()
                success = await self._execute_parallel_trading(opportunity)
                parallel_time = (time.time() - parallel_start) * 1000
                self.logger.info(f"🎯 执行步骤C.1完成: 并行执行耗时 {parallel_time:.2f}ms")

                # 检查执行是否成功
                if not success:
                    self.logger.error(f"❌ 执行步骤C.1失败: 并行执行失败，结束套利: {opportunity.symbol}")
                    self.current_execution.success = False
                    self.current_execution.error_message = "并行执行失败"
                    self.current_status = ExecutionStatus.FAILED

                    # 🔥 新增：失败后自动重置状态，避免卡住
                    await asyncio.sleep(1)  # 短暂延迟确保日志记录完成
                    await self._reset_execution_state()

                    return self.current_execution

                # 🔥 关键修复: 添加步骤D - 等待趋同监控
                self.logger.info("🔍 执行步骤D.1: 开始等待趋同监控...")
                convergence_success = await self.implement_convergence_monitoring(opportunity)
                if not convergence_success:
                    self.logger.error(f"❌ 执行步骤D.1失败: 趋同监控未触发目标价差，拒绝执行平仓: {opportunity.symbol}")
                    # 🔥 修复：趋同监控失败时严格拒绝执行平仓，确保安全
                    self.current_status = ExecutionStatus.FAILED
                    if self.current_execution:
                        self.current_execution.success = False
                        self.current_execution.error_message = "趋同监控未达到目标价差"
                    return self.current_execution or ExecutionResult(
                        success=False,
                        error_message="趋同监控未达到目标价差",
                        spot_orders=[],
                        futures_orders=[],
                        spot_filled=0.0,
                        futures_filled=0.0
                    )
                else:
                    self.logger.info("✅ 执行步骤D.1完成: 趋同监控达到目标价差")

                # 🔥 关键步骤3: 执行平仓处理
                self.logger.info("🔍 执行步骤E.1: 开始执行平仓处理...")
                closing_start = time.time()
                closing_success = await self.close_positions(opportunity)
                closing_time = (time.time() - closing_start) * 1000
                
                if closing_success:
                    self.logger.info(f"✅ 执行步骤E.1完成: 平仓处理耗时 {closing_time:.2f}ms")
                else:
                    self.logger.warning(f"⚠️ 执行步骤E.1部分完成: 平仓处理可能存在问题，耗时 {closing_time:.2f}ms")

                # 更新执行结果
                self.logger.info("🔍 执行步骤F.1: 更新执行结果...")
                self.current_execution.success = success and closing_success
                self.current_execution.end_time = time.time()
                self.current_execution.execution_time = self.current_execution.end_time - start_time

                # 更新性能统计
                self.logger.info("🔍 执行步骤F.2: 更新性能统计...")
                self._update_execution_stats(self.current_execution.execution_time, success)

                total_time_ms = self.current_execution.execution_time * 1000
                if success and closing_success:
                    self.logger.info("=" * 80)
                    self.logger.info(f"✅ 【套利执行成功】 {opportunity.symbol}")
                    self.logger.info(f"   总耗时: {total_time_ms:.2f}ms")
                    self.logger.info(f"   并行执行耗时: {parallel_time:.2f}ms")
                    self.logger.info(f"   平仓耗时: {closing_time:.2f}ms")
                    self.logger.info(f"   是否触发趋同: {convergence_success}")
                    self.logger.info("=" * 80)
                    self.current_status = ExecutionStatus.COMPLETED

                    # 🔥 记录执行结果到统一日志系统
                    spot_result_str = f"成功 {self.current_execution.spot_result.executed_quantity:.6f}@${self.current_execution.spot_result.executed_price:.6f}" if self.current_execution.spot_result else "失败"
                    futures_result_str = f"成功 {self.current_execution.futures_result.executed_quantity:.6f}@${self.current_execution.futures_result.executed_price:.6f}" if self.current_execution.futures_result else "失败"
                    self.logger.info(f"📊 EXECUTION_RESULT | {opportunity.symbol} | "
                                   f"现货: {spot_result_str} | 期货: {futures_result_str}")

                    # 🔥 删除错误的套利成功通知逻辑 - 这不是正确的通知时机
                else:
                    self.logger.error("=" * 80)
                    self.logger.error(f"❌ 【套利执行部分失败】 {opportunity.symbol}")
                    self.logger.error(f"   开仓成功: {'✅' if success else '❌'}")
                    self.logger.error(f"   平仓成功: {'✅' if closing_success else '❌'}")
                    self.logger.error(f"   总耗时: {total_time_ms:.2f}ms")
                    self.logger.error("=" * 80)
                    self.current_status = ExecutionStatus.FAILED

                    # 🔥 记录执行失败到统一日志系统
                    spot_result_str = "失败" if not success else f"成功 {self.current_execution.spot_result.executed_quantity:.6f}@${self.current_execution.spot_result.executed_price:.6f}" if self.current_execution.spot_result else "失败"
                    futures_result_str = "失败" if not success else f"成功 {self.current_execution.futures_result.executed_quantity:.6f}@${self.current_execution.futures_result.executed_price:.6f}" if self.current_execution.futures_result else "失败"
                    self.logger.error(f"📊 EXECUTION_FAILED | {opportunity.symbol} | "
                                    f"现货: {spot_result_str} | 期货: {futures_result_str}")

                return self.current_execution

            except Exception as e:
                # 🔥 修复：使用统一的SystemMonitor进行错误分析和恢复策略
                self.logger.error(f"❌ 套利执行异常: {e}")

                try:
                    from core.system_monitor import get_system_monitor
                    system_monitor = get_system_monitor()

                    # 委托给SystemMonitor进行错误分析
                    recovery_strategy = await system_monitor.analyze_error_and_get_recovery_strategy(
                        arbitrage_engine=None,  # ExecutionEngine独立错误处理
                        execution_engine=self
                    )

                    self.logger.info(f"🔧 ExecutionEngine错误恢复策略: {recovery_strategy.get('description', '默认策略')}")

                except Exception as monitor_error:
                    self.logger.warning(f"⚠️ SystemMonitor错误分析失败: {monitor_error}")

                self.current_status = ExecutionStatus.FAILED
                if self.current_execution:
                    self.current_execution.success = False
                    self.current_execution.error_message = str(e)
                return self.current_execution or ExecutionResult(
                    success=False,
                    error_message=str(e),
                    # 🔥 修复：确保兜底ExecutionResult也有必要的属性
                    spot_orders=[],
                    futures_orders=[],
                    spot_filled=0.0,
                    futures_filled=0.0
                )

    async def _pre_check_hedge_quality(self, opportunity: ArbitrageOpportunity) -> bool:
        """
        🔥 统一对冲质量预检查 - 完全基于TradingRulesPreloader统一系统
        删除所有重复计算，严格按照MD文档要求：统一使用TradingRulesPreloader
        """
        try:
            # 🔥 获取参数
            spot_exchange = opportunity.buy_exchange
            futures_exchange = opportunity.sell_exchange
            symbol = opportunity.symbol
            
            # 计算基础数量
            spot_price = opportunity.exchange1_price if opportunity.exchange1_market == 'spot' else opportunity.exchange2_price
            futures_price = opportunity.exchange1_price if opportunity.exchange1_market == 'futures' else opportunity.exchange2_price
            
            min_order_usd = float(os.getenv("MIN_ORDER_AMOUNT_USD", "50.0"))
            base_amount = min_order_usd / spot_price
            
            # 🔥 修复：ExecutionEngine不做精度处理，直接使用原始数量
            # 精度处理由各交易所的TradingRulesPreloader统一处理
            rules_preloader = get_trading_rules_preloader()
            spot_amount = base_amount
            futures_amount = base_amount

            # 🔥 统一对冲质量检查 - 唯一权威入口
            hedge_quality_data = rules_preloader.get_hedge_quality_cached(
                spot_exchange, futures_exchange, symbol,
                spot_amount, futures_amount, spot_price, futures_price
            )
            
            if not hedge_quality_data:
                self.logger.error(f"❌ 统一对冲质量检查失败")
                return False
            
            hedge_ratio = hedge_quality_data.get("hedge_ratio", 0.0)
            hedge_quality_ok = hedge_quality_data.get("is_good_hedge", False)
            
            # 🔥 统一日志格式
            self.logger.info(f"🎯 统一对冲质量检查: 比例={hedge_ratio:.4f} ({'✅通过' if hedge_quality_ok else '❌拒绝'})")

            if self.current_execution:
                self.current_execution.hedge_ratio = hedge_ratio

            return hedge_quality_ok

        except Exception as e:
            self.logger.error(f"❌ 统一对冲质量检查异常: {e}")
            return False

    async def _revalidate_opportunity_before_execution(self, opportunity: ArbitrageOpportunity, execution_context: str = "opening") -> tuple[bool, float]:
        """
        🔥 修复：执行前重新验证套利机会 - 支持开仓和平仓不同验证逻辑
        获取最新价格数据，重新计算价差，确保机会仍然有效

        Args:
            opportunity: 套利机会对象
            execution_context: 执行上下文 ("opening" 开仓 | "closing" 平仓)

        Returns:
            tuple[bool, float]: (是否有效, 当前价差)
        """
        try:
            # 🔥 **关键修复**：平仓时不依赖OpportunityScanner，开仓时才需要
            scanner = None
            if execution_context == "opening":
                from core.opportunity_scanner import get_opportunity_scanner
                scanner = get_opportunity_scanner()
                if not scanner:
                    self.logger.error("❌ 开仓时无法获取OpportunityScanner实例，验证失败")
                    return False, 0.0
            else:
                # 平仓时不需要OpportunityScanner，直接使用实时数据验证
                self.logger.info("📊 平仓验证：跳过OpportunityScanner依赖，直接验证价差方向")

            # 🔥 **关键BUG修复**：使用数据快照验证器，确保数据一致性
            # 解决3毫秒价格反转问题的核心修复
            from core.data_snapshot_validator import DataSnapshotValidator
            validator = DataSnapshotValidator()

            # 🔥 **核心修复**：根据执行上下文决定是否使用快照数据
            spot_data = None
            futures_data = None
            use_snapshot = False

            # 1. 根据执行上下文决定数据源策略
            if execution_context == "opening":
                # 开仓时：检查并使用快照确保数据一致性
                if hasattr(opportunity, 'data_snapshot') and opportunity.data_snapshot:
                    validation_result = validator.validate_market_data_snapshot(
                        opportunity.data_snapshot, execution_context
                    )

                    if validation_result.is_valid:
                        self.logger.info(f"✅ 开仓验证：数据快照验证通过，使用快照数据确保一致性")
                        use_snapshot = True
                        snapshot = opportunity.data_snapshot
                        spot_data = snapshot['spot_data']
                        futures_data = snapshot['futures_data']
                        self.logger.debug(f"✅ 快照详情: {validation_result.validation_details}")
                    else:
                        # 🔥 **智能降级处理**：快照验证失败时的处理策略
                        self.logger.warning(f"⚠️ 开仓验证：数据快照验证失败: {validation_result.validation_details}")
                        self.logger.warning(f"⚠️ 最大数据年龄: {validation_result.max_age_ms:.1f}ms")
                        
                        # 🔥 **降级策略**：如果快照年龄在合理范围内（<30秒），尝试使用实时数据
                        if validation_result.max_age_ms < 30000:  # 30秒内
                            self.logger.info(f"🔄 快照过期但在合理范围内，降级使用实时数据进行验证")
                            use_snapshot = False  # 不使用快照，获取实时数据
                        else:
                            self.logger.error(f"❌ 快照年龄过长({validation_result.max_age_ms:.1f}ms > 30000ms)，拒绝执行")
                            return False, 0.0
                else:
                    self.logger.info(f"📊 开仓验证：没有数据快照，将获取最新实时数据")
            else:
                # 平仓时：直接使用实时数据，不进行快照验证
                self.logger.info(f"📊 平仓验证：直接使用实时数据，确保市场状况准确性")

            # 2. 获取实时数据（开仓时从scanner，平仓时直接验证价差方向）
            if not spot_data or not futures_data:
                if execution_context == "opening" and scanner:
                    # 开仓时需要获取实时数据
                    spot_key = f"{opportunity.buy_exchange}_spot_{opportunity.symbol}"
                    futures_key = f"{opportunity.sell_exchange}_futures_{opportunity.symbol}"

                    spot_data = scanner.market_data.get(spot_key)
                    futures_data = scanner.market_data.get(futures_key)

                    if not spot_data or not futures_data:
                        self.logger.error(f"❌ 开仓时无法获取最新市场数据: spot={bool(spot_data)}, futures={bool(futures_data)}")
                        return False, 0.0
                elif execution_context == "closing":
                    # 平仓时获取实时WebSocket数据，确保价格精准性
                    self.logger.info("📊 平仓验证：获取实时WebSocket数据，确保价格精准性")
                    
                    # 获取实时现货和期货订单簿数据
                    spot_orderbook = self._get_websocket_orderbook(opportunity.buy_exchange, opportunity.symbol, "spot")
                    futures_orderbook = self._get_websocket_orderbook(opportunity.sell_exchange, opportunity.symbol, "futures")
                    
                    if not spot_orderbook or not futures_orderbook:
                        self.logger.error(f"❌ 平仓时无法获取实时订单簿数据: spot={bool(spot_orderbook)}, futures={bool(futures_orderbook)}")
                        return False, 0.0
                    
                    # 🔥 关键修复：平仓验证也应该使用opening上下文，确保与ConvergenceMonitor一致
                    from core.unified_order_spread_calculator import get_order_spread_calculator
                    calculator = get_order_spread_calculator()
                    
                    order_result = calculator.calculate_order_based_spread(
                        spot_orderbook, futures_orderbook, 100.0, "opening"  # 统一使用opening上下文
                    )
                    
                    if order_result is None:
                        self.logger.error("❌ 平仓时实时差价计算失败")
                        return False, 0.0
                    
                    current_spread = order_result.executable_spread
                    
                    # 验证是否为现货溢价（负值）
                    if current_spread >= 0:
                        self.logger.warning(f"⚠️ 平仓验证失败: 当前实时差价{current_spread*100:.3f}% >= 0 (非现货溢价，无法平仓)")
                        return False, current_spread
                    else:
                        self.logger.info(f"✅ 平仓验证通过: 当前实时现货溢价{current_spread*100:.3f}%，适合平仓")
                        return True, current_spread
                else:
                    self.logger.error(f"❌ 验证失败：缺少必要的市场数据，拒绝执行以防止风险")
                    return False, 0.0

            # 3. 🔥 **统一数据处理逻辑**：根据是否使用快照决定处理方式
            if use_snapshot and hasattr(opportunity, 'data_snapshot') and opportunity.data_snapshot:
                # 使用快照数据，跳过新鲜度检查
                self.logger.debug("✅ 使用数据快照，跳过新鲜度检查")
                snapshot = opportunity.data_snapshot
                spot_orderbook = snapshot.get('spot_orderbook', {})
                futures_orderbook = snapshot.get('futures_orderbook', {})

                # 如果快照中有计算结果，直接使用
                if 'calculation_result' in snapshot and snapshot['calculation_result']:
                    order_result = snapshot['calculation_result']
                    current_spread = order_result.executable_spread
                    self.logger.debug(f"✅ 使用快照中的差价计算结果: {current_spread*100:.3f}%")
                else:
                    # 🔥 修复：直接使用统一Order差价计算器，避免调用已删除的包装方法
                    from core.unified_order_spread_calculator import get_order_spread_calculator
                    calculator = get_order_spread_calculator()
                    order_result = calculator.calculate_order_based_spread(
                        spot_orderbook, futures_orderbook, 100.0, execution_context
                    )
                    if order_result is None:
                        self.logger.warning("⚠️ 快照数据Order差价计算失败")
                        return False, 0.0
                    current_spread = order_result.executable_spread
            else:
                # 🔥 **核心修复**：平仓时使用实时数据，开仓时验证新鲜度
                if execution_context == "closing":
                    self.logger.info("🔥 平仓验证：使用实时数据进行差价计算")
                else:
                    # 开仓时验证数据新鲜度
                    current_time = time.time() * 1000
                    spot_age = current_time - (spot_data.timestamp * 1000 if spot_data.timestamp < 1e12 else spot_data.timestamp)
                    futures_age = current_time - (futures_data.timestamp * 1000 if futures_data.timestamp < 1e12 else futures_data.timestamp)

                    max_age = 500  # 500ms内的数据才有效
                    if spot_age > max_age or futures_age > max_age:
                        self.logger.warning(f"⚠️ 价格数据过期: spot_age={spot_age:.1f}ms, futures_age={futures_age:.1f}ms > {max_age}ms")
                        return False, 0.0

                # 5. 🔥 使用统一Order差价计算
                spot_orderbook = spot_data.orderbook if hasattr(spot_data, 'orderbook') and spot_data.orderbook else {}
                futures_orderbook = futures_data.orderbook if hasattr(futures_data, 'orderbook') and futures_data.orderbook else {}

                # 🔥 **核心修复**：直接使用统一Order差价计算器，避免重复调用
                from core.unified_order_spread_calculator import get_order_spread_calculator
                from websocket.unified_timestamp_processor import sync_all_exchanges

                # 🔥 修复：使用正确的时间同步方法，确保数据一致性
                try:
                    await sync_all_exchanges(force=True)
                except Exception as e:
                    self.logger.debug(f"时间同步异常: {e}")  # 不影响主流程

                calculator = get_order_spread_calculator()
                # 🔥 关键修复：统一使用opening上下文进行价差计算，确保一致性
                order_result = calculator.calculate_order_based_spread(
                    spot_orderbook, futures_orderbook, 100.0, "opening"  # 统一使用opening上下文
                )

                if order_result is None:
                    self.logger.warning(f"⚠️ Order差价计算失败，无法验证机会")
                    return False, 0.0

                current_spread = order_result.executable_spread
                context_desc = "平仓" if execution_context == "closing" else "开仓"
                self.logger.debug(f"✅ {context_desc}验证差价计算(实时数据): {current_spread*100:.6f}%")

            # 6. 🔥 修复：根据执行上下文验证价差方向和幅度
            if execution_context == "opening":
                # 开仓阶段：只接受期货溢价 (current_spread > 0)
                if current_spread <= 0:
                    self.logger.warning(f"⚠️ 开仓验证失败: 当前{current_spread*100:.3f}% <= 0 (非期货溢价，无法开仓)")
                    return False, current_spread
            elif execution_context == "closing":
                # 平仓阶段：只接受现货溢价 (current_spread < 0)
                if current_spread >= 0:
                    self.logger.warning(f"⚠️ 平仓验证失败: 当前{current_spread*100:.3f}% >= 0 (非现货溢价，无法平仓)")
                    return False, current_spread
            else:
                self.logger.warning(f"⚠️ 未知执行上下文: {execution_context}")
                return False, current_spread

            # 7. 🔥 修复：根据执行上下文验证价差阈值
            if execution_context == "opening":
                # 开仓阶段：验证期货溢价是否达到开仓阈值
                # 🔥 修复：使用环境变量中的阈值，避免依赖scanner实例
                min_spread = float(os.getenv("MIN_SPREAD", "0.001"))  # 默认0.1%
                if current_spread < min_spread:
                    self.logger.warning(f"⚠️ 开仓价差不足: 当前{current_spread*100:.3f}% < 开仓阈值{min_spread*100:.3f}%")
                    return False, current_spread
            elif execution_context == "closing":
                # 平仓阶段：验证现货溢价是否达到平仓阈值
                # 使用环境变量中的平仓阈值
                close_spread_min = float(os.getenv("CLOSE_SPREAD_MIN", "-0.003"))  # 默认-0.3%
                if current_spread > close_spread_min:  # 注意：现货溢价时current_spread为负值
                    self.logger.warning(f"⚠️ 平仓价差不足: 当前{current_spread*100:.3f}% > 平仓阈值{close_spread_min*100:.3f}%")
                    return False, current_spread

            # 8. 🔥 修复：根据执行上下文验证价差变化幅度
            if execution_context == "opening":
                # 开仓时检查价差变化（应该变化不大）
                spread_change = abs(current_spread - opportunity.spread_percent)
                max_spread_change = 0.001  # 允许0.1%的价差变化

                if spread_change > max_spread_change:
                    self.logger.warning(
                        f"⚠️ 开仓价差变化过大: 原始{opportunity.spread_percent*100:.3f}% → "
                        f"当前{current_spread*100:.3f}% (变化{spread_change*100:.3f}% > {max_spread_change*100:.1f}%)"
                    )
                    return False, current_spread
            else:
                # 平仓时不检查价差变化（因为趋同监控期间价差变化是预期的）
                spread_change = abs(current_spread - opportunity.spread_percent)
                self.logger.info(
                    f"📊 平仓价差变化: 开仓时{opportunity.spread_percent*100:.3f}% → "
                    f"平仓时{current_spread*100:.3f}% (变化{spread_change*100:.3f}%)"
                )

            # 🔥 修复：记录验证成功的上下文信息
            context_desc = "开仓" if execution_context == "opening" else "平仓"
            self.logger.debug(f"✅ {context_desc}验证通过: 当前价差{current_spread*100:.3f}%")
            return True, current_spread

        except Exception as e:
            self.logger.error(f"❌ 执行前重新验证异常: {e}")
            return False, 0.0  # 🔥 修复：异常时拒绝执行，确保安全

    async def _execute_parallel_trading(self, opportunity: ArbitrageOpportunity) -> bool:
        """
        🔥 极速并行执行交易 - 核心方法 (<30ms优化版)
        按照全流程工作流.md的<30ms并行锁定设计
        🔥 零延迟参数准备 + 真正并行执行
        """
        try:
            execution_start_time = time.time()
            self.logger.debug(f"🔥 开始<30ms极速并行执行: 现货({opportunity.buy_exchange}) + 期货({opportunity.sell_exchange}) {opportunity.symbol}")

            # 🔥 性能优化：并行执行验证和参数准备，减少总延迟
            # 🔥 修复：传递正确的执行上下文 - 这是开仓阶段
            revalidation_task = asyncio.create_task(self._revalidate_opportunity_before_execution(opportunity, "opening"))

            # 🔥 零延迟参数准备（与验证并行进行）
            prep_start = time.time()
            
            # 🔥 优化1: 预缓存配置参数，避免重复读取.env
            if not hasattr(self, '_cached_config'):
                self._cached_config = {
                    'min_order_usd': float(os.getenv("MIN_ORDER_AMOUNT_USD", "50.0")),
                    'max_order_usd': float(os.getenv("MAX_ORDER_AMOUNT_USD", "110.0")),
                    'perfect_hedge_tolerance': float(os.getenv("PERFECT_HEDGE_TOLERANCE", "0.001")),
                    'order_depth_safety_factor': float(os.getenv("ORDER_DEPTH_SAFETY_FACTOR", "0.9")),
                    'max_depth_levels': int(os.getenv("MAX_DEPTH_LEVELS", "30")),  # 🔥 升级：改为30档深度，与UnifiedOrderSpreadCalculator保持一致
                    'hedge_quality_threshold': float(os.getenv("HEDGE_QUALITY_THRESHOLD", "0.98"))
                }

            config = self._cached_config
            spot_exchange = opportunity.buy_exchange
            futures_exchange = opportunity.sell_exchange
            symbol = opportunity.symbol

            # 🔥 优化2: 直接获取价格，减少条件判断
            spot_price = opportunity.exchange1_price if opportunity.exchange1_market == 'spot' else opportunity.exchange2_price
            futures_price = opportunity.exchange1_price if opportunity.exchange1_market == 'futures' else opportunity.exchange2_price

            # 🔥 优化3: 使用6大缓存系统，零API调用
            rules_preloader = get_trading_rules_preloader()

            # 🔥 优化4: 快速数量计算 - 🚨 关键修复：确保与.env配置一致
            # 🚨 修复现货数量不匹配问题：直接使用.env配置的金额
            min_order_usd = config['min_order_usd']  # 35.0 USD from .env
            
            # 🔥 关键修复：直接使用固定金额，确保与.env完全一致
            base_amount = min_order_usd / spot_price  # 35USD / spot_price = 正确的代币数量
            max_base_amount = config['max_order_usd'] / spot_price
            if base_amount > max_base_amount:
                base_amount = max_base_amount
                
            self.logger.info(f"🔧 数量计算修复: {min_order_usd}USD ÷ {spot_price:.6f} = {base_amount:.6f} 代币")

            # 🔥 优化5: 精度处理零延迟（使用缓存）- 修复完美对冲逻辑
            # 🚨 关键修复：确保现货和期货数量完全一致，防止对冲失败
            
            # 🔥 修复：ExecutionEngine不做精度处理，直接使用原始数量
            # 精度处理由各交易所的TradingRulesPreloader统一处理
            futures_amount_raw = base_amount
            spot_amount_raw = base_amount

            # 🔥 关键修复：在智能协调前检查Bybit最小订单价值并调整数量
            if futures_exchange == "bybit":
                min_value_required = 5.0  # Bybit最小订单值5USDT
                current_order_value = futures_amount_raw * futures_price

                if current_order_value < min_value_required:
                    # 计算满足最小订单价值的数量
                    required_amount = min_value_required / futures_price

                    # 使用TradingRulesPreloader进行精度处理
                    try:
                        adjusted_amount_str = rules_preloader.format_amount_unified(
                            required_amount, "bybit", symbol, "futures"
                        )
                        adjusted_amount = float(adjusted_amount_str)

                        # 验证调整后的订单价值
                        adjusted_value = adjusted_amount * futures_price
                        if adjusted_value >= min_value_required:
                            self.logger.warning(f"⚠️ Bybit期货数量自动调整: {futures_amount_raw:.6f} -> {adjusted_amount:.6f}")
                            self.logger.warning(f"   订单价值调整: {current_order_value:.2f}USDT -> {adjusted_value:.2f}USDT")

                            # 同时调整现货和期货数量以保持对冲
                            futures_amount_raw = adjusted_amount
                            spot_amount_raw = adjusted_amount

                            self.logger.info(f"✅ Bybit最小订单价值调整完成，进入智能协调")
                        else:
                            self.logger.error(f"❌ Bybit期货订单价值调整失败: {adjusted_value:.2f}USDT < {min_value_required}USDT")
                            return False
                    except Exception as e:
                        self.logger.error(f"❌ Bybit期货订单价值调整异常: {e}")
                        return False

            # 🔥 新增：ExecutionEngine智能协调 - 解决OKX期货0.001与Gate/Bybit期货0.01步长差异
            spot_amount, futures_amount = await self._intelligent_amount_coordination(
                spot_amount_raw, futures_amount_raw, spot_exchange, futures_exchange, symbol, futures_price, rules_preloader
            )
            
            if spot_amount is None or futures_amount is None:
                self.logger.error("❌ ExecutionEngine智能协调失败")
                return False
                
            # 🔥 统一架构：删除重复精度转换！
            # 精度转换由各交易所的format_amount_with_contract_conversion统一处理
            # ExecutionEngine只负责数量协调，不做精度转换

            self.logger.info(f"✅ 统一架构：数量协调完成，精度转换由各交易所统一处理")
            self.logger.info(f"   协调后数量: 现货={spot_amount:.8f}, 期货={futures_amount:.8f}")
            self.logger.info(f"   对冲质量检查已在预检查阶段完成，符合MD文件统一计算要求")

            prep_time = (time.time() - prep_start) * 1000
            self.logger.debug(f"⚡ 零延迟参数准备: {prep_time:.2f}ms")

            # 🔥 优化6: 并行获取深度数据和对冲质量检查
            parallel_prep_start = time.time()
            
            spot_exchange_client = self.exchanges.get(spot_exchange)
            futures_exchange_client = self.exchanges.get(futures_exchange)
            
            if not spot_exchange_client or not futures_exchange_client:
                self.logger.error(f"❌ 交易所实例获取失败")
                return False

            # 🔥 并行获取订单簿数据 - 使用WebSocket零缓存延迟
            spot_orderbook_task = asyncio.create_task(
                self._get_websocket_orderbook_async(spot_exchange, symbol, "spot")
            )
            futures_orderbook_task = asyncio.create_task(
                self._get_websocket_orderbook_async(futures_exchange, symbol, "futures")
            )
            
            spot_orderbook, futures_orderbook = await asyncio.gather(
                spot_orderbook_task,
                futures_orderbook_task,
                return_exceptions=True
            )
            
            # 🔥 精准修复：统一处理部分数据，避免过度严格验证
            if not spot_orderbook or not futures_orderbook:
                self.logger.warning(f"⚠️ WebSocket订单簿数据获取失败: spot={bool(spot_orderbook)}, futures={bool(futures_orderbook)}")
                return False

            # 🔥 关键修复：检查数据质量，允许部分数据继续处理
            spot_incomplete = spot_orderbook.get('incomplete', False)
            futures_incomplete = futures_orderbook.get('incomplete', False)

            if spot_incomplete or futures_incomplete:
                self.logger.warning(f"⚠️ 订单簿数据不完整但继续验证: spot_incomplete={spot_incomplete}, futures_incomplete={futures_incomplete}")
                self.logger.warning(f"   现货数据质量: {spot_orderbook.get('data_quality', 'unknown')}")
                self.logger.warning(f"   期货数据质量: {futures_orderbook.get('data_quality', 'unknown')}")

                # 🔥 检查是否有足够的数据进行基本验证
                spot_has_data = bool(spot_orderbook.get('asks')) or bool(spot_orderbook.get('bids'))
                futures_has_data = bool(futures_orderbook.get('asks')) or bool(futures_orderbook.get('bids'))

                if not spot_has_data or not futures_has_data:
                    self.logger.error(f"❌ 订单簿数据完全不可用: spot_has_data={spot_has_data}, futures_has_data={futures_has_data}")
                    return False
            
            # 🔥 使用统一验证器验证订单簿数据同步性 - 增加重试机制
            from websocket.orderbook_validator import validate_orderbook_synchronization

            # 🔥 使用统一网络配置管理器
            from config.network_config import get_network_config_manager
            network_config = get_network_config_manager()
            retry_config = network_config.get_retry_config()

            max_retries = retry_config['max_retries']  # 统一配置
            base_time_diff_ms = 200
            retry_delay = retry_config['retry_delay']  # 统一配置

            is_sync = False
            sync_error = ""

            for retry_attempt in range(max_retries):
                # 🔥 动态调整时间阈值：第一次200ms，第二次300ms，第三次400ms
                current_time_diff_ms = base_time_diff_ms + (retry_attempt * 100)

                is_sync, sync_error = validate_orderbook_synchronization(
                    spot_orderbook, futures_orderbook,
                    max_time_diff_ms=current_time_diff_ms,
                    adaptive_threshold=True  # 🔥 启用自适应阈值
                )

                if is_sync:
                    if retry_attempt > 0:
                        self.logger.info(f"✅ 订单簿同步验证成功 (重试{retry_attempt+1}次，阈值{current_time_diff_ms}ms)")
                    break
                else:
                    if retry_attempt < max_retries - 1:
                        self.logger.debug(f"🔄 订单簿同步验证失败，重试{retry_attempt+1}/{max_retries}: {sync_error}")
                        await asyncio.sleep(retry_delay)

                        # 🔥 重新获取订单簿数据
                        spot_orderbook_retry = await self._get_websocket_orderbook_async(spot_exchange, symbol, "spot")
                        futures_orderbook_retry = await self._get_websocket_orderbook_async(futures_exchange, symbol, "futures")

                        if spot_orderbook_retry and futures_orderbook_retry:
                            spot_orderbook = spot_orderbook_retry
                            futures_orderbook = futures_orderbook_retry
                        else:
                            self.logger.warning(f"⚠️ 重试获取订单簿数据失败")
                            break
                    else:
                        self.logger.warning(f"⚠️ 订单簿同步验证最终失败: {sync_error}")

            if not is_sync:
                self.logger.warning(f"⚠️ 经过{max_retries}次重试后仍然失败: {sync_error}")
                return False

            # 🔥 计算时间差和数据年龄用于日志记录
            current_time = time.time() * 1000
            spot_timestamp = spot_orderbook.get('timestamp', current_time)
            futures_timestamp = futures_orderbook.get('timestamp', current_time)

            # 确保时间戳格式一致（毫秒）
            if spot_timestamp < 1e12:
                spot_timestamp *= 1000
            if futures_timestamp < 1e12:
                futures_timestamp *= 1000

            time_diff_ms = abs(spot_timestamp - futures_timestamp)
            data_age_spot = current_time - spot_timestamp
            data_age_futures = current_time - futures_timestamp

            # 🔥 提取WebSocket订单簿数据
            spot_depth = spot_orderbook
            futures_depth = futures_orderbook

            parallel_prep_time = (time.time() - parallel_prep_start) * 1000
            self.logger.info(f"✅ 使用WebSocket实时订单簿数据，时间差：{time_diff_ms:.1f}ms，数据年龄：spot={data_age_spot:.1f}ms, futures={data_age_futures:.1f}ms")
            self.logger.debug(f"⚡ WebSocket深度获取: {parallel_prep_time:.2f}ms")

            # 🔥 统一验证流程（零重复）
            validation_start = time.time()
            
            # 🔥 精准修复：统一异常处理和数据验证
            if isinstance(spot_depth, Exception) or isinstance(futures_depth, Exception):
                self.logger.error(f"❌ 深度数据获取异常: spot={type(spot_depth)}, futures={type(futures_depth)}")
                return False

            if not spot_depth or not futures_depth:
                self.logger.error(f"❌ 深度数据为空: spot={bool(spot_depth)}, futures={bool(futures_depth)}")
                return False

            # 🔥 修复：统一错误检查逻辑
            if spot_depth.get('error') or futures_depth.get('error'):
                self.logger.error(f"❌ 深度数据API错误: spot={spot_depth.get('error')}, futures={futures_depth.get('error')}")
                return False

            # 🔥 按照MD文件要求：分析前10档订单簿数据（不要求最少深度）
            spot_asks = spot_depth.get('asks', [])
            spot_bids = spot_depth.get('bids', [])
            futures_asks = futures_depth.get('asks', [])
            futures_bids = futures_depth.get('bids', [])

            spot_asks_depth = len(spot_asks)
            spot_bids_depth = len(spot_bids)
            futures_asks_depth = len(futures_asks)
            futures_bids_depth = len(futures_bids)

            # 🔥 常驻正常订单簿数据日志（按MD文件要求）
            self.logger.info(f"📊 [ORDERBOOK] 分析套利深度数据: {symbol}")
            self.logger.info(f"   现货深度: asks={spot_asks_depth}档, bids={spot_bids_depth}档")
            self.logger.info(f"   期货深度: asks={futures_asks_depth}档, bids={futures_bids_depth}档")

            # 🔥 根据套利方向检查必要的数据存在性（不要求深度）
            if opportunity.buy_market == "spot":
                # 买现货卖期货：需要现货asks和期货bids
                required_data_available = spot_asks_depth > 0 and futures_bids_depth > 0
                direction_info = f"买现货卖期货: 现货asks={spot_asks_depth}, 期货bids={futures_bids_depth}"
            else:
                # 卖现货买期货：需要现货bids和期货asks
                required_data_available = spot_bids_depth > 0 and futures_asks_depth > 0
                direction_info = f"卖现货买期货: 现货bids={spot_bids_depth}, 期货asks={futures_asks_depth}"

            if not required_data_available:
                self.logger.error(f"❌ 套利方向所需数据缺失: {symbol}")
                self.logger.error(f"   {direction_info}")
                return False

            # 🔥 有数据就继续分析，记录正常日志
            self.logger.info(f"✅ [ORDERBOOK] 套利数据可用: {symbol}")
            self.logger.info(f"   {direction_info}")
                
            if futures_depth.get('error'):
                self.logger.error(f"❌ 期货深度API错误: {futures_depth['error']}")
                return False
            
            # 🔥 完美修复：删除重复的空数据检查，统一深度分析器已处理

            # 🔥 升级：使用30档累积表+二分查找算法进行深度分析
            from core.unified_depth_analyzer import get_depth_analyzer
            from core.unified_order_spread_calculator import get_order_spread_calculator

            depth_analyzer = get_depth_analyzer()
            order_calculator = get_order_spread_calculator()

            # 🔥 新增：使用Order差价计算器进行精确分析
            target_amount_usd = spot_amount * spot_depth.get('asks', [[0, 0]])[0][0] if spot_depth.get('asks') else 0

            order_spread_result = order_calculator.calculate_order_based_spread(
                spot_depth, futures_depth, target_amount_usd, "opening"
            )

            # 🔥 性能优化：检查并行验证结果 - 🚨 关键修复：先验证价差方向，再检查滑点
            revalidation_start = time.time()
            is_valid, current_spread = await revalidation_task
            revalidation_time = (time.time() - revalidation_start) * 1000

            if not is_valid:
                self.logger.warning(f"⚠️ 执行前验证失败，取消执行: {opportunity.symbol} 当前价差{current_spread*100:.3f}% (验证耗时{revalidation_time:.1f}ms)")
                return False

            self.logger.debug(f"✅ 执行前验证通过: {opportunity.symbol} 当前价差{current_spread*100:.3f}% (验证耗时{revalidation_time:.1f}ms)")

            # 🔥 如果Order差价计算成功，使用新算法结果进行滑点检查
            if order_spread_result is not None:
                self.logger.info(f"✅ 30档Order差价分析: 可执行价差{order_spread_result.executable_spread*100:.3f}%, "
                               f"滑点{order_spread_result.total_slippage*100:.3f}%, "
                               f"使用档位: 现货{order_spread_result.spot_levels_used}, 期货{order_spread_result.futures_levels_used}")

                # 检查滑点是否在阈值内
                if order_spread_result.total_slippage <= 0.001:  # 0.1%滑点阈值
                    # 🔥 修复：滑点检查通过后，继续执行真正的交易，不直接返回
                    pass  # 继续执行后续的交易逻辑
                else:
                    self.logger.warning(f"⚠️ 滑点过大: {order_spread_result.total_slippage*100:.3f}% > 0.1%")
                    return False

            # 🔥 深度分析：验证订单簿深度充足性
            spot_result, futures_result = depth_analyzer.analyze_dual_orderbook_depth(
                spot_depth, futures_depth, spot_amount, futures_amount,
                spot_depth.get('asks', [[0, 0]])[0][0] if spot_depth.get('asks') else 0,
                futures_depth.get('bids', [[0, 0]])[0][0] if futures_depth.get('bids') else 0
            )

            # 🔥 修复：严格深度验证，任何一个市场深度不足都跳过，遵循MD文档01设计
            if not spot_result.is_sufficient or not futures_result.is_sufficient:
                error_details = []
                if not spot_result.is_sufficient:
                    error_details.append(f"现货:{spot_result.error_message}")
                if not futures_result.is_sufficient:
                    error_details.append(f"期货:{futures_result.error_message}")

                error_message = f"深度不足，跳过套利 - {', '.join(error_details)}"
                self.logger.warning(f"⚠️ {error_message}")

                # 发送深度不足通知
                await send_trading_error(
                    exchange=f"{spot_exchange}+{futures_exchange}",
                    symbol=symbol,
                    error=error_message,
                    amount=f"{spot_amount:.6f}"
                )
                return False

            validation_time = (time.time() - validation_start) * 1000
            self.logger.debug(f"⚡ 快速验证: {validation_time:.2f}ms")

            # 🔥 修复：删除重复的验证检查，验证已在上面完成

            # 🔥 核心: 真正并行差价锁定 (<30ms目标)
            trading_start = time.time()
            
            self.logger.info(f"🚀 启动并行差价锁定: 现货={spot_amount:.8f}, 期货={futures_amount:.8f} (精度转换由各交易所统一处理)")

            # 🔥 使用asyncio.gather()实现真正并行执行
            spot_result, futures_result = await asyncio.gather(
                self._execute_spot_order(opportunity, spot_amount),
                self._execute_futures_order(opportunity, futures_amount),
                return_exceptions=True
            )
            
            trading_time = (time.time() - trading_start) * 1000
            total_time = (time.time() - execution_start_time) * 1000

            # 🔥 性能监控和目标检查
            self.logger.info(f"⚡ 并行交易执行: {trading_time:.2f}ms")
            self.logger.info(f"🎯 总执行时间: {total_time:.2f}ms (目标: <30ms)")
            
            if total_time < 30.0:
                performance_score = ((30.0 - total_time) / 30.0) * 100
                self.logger.info(f"✅ 达到<30ms目标! 性能得分: {performance_score:.1f}%")
            else:
                self.logger.warning(f"⚠️ 超过30ms目标: {total_time:.2f}ms")

            # 🔥 优化9: 快速结果检查
            spot_success = not isinstance(spot_result, Exception) and spot_result and spot_result.success
            futures_success = not isinstance(futures_result, Exception) and futures_result and futures_result.success

            # 🔥 执行后结果记录（删除重复的对冲质量计算）
            if spot_success and futures_success:
                actual_spot_qty = spot_result.executed_quantity
                actual_futures_qty = futures_result.executed_quantity
                
                # 🔥 删除冗余的对冲质量计算：智能协调阶段已完成正确的对冲质量检查
                # 此处仅记录执行结果，不再重复计算对冲质量
                self.logger.info(f"📊 执行结果: 现货{actual_spot_qty:.6f} vs 期货{actual_futures_qty:.6f}")
                
                # 🔥 删除冗余的差异警告：智能协调已确保98%对冲质量

            # 🔥 异常处理和紧急平仓（保持原有逻辑）
            if isinstance(spot_result, Exception):
                self.logger.error(f"❌ 现货执行异常: {spot_result}")
                # 🔥 修复：正确导入send_trading_error函数
                await send_trading_error(opportunity.buy_exchange, symbol, f"现货执行异常: {spot_result}", f"{spot_amount:.6f}")

            if isinstance(futures_result, Exception):
                self.logger.error(f"❌ 期货执行异常: {futures_result}")
                # 🔥 修复：正确导入send_trading_error函数
                await send_trading_error(opportunity.sell_exchange, symbol, f"期货执行异常: {futures_result}", f"{futures_amount:.6f}")

            # 🔥 紧急平仓机制
            if spot_success and not futures_success:
                self.logger.error("🚨 现货成功但期货失败，执行紧急平仓现货仓位")
                # 🔥 修复：正确导入send_trading_error函数
                await send_trading_error(opportunity.sell_exchange, symbol, "期货开仓失败，现货已成功，执行紧急平仓", f"{futures_amount:.6f}")
                await self._emergency_close_spot_position(opportunity, spot_result)
            elif not spot_success and futures_success:
                self.logger.error("🚨 期货成功但现货失败，执行紧急平仓期货仓位")
                # 🔥 修复：正确导入send_trading_error函数
                await send_trading_error(opportunity.buy_exchange, symbol, "现货开仓失败，期货已成功，执行紧急平仓", f"{spot_amount:.6f}")
                await self._emergency_close_futures_position(opportunity, futures_result)

            # 🔥 修复：保存结果并正确设置success标志
            success = spot_success and futures_success
            if self.current_execution:
                self.current_execution.spot_result = spot_result if not isinstance(spot_result, Exception) else None
                self.current_execution.futures_result = futures_result if not isinstance(futures_result, Exception) else None
                # 🔥 修复：从预检查阶段获取hedge_ratio，避免重复计算
                # hedge_ratio已在_pre_check_hedge_quality()中计算并保存
                self.current_execution.hedge_ratio = getattr(self.current_execution, 'hedge_ratio', 0.0)
                # 🔥 关键修复：设置总体执行成功标志
                self.current_execution.success = success

                # 🔥 修复：填充ArbitrageEngine期望的属性
                if spot_result and not isinstance(spot_result, Exception):
                    self.current_execution.spot_orders = [{
                        'order_id': spot_result.order_id,
                        'status': 'filled' if spot_result.success else 'failed',
                        'filled': spot_result.executed_quantity,
                        'average_price': spot_result.executed_price,
                        'price': spot_result.executed_price,
                        'amount': spot_result.executed_quantity
                    }] if spot_result.order_id else []
                    self.current_execution.spot_filled = spot_result.executed_quantity
                else:
                    self.current_execution.spot_orders = []
                    self.current_execution.spot_filled = 0.0

                if futures_result and not isinstance(futures_result, Exception):
                    self.current_execution.futures_orders = [{
                        'order_id': futures_result.order_id,
                        'status': 'filled' if futures_result.success else 'failed',
                        'filled': futures_result.executed_quantity,
                        'average_price': futures_result.executed_price,
                        'price': futures_result.executed_price,
                        'amount': futures_result.executed_quantity
                    }] if futures_result.order_id else []
                    self.current_execution.futures_filled = futures_result.executed_quantity
                else:
                    self.current_execution.futures_orders = []
                    self.current_execution.futures_filled = 0.0

            self.logger.info(f"🔥 并行执行结果: {'✅成功' if success else '❌失败'} (总耗时: {total_time:.2f}ms)")
            
            return success

        except Exception as e:
            total_time = (time.time() - execution_start_time) * 1000 if 'execution_start_time' in locals() else 0
            self.logger.error(f"❌ 并行执行异常: {e} (耗时: {total_time:.2f}ms)")
            return False

    # 🔥 按照全流程工作流.md：余额检查已移至第2阶段ArbitrageEngine零延迟验证
    # 此处不再需要重复的余额检查方法

    async def _intelligent_amount_coordination(self, spot_amount_raw: float, futures_amount_raw: float,
                                             spot_exchange: str, futures_exchange: str, symbol: str,
                                             futures_price: float, rules_preloader) -> Tuple[Optional[float], Optional[float]]:
        """
        🔥 正确的智能协调：统一返回币数量，删除重复转换
        所有交易所都返回币数量，在下单时各自进行最终转换
        """
        try:
            self.logger.info(f"🔥 智能协调开始: 原始数量={spot_amount_raw:.8f}币")
            
            # 🔥 步骤1：获取各交易所精度规则（使用缓存）
            spot_rule = rules_preloader.get_trading_rule(spot_exchange, symbol, "spot")
            futures_rule = rules_preloader.get_trading_rule(futures_exchange, symbol, "futures")
            
            if not spot_rule or not futures_rule:
                self.logger.error(f"❌ 无法获取交易规则: {spot_exchange}/{futures_exchange} {symbol}")
                return None, None
            
            # 🔥 步骤2：计算各交易所最终可交易数量（统一使用币数量）
            from decimal import Decimal
            
            # 现货：使用步长截取
            spot_decimal = Decimal(str(spot_amount_raw))
            spot_final = float(spot_rule.truncate_quantity(spot_decimal))
            
            # 🔥 关键修复：期货也统一返回币数量，不提前转换
            if futures_exchange.lower() == "gate":
                # Gate期货：必须整数币数量，但需要检查最小值
                futures_final = round(futures_amount_raw)
                self.logger.info(f"🔥 Gate期货整数截取: {futures_amount_raw:.6f} → {futures_final}币")

                # 🚨 通用系统修复：检查Gate期货最小数量要求
                if futures_final < futures_rule.min_qty:
                    self.logger.error(f"❌ Gate期货数量不足最小要求: {futures_final} < {futures_rule.min_qty}")
                    self.logger.error(f"   建议：增加交易金额至少${float(futures_rule.min_qty) * futures_price:.2f}USD")
                    return None, None
            else:
                # Bybit/OKX期货：使用步长截取币数量
                futures_decimal = Decimal(str(futures_amount_raw))
                futures_final = float(futures_rule.truncate_quantity(futures_decimal))
                self.logger.info(f"🔥 {futures_exchange}期货步长截取: {futures_amount_raw:.6f} → {futures_final}币")
            
            # 🔥 步骤3：完整的数量有效性和对冲质量检查（通用系统修复）
            # 🚨 关键修复：首先检查数量有效性，防止任何一方数量为0
            if spot_final <= 0 or futures_final <= 0:
                self.logger.error(f"❌ 智能协调失败：数量无效")
                self.logger.error(f"   现货数量: {spot_final:.6f}币 {'✅' if spot_final > 0 else '❌'}")
                self.logger.error(f"   期货数量: {futures_final:.6f}币 {'✅' if futures_final > 0 else '❌'}")
                self.logger.error(f"   这通常是由于交易金额过小，不满足交易所最小要求")
                return None, None

            # 🔥 步骤4：对冲质量检查（只有在数量都有效时才检查）
            hedge_quality = min(spot_final, futures_final) / max(spot_final, futures_final)

            self.logger.info(f"🎯 对冲质量检查: {spot_final:.6f}币 vs {futures_final:.6f}币 = {hedge_quality*100:.1f}%")

            # 🔥 使用缓存配置中的对冲质量阈值，避免硬编码
            hedge_threshold = self._cached_config['hedge_quality_threshold']
            if hedge_quality < hedge_threshold:
                self.logger.warning(f"⚠️ 对冲质量不足{hedge_threshold*100:.1f}%: {hedge_quality*100:.1f}%，跳过此次套利")
                return None, None

            # 🔥 关键：记录最终协调结果
            self.logger.info(f"✅ 智能协调完成: 现货={spot_final:.6f}币, 期货={futures_final:.6f}币")
            self.logger.info(f"✅ 对冲质量: {hedge_quality*100:.1f}% (≥98%)")
            self.logger.info(f"🔥 统一单位：返回币数量，各交易所在下单时自行转换")

            # 🔥 返回最终可交易数量：统一使用币数量
            return spot_final, futures_final
            
        except Exception as e:
            self.logger.error(f"❌ 智能协调异常: {e}")
            import traceback
            self.logger.error(f"❌ 异常堆栈: {traceback.format_exc()}")
            return None, None

    async def _emergency_close_spot_position(self, opportunity: ArbitrageOpportunity, spot_result: OpeningResult):
        """🚨 紧急平仓现货仓位"""
        try:
            self.logger.error(f"🚨 开始紧急平仓现货仓位: {opportunity.symbol}")

            spot_exchange = self.exchanges.get(opportunity.buy_exchange)
            if not spot_exchange:
                self.logger.error(f"❌ 无法获取现货交易所: {opportunity.buy_exchange}")
                return

            # 🔥 修复：获取现货深度数据，确保紧急平仓有orderbook数据
            orderbook = None
            try:
                # 1. 首先尝试从执行上下文获取预取的深度数据
                if hasattr(self, 'current_execution') and self.current_execution:
                    orderbook = getattr(self.current_execution, 'spot_depth', None)
                    if orderbook:
                        self.logger.info(f"✅ 紧急平仓使用预取的现货深度数据: {opportunity.symbol}")
                
                # 2. 如果没有预取数据，从WebSocket获取实时数据
                if not orderbook:
                    self.logger.info(f"🔍 紧急平仓获取WebSocket现货深度数据: {opportunity.symbol}")
                    # 🔥 修复：使用WebSocket实时数据，删除REST API调用
                    try:
                        orderbook = self._get_websocket_orderbook(opportunity.buy_exchange, opportunity.symbol, "spot")
                    except Exception as e:
                        self.logger.warning(f"⚠️ 获取WebSocket深度数据失败: {e}")
                        orderbook = None
                    
                    if orderbook and orderbook.get('asks') and orderbook.get('bids'):
                        self.logger.info(f"✅ 紧急平仓成功获取WebSocket深度数据: asks={len(orderbook.get('asks', []))}, bids={len(orderbook.get('bids', []))}")
                    else:
                        self.logger.warning(f"⚠️ 紧急平仓WebSocket深度数据无效，使用基础平仓")
                        orderbook = None
                        
            except Exception as e:
                self.logger.warning(f"⚠️ 紧急平仓获取深度数据失败: {e}，使用基础平仓")
                orderbook = None

            # 🔥 修复：使用SpotTrader直接执行紧急平仓，确保orderbook数据传递
            spot_trader = self.spot_traders.get(opportunity.buy_exchange)
            if spot_trader and spot_result and spot_result.executed_quantity:
                
                # 使用SpotTrader的market_sell方法进行紧急平仓
                close_result = await spot_trader.market_sell(
                    symbol=opportunity.symbol,
                    amount=spot_result.executed_quantity,
                    orderbook=orderbook,  # 🔥 关键：传递深度数据
                    disable_split=True  # 紧急平仓不拆单
                )
                
                if close_result and close_result.get('status') in ['filled', 'open', 'new', 'success']:
                    self.logger.info(f"✅ 紧急平仓现货成功: {close_result.get('id', 'unknown')}")
                else:
                    self.logger.error(f"❌ 紧急平仓现货失败: {close_result}")
            else:
                # 兜底方案：使用统一平仓管理器
                close_result = await self.closing_manager.close_position_unified(
                    symbol=opportunity.symbol,
                    exchange=spot_exchange,
                    market_type="spot",
                    side="sell"
                )
                
                if close_result.success:
                    self.logger.info(f"✅ 紧急平仓现货成功（兜底）: {close_result.order_id}")
                else:
                    self.logger.error(f"❌ 紧急平仓现货失败（兜底）: {close_result.error_message}")

        except Exception as e:
            self.logger.error(f"❌ 紧急平仓现货异常: {e}")

    async def _emergency_close_futures_position(self, opportunity: ArbitrageOpportunity, futures_result: OpeningResult):
        """🚨 紧急平仓期货仓位"""
        try:
            self.logger.error(f"🚨 开始紧急平仓期货仓位: {opportunity.symbol}")

            futures_exchange = self.exchanges.get(opportunity.sell_exchange)
            if not futures_exchange:
                self.logger.error(f"❌ 无法获取期货交易所: {opportunity.sell_exchange}")
                return

            # 使用统一平仓管理器紧急平仓
            close_result = await self.closing_manager.close_position_unified(
                symbol=opportunity.symbol,
                exchange=futures_exchange,
                market_type="futures"
                # 期货平仓不需要指定side，由平仓管理器自动判断
            )

            if close_result.success:
                self.logger.info(f"✅ 紧急平仓期货成功: {close_result.order_id}")
            else:
                self.logger.error(f"❌ 紧急平仓期货失败: {close_result.error_message}")

        except Exception as e:
            self.logger.error(f"❌ 紧急平仓期货异常: {e}")

    async def _adjust_spot_excess(self, opportunity: ArbitrageOpportunity, excess_amount: float):
        """🔧 调整现货超额数量"""
        try:
            self.logger.info(f"🔧 开始调整现货超额: {opportunity.symbol} {excess_amount:.6f}")
            
            spot_exchange = self.exchanges.get(opportunity.buy_exchange)
            if not spot_exchange:
                self.logger.error(f"❌ 无法获取现货交易所: {opportunity.buy_exchange}")
                return
            
            # 使用SpotTrader卖出超额现货
            spot_trader = self.spot_traders.get(opportunity.buy_exchange)
            if spot_trader:
                # 🔥 修复：使用统一WebSocket订单簿获取方法
                orderbook = self._get_websocket_orderbook(opportunity.buy_exchange, opportunity.symbol, "spot")
                
                result = await spot_trader.market_sell(
                    symbol=opportunity.symbol,
                    amount=excess_amount,
                    orderbook=orderbook,
                    disable_split=True
                )
                
                if result and result.get('status') in ['filled', 'open', 'new', 'success']:
                    self.logger.info(f"✅ 现货超额调整成功: {excess_amount:.6f}")
                else:
                    self.logger.error(f"❌ 现货超额调整失败: {result}")
            else:
                self.logger.error(f"❌ 无法获取现货交易器: {opportunity.buy_exchange}")
                
        except Exception as e:
            self.logger.error(f"❌ 调整现货超额异常: {e}")

    async def _adjust_futures_excess(self, opportunity: ArbitrageOpportunity, excess_amount: float):
        """🔧 调整期货超额数量"""
        try:
            self.logger.info(f"🔧 开始调整期货超额: {opportunity.symbol} {excess_amount:.6f}")
            
            futures_exchange = self.exchanges.get(opportunity.sell_exchange)
            if not futures_exchange:
                self.logger.error(f"❌ 无法获取期货交易所: {opportunity.sell_exchange}")
                return
            
            # 使用FuturesTrader平仓超额期货
            futures_trader = self.futures_traders.get(opportunity.sell_exchange)
            if futures_trader:
                result = await futures_trader.close_position(
                    symbol=opportunity.symbol,
                    amount=excess_amount,
                    close_type="market"
                )
                
                if result and result.get('status') in ['filled', 'open', 'new', 'success']:
                    self.logger.info(f"✅ 期货超额调整成功: {excess_amount:.6f}")
                else:
                    self.logger.error(f"❌ 期货超额调整失败: {result}")
            else:
                self.logger.error(f"❌ 无法获取期货交易器: {opportunity.sell_exchange}")
                
        except Exception as e:
            self.logger.error(f"❌ 调整期货超额异常: {e}")

    async def _execute_spot_order(self, opportunity: ArbitrageOpportunity, amount: float) -> Optional[OpeningResult]:
        """
        🔥 执行现货订单 - 使用统一开仓管理器
        按照三个交易所统一模块清单设计
        """
        try:
            # 确定现货交易所和操作
            spot_exchange = opportunity.buy_exchange
            symbol = opportunity.symbol

            self.logger.info(f"🔍 现货执行: {spot_exchange} {symbol} {amount}")

            # 获取交易所实例
            exchange = self.exchanges.get(spot_exchange)
            if not exchange:
                self.logger.error(f"❌ 现货交易所不存在: {spot_exchange}")
                return None

            # 🔥 修复：确保获取有效的orderbook数据
            orderbook = None

            # 1. 首先尝试从执行上下文获取预取的深度数据
            if hasattr(self, 'current_execution') and self.current_execution:
                orderbook = getattr(self.current_execution, 'spot_depth', None)
                if orderbook:
                    self.logger.info(f"✅ 使用预取的现货深度数据: {symbol}")

            # 2. 如果没有预取数据，从WebSocket获取实时数据
            if not orderbook:
                try:
                    self.logger.info(f"🔍 使用WebSocket获取现货深度数据: {symbol}")
                    # 🔥 修复：使用统一WebSocket订单簿获取方法
                    orderbook = self._get_websocket_orderbook(spot_exchange, symbol, "spot")

                    # 🔥 按照MD文件要求：分析前10档订单簿数据
                    if not orderbook:
                        self.logger.error(f"❌ 获取的现货深度数据为空: {symbol}")
                        return OpeningResult(
                            success=False,
                            error_message=f"现货深度数据为空: {symbol}"
                        )

                    # 🔥 分析现有数据，不要求最少深度
                    asks = orderbook.get('asks', [])
                    bids = orderbook.get('bids', [])
                    asks_depth = orderbook.get('asks_depth', len(asks))
                    bids_depth = orderbook.get('bids_depth', len(bids))

                    # 🔥 常驻正常订单簿数据日志
                    self.logger.info(f"📊 [ORDERBOOK] 现货开仓分析: {symbol}")
                    self.logger.info(f"   现货深度: asks={asks_depth}档, bids={bids_depth}档")

                    if not asks and not bids:
                        self.logger.error(f"❌ 现货深度数据完全为空: {symbol}")
                        return OpeningResult(
                            success=False,
                            error_message=f"现货深度数据完全为空: {symbol}"
                        )

                    # 🔥 根据套利方向检查必要数据存在性（不要求深度）
                    if opportunity.buy_market == "spot" and not asks:
                        self.logger.error(f"❌ 买现货需要asks数据但为空: {symbol}")
                        return OpeningResult(
                            success=False,
                            error_message=f"买现货需要asks数据但为空: {symbol}"
                        )

                    if opportunity.sell_market == "spot" and not bids:
                        self.logger.error(f"❌ 卖现货需要bids数据但为空: {symbol}")
                        return OpeningResult(
                            success=False,
                            error_message=f"卖现货需要bids数据但为空: {symbol}"
                        )

                    # 🔥 有数据就继续处理，记录正常日志
                    self.logger.info(f"✅ [ORDERBOOK] 现货数据可用: {symbol} - asks={asks_depth}, bids={bids_depth}")

                    self.logger.info(f"✅ 成功获取现货深度数据: asks={len(orderbook.get('asks', []))}, bids={len(orderbook.get('bids', []))}")

                except Exception as e:
                    self.logger.error(f"❌ 获取现货深度数据失败: {e}")
                    return OpeningResult(
                        success=False,
                        error_message=f"获取现货深度数据失败: {str(e)}"
                    )

            # 3. 最终验证：确保orderbook不为None且包含有效数据
            if not orderbook or not isinstance(orderbook, dict):
                self.logger.error(f"❌ 现货深度数据为空或格式错误: {orderbook}")
                return OpeningResult(
                    success=False,
                    error_message="现货深度数据为空或格式错误"
                )
            
            # 🔥 新增：数据新鲜度检查
            if orderbook.get('timestamp'):
                current_time = time.time() * 1000
                data_age = current_time - orderbook.get('timestamp', current_time)
                max_data_age = 2000  # 2秒内的数据才算新鲜
                if data_age > max_data_age:
                    self.logger.warning(f"⚠️ 现货开仓数据过期: {data_age:.1f}ms > {max_data_age}ms")
                else:
                    self.logger.debug(f"✅ 现货开仓数据新鲜: {data_age:.1f}ms")

            # 🔥 使用智能协调的最终数量进行现货买入
            result = await self.opening_manager.unified_market_buy(
                symbol=symbol,
                quantity=amount,  # amount已经是智能协调的最终数量
                exchange=exchange,
                market_type="spot",
                orderbook=orderbook  # 🔥 修复：确保传递有效的深度数据
            )

            if result and result.success:
                self.logger.info(f"✅ 现货执行成功: {result}")
                return result
            else:
                self.logger.error(f"❌ 现货执行失败: {result}")
                # 🔥 修复：发送电报通知
                error_msg = result.error_message if result else "现货执行失败，无结果返回"
                await send_trading_error(
                    exchange=spot_exchange,
                    symbol=symbol,
                    error=error_msg,
                    amount=f"{amount:.6f}"
                )
                return result

        except Exception as e:
            self.logger.error(f"❌ 现货执行异常: {e}")
            return OpeningResult(
                success=False,
                error_message=f"现货执行异常: {str(e)}"
            )

    async def _execute_futures_order(self, opportunity: ArbitrageOpportunity, amount: float) -> Optional[OpeningResult]:
        """
        🔥 执行期货订单 - 使用统一开仓管理器
        按照三个交易所统一模块清单设计
        """
        try:
            # 确定期货交易所和操作
            futures_exchange = opportunity.sell_exchange
            symbol = opportunity.symbol

            self.logger.info(f"🔍 期货执行: {futures_exchange} {symbol} {amount}")

            # 获取交易所实例
            exchange = self.exchanges.get(futures_exchange)
            if not exchange:
                self.logger.error(f"❌ 期货交易所不存在: {futures_exchange}")
                return None

                         # 🔥 杠杆设置：使用【系统核心知识库】第5项 unified_leverage_manager 统一杠杆管理器
            try:
                # 🔥 正确使用：使用统一杠杆管理器
                from core.unified_leverage_manager import set_leverage_unified
                
                self.logger.info(f"🔧 设置{futures_exchange}杠杆: {symbol}")
                
                # 🔥 修复：统一使用3倍杠杆
                leverage_result = await set_leverage_unified(exchange, symbol, leverage=3)
                
                if leverage_result and leverage_result.get("success"):
                    self.logger.info(f"✅ 杠杆设置成功: {futures_exchange} {symbol} = 3x")
                else:
                    error_msg = leverage_result.get("message", "未知错误") if leverage_result else "无返回结果"
                    self.logger.warning(f"⚠️ 杠杆设置失败，使用默认杠杆继续执行: {symbol} - {error_msg}")
                    
            except Exception as leverage_error:
                self.logger.warning(f"⚠️ 杠杆设置异常，使用默认杠杆继续执行: {leverage_error}")

            # 🔥 修复：确保获取有效的orderbook数据
            orderbook = None

            # 1. 首先尝试从执行上下文获取预取的深度数据
            if hasattr(self, 'current_execution') and self.current_execution:
                orderbook = getattr(self.current_execution, 'futures_depth', None)
                if orderbook:
                    self.logger.info(f"✅ 使用预取的期货深度数据: {symbol}")

            # 2. 如果没有预取数据，从WebSocket获取实时数据
            if not orderbook:
                try:
                    self.logger.info(f"🔍 使用WebSocket获取期货深度数据: {symbol}")
                    # 🔥 修复：使用统一WebSocket订单簿获取方法
                    orderbook = self._get_websocket_orderbook(futures_exchange, symbol, "futures")

                    # 🔥 按照MD文件要求：分析前10档期货订单簿数据（与现货保持一致）
                    if not orderbook:
                        self.logger.error(f"❌ 获取的期货深度数据为空: {symbol}")
                        return OpeningResult(
                            success=False,
                            error_message=f"期货深度数据为空: {symbol}"
                        )

                    # 🔥 分析现有数据，不要求最少深度
                    asks = orderbook.get('asks', [])
                    bids = orderbook.get('bids', [])
                    asks_depth = orderbook.get('asks_depth', len(asks))
                    bids_depth = orderbook.get('bids_depth', len(bids))

                    # 🔥 常驻正常订单簿数据日志
                    self.logger.info(f"📊 [ORDERBOOK] 期货开仓分析: {symbol}")
                    self.logger.info(f"   期货深度: asks={asks_depth}档, bids={bids_depth}档")

                    if not asks and not bids:
                        self.logger.error(f"❌ 期货深度数据完全为空: {symbol}")
                        return OpeningResult(
                            success=False,
                            error_message=f"期货深度数据完全为空: {symbol}"
                        )

                    # 🔥 根据套利方向检查必要数据存在性（不要求深度）
                    if opportunity.buy_market == "futures" and not asks:
                        self.logger.error(f"❌ 买期货需要asks数据但为空: {symbol}")
                        return OpeningResult(
                            success=False,
                            error_message=f"买期货需要asks数据但为空: {symbol}"
                        )

                    if opportunity.sell_market == "futures" and not bids:
                        self.logger.error(f"❌ 卖期货需要bids数据但为空: {symbol}")
                        return OpeningResult(
                            success=False,
                            error_message=f"卖期货需要bids数据但为空: {symbol}"
                        )

                    # 🔥 有数据就继续处理，记录正常日志
                    self.logger.info(f"✅ [ORDERBOOK] 期货数据可用: {symbol} - asks={asks_depth}, bids={bids_depth}")

                    self.logger.info(f"✅ 成功获取期货深度数据: asks={len(orderbook.get('asks', []))}, bids={len(orderbook.get('bids', []))}")

                except Exception as e:
                    self.logger.error(f"❌ 获取期货深度数据失败: {e}")
                    return OpeningResult(
                        success=False,
                        error_message=f"获取期货深度数据失败: {str(e)}"
                    )

            # 3. 最终验证：确保orderbook不为None且包含有效数据
            if not orderbook or not isinstance(orderbook, dict):
                self.logger.error(f"❌ 期货深度数据为空或格式错误: {orderbook}")
                return OpeningResult(
                    success=False,
                    error_message="期货深度数据为空或格式错误"
                )

            # 🔥 使用智能协调的最终数量进行期货卖出
            result = await self.opening_manager.unified_market_sell(
                symbol=symbol,
                quantity=amount,  # amount已经是智能协调的最终数量
                exchange=exchange,
                market_type="futures",
                orderbook=orderbook  # 🔥 修复：确保传递有效的深度数据
            )
            
            # 🔥 删除所有重复验证：期货执行结果直接返回，不再重复验证
            if result and result.success:
                self.logger.info(f"✅ 期货执行成功: {symbol}")
                return result
            else:
                self.logger.error(f"❌ 期货执行失败: {result}")
                # 🔥 修复：发送电报通知
                error_msg = result.error_message if result else "期货执行失败，无结果返回"
                await send_trading_error(
                    exchange=futures_exchange,
                    symbol=symbol,
                    error=error_msg,
                    amount=f"{amount:.6f}"
                )
                return result

        except Exception as e:
            self.logger.error(f"❌ 期货执行异常: {e}")
            return OpeningResult(
                success=False,
                error_message=f"期货执行异常: {str(e)}"
            )

    async def close_positions(self, opportunity: ArbitrageOpportunity) -> bool:
        """
        🔥 平仓所有仓位 - 使用统一平仓管理器
        按照全流程工作流.md的极速平仓处理(35ms)设计
        🔥 关键修复：确保平仓时使用实时WebSocket数据，不依赖历史spread数据
        """
        try:
            self.logger.info("=" * 80)
            self.logger.info(f"🔥 【开始平仓】 {opportunity.symbol}")
            self.logger.info(f"   现货交易所: {opportunity.buy_exchange}")
            self.logger.info(f"   期货交易所: {opportunity.sell_exchange}")
            self.logger.info("=" * 80)

            close_start_time = time.time()

            # 🔥 关键修复：平仓前获取实时WebSocket数据，不使用历史数据
            self.logger.info("🔍 平仓前验证: 获取实时WebSocket数据进行精准验证...")
            
            # 🔥 获取实时现货和期货订单簿数据
            spot_orderbook = self._get_websocket_orderbook(opportunity.buy_exchange, opportunity.symbol, "spot")
            futures_orderbook = self._get_websocket_orderbook(opportunity.sell_exchange, opportunity.symbol, "futures")
            
            if not spot_orderbook or not futures_orderbook:
                self.logger.error(f"❌ 平仓时无法获取实时订单簿数据: spot={bool(spot_orderbook)}, futures={bool(futures_orderbook)}")
                return False
            
            # 🔥 关键修复：平仓验证应该使用opening上下文，与ConvergenceMonitor保持一致
            # 根据07号文档设计原理：趋同监控与机会扫描使用相同的价差计算方法
            # 平仓验证也应该使用相同的上下文，确保判断一致性
            from core.unified_order_spread_calculator import get_order_spread_calculator
            calculator = get_order_spread_calculator()
            
            order_result = calculator.calculate_order_based_spread(
                spot_orderbook, futures_orderbook, 100.0, "opening"  # 🔥 修复：使用opening上下文
            )
            
            if order_result is None:
                self.logger.error("❌ 平仓时实时差价计算失败")
                return False
            
            current_spread = order_result.executable_spread
            
            # 🔥 验证是否为现货溢价（负值）- 适合平仓
            close_spread_min = float(os.getenv("CLOSE_SPREAD_MIN", "-0.003"))  # 默认-0.3%
            
            if current_spread >= 0:
                self.logger.error(f"❌ 平仓验证失败: 当前实时差价{current_spread*100:.3f}% >= 0 (非现货溢价，无法平仓)")
                return False
            
            if current_spread > close_spread_min:
                self.logger.error(f"❌ 平仓验证失败: 当前实时差价{current_spread*100:.3f}% > 平仓阈值{close_spread_min*100:.3f}%")
                return False
            
            # 🔥 记录实时数据与历史数据的对比
            historical_spread = opportunity.spread_percent
            spread_change = current_spread - historical_spread
            
            # 🔥 复用现有统一模块：使用DataSnapshotValidator进行数据新鲜度检查
            from core.data_snapshot_validator import DataSnapshotValidator
            from config.network_config import get_network_config_manager

            # 🔥 使用统一网络配置管理器获取重试配置
            network_config = get_network_config_manager()
            retry_config = network_config.get_retry_config()
            max_retries = retry_config['max_retries']  # 统一配置：3次
            retry_delay = retry_config['retry_delay']  # 统一配置：50ms

            # 🔥 创建数据快照进行验证
            snapshot_validator = DataSnapshotValidator()

            # 🔥 智能重试机制：保持1000ms严格阈值，通过重试提高成功率
            validation_success = False
            last_validation_error = ""

            for retry_attempt in range(max_retries + 1):  # 0, 1, 2, 3 = 4次尝试
                try:
                    # 🔥 创建验证快照
                    data_snapshot = snapshot_validator.create_validated_snapshot(
                        spot_data=type('SpotData', (), {'timestamp': spot_orderbook.get('timestamp', 0)})(),
                        futures_data=type('FuturesData', (), {'timestamp': futures_orderbook.get('timestamp', 0)})(),
                        spot_orderbook=spot_orderbook,
                        futures_orderbook=futures_orderbook
                    )

                    # 🔥 使用统一验证器进行数据新鲜度检查
                    validation_result = snapshot_validator.validate_market_data_snapshot(
                        data_snapshot,
                        execution_context="closing"  # 平仓上下文
                    )

                    if validation_result.is_valid and validation_result.data_fresh:
                        validation_success = True
                        if retry_attempt > 0:
                            self.logger.info(f"✅ 数据新鲜度重试成功: 第{retry_attempt}次重试")
                        break
                    else:
                        last_validation_error = validation_result.validation_details

                        if retry_attempt < max_retries:
                            self.logger.warning(f"⚠️ 数据新鲜度检查失败，尝试重试 ({retry_attempt + 1}/{max_retries}): {last_validation_error}")

                            # 🔥 重试前等待，让WebSocket获取更新数据
                            await asyncio.sleep(retry_delay / 1000.0)  # 转换为秒

                            # 🔥 重新获取最新WebSocket数据
                            spot_orderbook = self._get_websocket_orderbook(opportunity.buy_exchange, opportunity.symbol, "spot")
                            futures_orderbook = self._get_websocket_orderbook(opportunity.sell_exchange, opportunity.symbol, "futures")

                            if not spot_orderbook or not futures_orderbook:
                                self.logger.error(f"❌ 重试时无法获取WebSocket数据")
                                break

                except Exception as e:
                    last_validation_error = f"验证异常: {e}"
                    self.logger.error(f"❌ 数据新鲜度验证异常: {e}")
                    break

            if not validation_success:
                self.logger.error(f"❌ 数据新鲜度检查失败，已重试{max_retries}次: {last_validation_error}")
                return False

            # 🔥 修复：计算数据年龄用于日志输出
            current_time = time.time() * 1000
            spot_data_age = current_time - spot_orderbook.get('timestamp', current_time)
            futures_data_age = current_time - futures_orderbook.get('timestamp', current_time)

            self.logger.info(f"✅ 平仓验证通过: 使用实时WebSocket数据")
            self.logger.info(f"   历史差价: {historical_spread*100:.3f}% (开仓时)")
            self.logger.info(f"   实时差价: {current_spread*100:.3f}% (平仓时)")
            self.logger.info(f"   差价变化: {spread_change*100:.3f}% (趋同效果)")
            self.logger.info(f"   平仓阈值: {close_spread_min*100:.3f}%")
            self.logger.info(f"   数据新鲜度: spot={spot_data_age:.1f}ms, futures={futures_data_age:.1f}ms")

            # 🔥 获取当前执行结果，确定实际需要平仓的仓位
            execution_result = await self.get_execution_result()
            
            # 🔥 修复：检查是否有实际的交易结果需要平仓
            has_spot_position = (execution_result and execution_result.spot_result and 
                               execution_result.spot_result.success and 
                               execution_result.spot_result.executed_quantity and 
                               execution_result.spot_result.executed_quantity > 0)
            
            has_futures_position = (execution_result and execution_result.futures_result and 
                                  execution_result.futures_result.success and 
                                  execution_result.futures_result.executed_quantity and 
                                  execution_result.futures_result.executed_quantity > 0)
            
            # 🔥 修复：增加总体成功标志的检查
            has_successful_execution = (execution_result and 
                                      getattr(execution_result, 'success', False))
            
            self.logger.info(f"🔍 平仓条件检查:")
            self.logger.info(f"   execution_result存在: {'✅' if execution_result else '❌'}")
            if execution_result:
                self.logger.info(f"   总体执行成功: {'✅' if getattr(execution_result, 'success', False) else '❌'}")
                self.logger.info(f"   现货结果: {'✅' if execution_result.spot_result else '❌'}")
                self.logger.info(f"   期货结果: {'✅' if execution_result.futures_result else '❌'}")
            
            # 🔥 修复：如果总体执行成功，或者有任一仓位成功，都需要平仓
            if not has_successful_execution and not has_spot_position and not has_futures_position:
                self.logger.warning("无有效的执行结果或持仓需要平仓，跳过平仓操作")
                return True
            
            self.logger.info(f"🔍 检测到持仓: 现货={'✅' if has_spot_position else '❌'}, 期货={'✅' if has_futures_position else '❌'}")
            
            if has_spot_position:
                # 🔥 修复持仓显示格式：分离显示币数量和USD价值，符合统一格式
                base_currency = opportunity.symbol.split('-')[0]  # 从BTC-USDT获取BTC
                spot_quantity = execution_result.spot_result.executed_quantity
                spot_price = execution_result.spot_result.executed_price or 0
                spot_value_usd = spot_quantity * spot_price
                self.logger.info(f"   现货持仓: {spot_quantity:.6f} {base_currency} (价值: ${spot_value_usd:.2f})")
                
            if has_futures_position:
                # 🔥 修复持仓显示格式：分离显示币数量和USD价值，符合统一格式
                base_currency = opportunity.symbol.split('-')[0]  # 从BTC-USDT获取BTC
                futures_quantity = execution_result.futures_result.executed_quantity
                futures_price = execution_result.futures_result.executed_price or 0
                futures_value_usd = futures_quantity * futures_price
                self.logger.info(f"   期货持仓: {futures_quantity:.6f} {base_currency} (价值: ${futures_value_usd:.2f})")

            # 🔥 获取交易所实例
            spot_exchange = self.exchanges.get(opportunity.buy_exchange)
            futures_exchange = self.exchanges.get(opportunity.sell_exchange)

            if not spot_exchange or not futures_exchange:
                self.logger.error(f"交易所实例获取失败: spot={spot_exchange}, futures={futures_exchange}")
                return False

            # 🔥 并行平仓：现货和期货同时进行 - 按照35ms目标
            self.logger.info("🔥 开始并行平仓: 现货卖出 + 期货平仓")

            spot_result, futures_result = await asyncio.gather(
                self._close_spot_position(opportunity, spot_exchange),
                self._close_futures_position(opportunity, futures_exchange),
                return_exceptions=True
            )

            # 检查平仓结果
            spot_success = not isinstance(spot_result, Exception) and spot_result
            futures_success = not isinstance(futures_result, Exception) and futures_result

            if isinstance(spot_result, Exception):
                self.logger.error(f"❌ 现货平仓异常: {spot_result}")
                await send_trading_error(
                    exchange=opportunity.buy_exchange,
                    symbol=opportunity.symbol,
                    error=f"现货平仓异常: {spot_result}",
                    amount="平仓"
                )

            if isinstance(futures_result, Exception):
                self.logger.error(f"❌ 期货平仓异常: {futures_result}")
                await send_trading_error(
                    exchange=opportunity.sell_exchange,
                    symbol=opportunity.symbol,
                    error=f"期货平仓异常: {futures_result}",
                    amount="平仓"
                )

            close_success = spot_success and futures_success
            close_time_ms = (time.time() - close_start_time) * 1000

            if close_success:
                self.logger.info("=" * 80)
                self.logger.info(f"✅ 【平仓完成】 {opportunity.symbol}")
                self.logger.info(f"   平仓耗时: {close_time_ms:.2f}ms")
                self.logger.info(f"   性能目标: {'✅达成' if close_time_ms < 35 else '❌未达成'} (<35ms)")
                self.logger.info("=" * 80)
            else:
                self.logger.error("=" * 80)
                self.logger.error(f"❌ 【平仓失败】 {opportunity.symbol}")
                self.logger.error(f"   现货平仓: {'✅成功' if spot_success else '❌失败'}")
                self.logger.error(f"   期货平仓: {'✅成功' if futures_success else '❌失败'}")
                self.logger.error(f"   平仓耗时: {close_time_ms:.2f}ms")
                self.logger.error("=" * 80)

                # 🔥 发送平仓失败通知
                await send_trading_error(
                    exchange=f"{opportunity.buy_exchange}+{opportunity.sell_exchange}",
                    symbol=opportunity.symbol,
                    error=f"平仓失败: 现货{'成功' if spot_success else '失败'}, 期货{'成功' if futures_success else '失败'}",
                    amount="平仓"
                )

            return close_success

        except Exception as e:
            self.logger.error(f"❌ 平仓异常: {e}")
            await send_trading_error(
                exchange=f"{opportunity.buy_exchange}+{opportunity.sell_exchange}",
                symbol=opportunity.symbol,
                error=f"平仓异常: {e}",
                amount="平仓"
            )
            return False

    async def _close_spot_position(self, opportunity: ArbitrageOpportunity, spot_exchange) -> bool:
        """
        🔥 平仓现货仓位 - 卖出现货
        🔥 关键修复：确保使用实时WebSocket数据，不依赖历史数据
        """
        try:
            symbol = opportunity.symbol
            self.logger.info(f"🔍 现货平仓: {opportunity.buy_exchange} {symbol}")

            # 🔥 关键修复：获取实时WebSocket现货订单簿数据用于精准平仓
            try:
                self.logger.info(f"🔍 获取实时WebSocket现货深度数据用于平仓: {symbol}")
                spot_orderbook = self._get_websocket_orderbook(opportunity.buy_exchange, symbol, "spot")
                
                # 🔥 新增：orderbook None检查
                if spot_orderbook is None:
                    self.logger.warning(f"⚠️ WebSocket现货深度数据为None，使用基础平仓")
                    spot_orderbook = None
                elif spot_orderbook and spot_orderbook.get('asks') and spot_orderbook.get('bids'):
                    # 🔥 记录实时数据质量
                    asks_count = len(spot_orderbook.get('asks', []))
                    bids_count = len(spot_orderbook.get('bids', []))
                    timestamp = spot_orderbook.get('timestamp', 0)
                    data_age = (time.time() * 1000 - timestamp) if timestamp else 0
                    
                    self.logger.info(f"✅ 成功获取实时WebSocket现货平仓深度数据:")
                    self.logger.info(f"   深度质量: asks={asks_count}档, bids={bids_count}档")
                    self.logger.info(f"   数据新鲜度: {data_age:.1f}ms")
                    
                    # 🔥 验证平仓方向的深度数据（现货平仓需要bids数据）
                    if bids_count == 0:
                        self.logger.warning(f"⚠️ 现货平仓需要bids数据但为空，尝试重新获取")
                        # 短暂等待后重试一次
                        await asyncio.sleep(0.1)
                        spot_orderbook = self._get_websocket_orderbook(opportunity.buy_exchange, symbol, "spot")
                        if not spot_orderbook or not spot_orderbook.get('bids'):
                            self.logger.error(f"❌ 现货平仓重试后仍无bids数据，使用基础平仓")
                            spot_orderbook = None
                else:
                    self.logger.warning(f"⚠️ WebSocket现货深度数据无效，使用基础平仓")
                    spot_orderbook = None
            except Exception as e:
                self.logger.warning(f"⚠️ 获取WebSocket现货平仓深度数据失败: {e}，使用基础平仓")
                spot_orderbook = None

            # 🔥 使用统一平仓管理器平仓现货 - 传递实时orderbook数据
            result = await self.closing_manager.close_position_unified(
                symbol=symbol,
                exchange=spot_exchange,
                market_type="spot",
                side="sell",  # 现货平仓是卖出
                orderbook=spot_orderbook  # 🔥 关键：传递实时WebSocket订单簿数据
            )

            if result and result.success:
                self.logger.info(f"✅ 现货平仓成功: {result.order_id}")
                self.logger.info(f"   执行数量: {result.executed_quantity:.8f}")
                self.logger.info(f"   执行价格: ${result.executed_price:.6f}")
                return True
            else:
                error_msg = result.error_message if result else "现货平仓失败，无结果返回"
                self.logger.error(f"❌ 现货平仓失败: {error_msg}")
                return False

        except Exception as e:
            self.logger.error(f"❌ 现货平仓异常: {e}")
            return False

    async def _close_futures_position(self, opportunity: ArbitrageOpportunity, futures_exchange) -> bool:
        """
        🔥 平仓期货仓位 - 平掉期货空头
        """
        try:
            symbol = opportunity.symbol
            self.logger.info(f"🔍 期货平仓: {opportunity.sell_exchange} {symbol}")

            # 🔥 使用统一平仓管理器平仓期货
            result = await self.closing_manager.close_position_unified(
                symbol=symbol,
                exchange=futures_exchange,
                market_type="futures"
                # 期货平仓不需要指定side，由平仓管理器自动判断
            )

            if result and result.success:
                self.logger.info(f"✅ 期货平仓成功: {result.order_id}")
                return True
            else:
                error_msg = result.error_message if result else "期货平仓失败，无结果返回"
                self.logger.error(f"❌ 期货平仓失败: {error_msg}")
                return False

        except Exception as e:
            self.logger.error(f"❌ 期货平仓异常: {e}")
            return False

    async def _emergency_close_spot_position(self, opportunity: ArbitrageOpportunity, spot_result: OpeningResult):
        """🚨 紧急平仓现货仓位 - 按照老版本实现"""
        try:
            self.logger.error(f"🚨 开始紧急平仓现货仓位: {opportunity.symbol}")

            spot_exchange = self.exchanges.get(opportunity.buy_exchange)
            if not spot_exchange:
                self.logger.error(f"❌ 无法获取现货交易所: {opportunity.buy_exchange}")
                return

            # 🔥 修复：获取现货深度数据，确保紧急平仓有orderbook数据
            orderbook = None
            try:
                # 1. 首先尝试从执行上下文获取预取的深度数据
                if hasattr(self, 'current_execution') and self.current_execution:
                    orderbook = getattr(self.current_execution, 'spot_depth', None)
                    if orderbook:
                        self.logger.info(f"✅ 紧急平仓使用预取的现货深度数据: {opportunity.symbol}")

                # 2. 如果没有预取数据，从WebSocket获取实时数据
                if not orderbook:
                    self.logger.info(f"🔍 紧急平仓获取WebSocket现货深度数据: {opportunity.symbol}")
                    # 🔥 修复：使用WebSocket实时数据，删除REST API调用
                    try:
                        orderbook = self._get_websocket_orderbook(opportunity.buy_exchange, opportunity.symbol, "spot")
                    except Exception as e:
                        self.logger.warning(f"⚠️ 获取WebSocket深度数据失败: {e}")
                        orderbook = None

                    if orderbook and orderbook.get('asks') and orderbook.get('bids'):
                        self.logger.info(f"✅ 紧急平仓成功获取WebSocket深度数据: asks={len(orderbook.get('asks', []))}, bids={len(orderbook.get('bids', []))}")
                    else:
                        self.logger.warning(f"⚠️ 紧急平仓WebSocket深度数据无效，使用基础平仓")
                        orderbook = None

            except Exception as e:
                self.logger.warning(f"⚠️ 紧急平仓获取深度数据失败: {e}，使用基础平仓")
                orderbook = None

            # 🔥 修复：使用SpotTrader直接执行紧急平仓，确保orderbook数据传递
            spot_trader = self.spot_traders.get(opportunity.buy_exchange)
            if spot_trader and spot_result and spot_result.executed_quantity:

                # 🔥 关键修复：等待现货订单完全成交，避免Bybit资金锁定问题
                if spot_result.order_id:
                    self.logger.info(f"🔍 等待现货订单完全成交: {spot_result.order_id}")

                    # 🔥 增强等待机制：多次检查订单状态直到完全成交
                    max_wait_attempts = 10  # 最多等待10次
                    wait_interval = 0.5  # 每次等待0.5秒

                    for attempt in range(max_wait_attempts):
                        await asyncio.sleep(wait_interval)

                        try:
                            spot_exchange = self.exchanges.get(opportunity.buy_exchange)
                            if spot_exchange and hasattr(spot_exchange, 'get_order'):
                                order_status = await spot_exchange.get_order(spot_result.order_id, opportunity.symbol, market_type="spot")
                                if order_status and order_status.get('status') in ['filled', 'closed']:
                                    self.logger.info(f"✅ 现货订单已完全成交: {spot_result.order_id}")
                                    break  # 订单已完全成交，退出等待循环
                                else:
                                    self.logger.debug(f"⏳ 等待现货订单成交 (尝试{attempt+1}/{max_wait_attempts}): {order_status.get('status') if order_status else 'unknown'}")
                        except Exception as e:
                            self.logger.warning(f"⚠️ 检查现货订单状态失败 (尝试{attempt+1}): {e}")

                    self.logger.info(f"✅ 现货订单等待完成，开始紧急平仓")

                # 🚨 通用系统修复：使用现有的安全数量计算，避免造轮子
                try:
                    # 🔥 使用现有的ExchangeParamAdapter.calculate_safe_amount方法
                    from exchanges.exchange_adapters import ExchangeParamAdapter

                    spot_exchange_name = spot_exchange.__class__.__name__.lower().replace("exchange", "")
                    original_amount = spot_result.executed_quantity

                    # 获取当前价格（用于安全数量计算）
                    current_price = orderbook.get('bids', [[0, 0]])[0][0] if orderbook else 1.0

                    # 🔥 使用通用的安全数量计算方法，复用现有逻辑
                    safe_amount = ExchangeParamAdapter.calculate_safe_amount(
                        spot_exchange_name,
                        opportunity.symbol,
                        "spot",
                        original_amount,
                        current_price
                    )

                    # 🔥 使用系统统一的安全边际标准，避免硬编码
                    if safe_amount > original_amount:
                        # 使用与MarginCalculator一致的5%安全边际（从缓存配置读取）
                        safety_margin_rate = self._cached_config.get('order_depth_safety_factor', 0.05)
                        safe_amount = original_amount * (1 - safety_margin_rate)

                    self.logger.info(f"🔧 通用安全平仓数量调整:")
                    self.logger.info(f"   买入数量: {original_amount:.8f}")
                    self.logger.info(f"   安全数量: {safe_amount:.8f}")
                    self.logger.info(f"   使用方法: ExchangeParamAdapter.calculate_safe_amount")

                except Exception as balance_check_error:
                    self.logger.warning(f"⚠️ 安全数量计算失败，使用原始数量: {balance_check_error}")
                    safe_amount = spot_result.executed_quantity

                # 使用SpotTrader的market_sell方法进行紧急平仓
                close_result = await spot_trader.market_sell(
                    symbol=opportunity.symbol,
                    amount=safe_amount,  # 🔥 使用安全数量
                    orderbook=orderbook,  # 🔥 关键：传递深度数据
                    disable_split=True  # 紧急平仓不拆单
                )

                if close_result and close_result.get('status') in ['filled', 'open', 'new', 'success']:
                    self.logger.info(f"✅ 紧急平仓现货成功: {close_result.get('id', 'unknown')}")
                else:
                    self.logger.error(f"❌ 紧急平仓现货失败: {close_result}")
            else:
                # 兜底方案：使用统一平仓管理器
                close_result = await self.closing_manager.close_position_unified(
                    symbol=opportunity.symbol,
                    exchange=spot_exchange,
                    market_type="spot",
                    side="sell"
                )

                if close_result.success:
                    self.logger.info(f"✅ 紧急平仓现货成功（兜底）: {close_result.order_id}")
                else:
                    self.logger.error(f"❌ 紧急平仓现货失败（兜底）: {close_result.error_message}")

        except Exception as e:
            self.logger.error(f"❌ 紧急平仓现货异常: {e}")

    async def _emergency_close_futures_position(self, opportunity: ArbitrageOpportunity, futures_result: OpeningResult):
        """🚨 紧急平仓期货仓位 - 按照老版本实现"""
        try:
            self.logger.error(f"🚨 开始紧急平仓期货仓位: {opportunity.symbol}")

            futures_exchange = self.exchanges.get(opportunity.sell_exchange)
            if not futures_exchange:
                self.logger.error(f"❌ 无法获取期货交易所: {opportunity.sell_exchange}")
                return

            # 使用统一平仓管理器紧急平仓
            close_result = await self.closing_manager.close_position_unified(
                symbol=opportunity.symbol,
                exchange=futures_exchange,
                market_type="futures"
                # 期货平仓不需要指定side，由平仓管理器自动判断
            )

            if close_result.success:
                self.logger.info(f"✅ 紧急平仓期货成功: {close_result.order_id}")
            else:
                self.logger.error(f"❌ 紧急平仓期货失败: {close_result.error_message}")

        except Exception as e:
            self.logger.error(f"❌ 紧急平仓期货异常: {e}")

    async def get_execution_result(self) -> Optional[ExecutionResult]:
        """
        🔥 获取执行结果 - 供ArbitrageEngine调用
        """
        return self.current_execution

    def _update_execution_stats(self, execution_time: float, success: bool):
        """更新执行统计信息"""
        try:
            self.execution_stats['total_executions'] += 1

            if success:
                self.execution_stats['successful_executions'] += 1
            else:
                self.execution_stats['failed_executions'] += 1

            # 更新执行时间统计
            if execution_time < self.execution_stats['fastest_execution']:
                self.execution_stats['fastest_execution'] = execution_time

            if execution_time > self.execution_stats['slowest_execution']:
                self.execution_stats['slowest_execution'] = execution_time

            # 计算平均执行时间
            total_time = (self.execution_stats['average_execution_time'] *
                         (self.execution_stats['total_executions'] - 1) + execution_time)
            self.execution_stats['average_execution_time'] = total_time / self.execution_stats['total_executions']

        except Exception as e:
            self.logger.error(f"更新执行统计失败: {e}")

    def get_execution_stats(self) -> Dict[str, Any]:
        """获取执行统计信息"""
        return self.execution_stats.copy()

    def get_current_status(self) -> ExecutionStatus:
        """获取当前执行状态"""
        return self.current_status

    def get_current_execution(self) -> Optional[ExecutionResult]:
        """获取当前执行结果"""
        return self.current_execution

    async def _reset_execution_state(self):
        """🔥 统一错误恢复：重置执行状态，使用SystemMonitor进行恢复验证"""
        try:
            self.logger.info("🔧 重置ExecutionEngine状态...")

            # 🔥 修复：使用统一的SystemMonitor进行错误分析
            from core.system_monitor import get_system_monitor
            system_monitor = get_system_monitor()

            # 委托给SystemMonitor进行健康检查
            health_ok = await system_monitor.perform_health_check_for_component('execution_engine', self)
            if not health_ok:
                self.logger.warning("⚠️ ExecutionEngine健康检查失败，执行强制重置")

            # 重置基本状态
            self.current_status = ExecutionStatus.IDLE
            self.current_execution = None

            # 释放执行锁
            if self.execution_lock.locked():
                self.execution_lock.release()
                self.logger.debug("✅ 执行锁已释放")

            # 🔥 修复：使用统一的SystemMonitor验证恢复就绪
            if await system_monitor.verify_system_ready_for_recovery():
                self.logger.info("✅ ExecutionEngine状态重置完成，系统恢复就绪")
            else:
                self.logger.warning("⚠️ ExecutionEngine状态重置完成，但系统恢复验证失败")

        except Exception as e:
            self.logger.error(f"❌ ExecutionEngine状态重置失败: {e}")
            # 强制重置
            self.current_status = ExecutionStatus.IDLE
            self.current_execution = None

    async def cleanup(self):
        """🔥 统一错误恢复：清理执行引擎，使用SystemMonitor进行协调"""
        try:
            self.logger.info("🧹 清理ExecutionEngine...")

            # 🔥 修复：使用统一的SystemMonitor进行清理协调
            from core.system_monitor import get_system_monitor
            system_monitor = get_system_monitor()

            # 执行状态重置
            await self._reset_execution_state()

            # 委托给SystemMonitor进行最终验证
            if await system_monitor.perform_health_check_for_component('execution_engine', self):
                self.logger.info("✅ ExecutionEngine清理完成，健康检查通过")
            else:
                self.logger.warning("⚠️ ExecutionEngine清理完成，但健康检查失败")

        except Exception as e:
            self.logger.error(f"❌ ExecutionEngine清理失败: {e}")
            # 🔥 修复：即使清理失败，也要确保基本状态重置
            self.current_status = ExecutionStatus.IDLE
            self.current_execution = None


# 🔥 全局实例管理
_execution_engine = None

def get_execution_engine() -> ExecutionEngine:
    """获取ExecutionEngine全局实例"""
    global _execution_engine
    if _execution_engine is None:
        _execution_engine = ExecutionEngine()
    return _execution_engine

def reset_execution_engine():
    """🔥 重置ExecutionEngine全局实例 - 用于模块重新加载"""
    global _execution_engine
    _execution_engine = None