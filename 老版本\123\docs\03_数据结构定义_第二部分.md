# 📊 通用期货溢价套利系统 - 数据结构定义（第二部分：执行结果和监控）

## 📈 4. 执行结果类

### 4.1 开仓结果

```python
@dataclass
class OpeningResult:
    """开仓结果数据类"""
    success: bool
    order_id: Optional[str] = None
    symbol: str = ""
    side: str = ""
    executed_quantity: float = 0.0
    executed_price: float = 0.0
    total_cost: float = 0.0
    execution_time_ms: float = 0.0
    error_message: Optional[str] = None
    retry_count: int = 0
    exchange: str = ""
    market_type: str = "spot"
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "success": self.success,
            "order_id": self.order_id,
            "symbol": self.symbol,
            "side": self.side,
            "executed_quantity": self.executed_quantity,
            "executed_price": self.executed_price,
            "total_cost": self.total_cost,
            "execution_time_ms": self.execution_time_ms,
            "error_message": self.error_message,
            "retry_count": self.retry_count,
            "exchange": self.exchange,
            "market_type": self.market_type
        }

@dataclass
class ClosingResult:
    """平仓结果数据类"""
    success: bool
    order_id: Optional[str] = None
    symbol: str = ""
    executed_quantity: float = 0.0
    executed_price: float = 0.0
    realized_pnl: float = 0.0
    execution_time_ms: float = 0.0
    error_message: Optional[str] = None
    retry_count: int = 0
    exchange: str = ""
    market_type: str = "spot"
    remaining_position: float = 0.0
    
    def is_complete_close(self) -> bool:
        """是否完全平仓"""
        return self.success and abs(self.remaining_position) < 0.001
```

### 4.2 套利执行结果

```python
@dataclass
class ArbitrageExecutionResult:
    """套利执行结果数据类"""
    opportunity_id: str
    success: bool
    spot_result: Optional[OpeningResult] = None
    futures_result: Optional[OpeningResult] = None
    total_execution_time_ms: float = 0.0
    hedge_quality_achieved: float = 0.0
    estimated_profit: float = 0.0
    actual_cost: float = 0.0
    error_message: Optional[str] = None
    timestamp: float = field(default_factory=time.time)
    
    @property
    def both_orders_successful(self) -> bool:
        """两个订单是否都成功"""
        return (
            self.spot_result is not None and self.spot_result.success and
            self.futures_result is not None and self.futures_result.success
        )
    
    def calculate_actual_hedge_quality(self) -> float:
        """计算实际对冲质量"""
        if not self.both_orders_successful:
            return 0.0
        
        spot_value = self.spot_result.executed_quantity * self.spot_result.executed_price
        futures_value = self.futures_result.executed_quantity * self.futures_result.executed_price
        
        if max(spot_value, futures_value) == 0:
            return 0.0
        
        return min(spot_value, futures_value) / max(spot_value, futures_value)
```

## 📡 5. WebSocket数据类

### 5.1 市场数据

```python
@dataclass
class MarketData:
    """市场数据数据类"""
    exchange: str
    symbol: str
    price: float
    volume: float
    timestamp: float
    bid: float = 0.0
    ask: float = 0.0
    
    @property
    def spread(self) -> float:
        """买卖价差"""
        return self.ask - self.bid if self.ask > 0 and self.bid > 0 else 0.0

@dataclass
class OrderBookData:
    """订单簿数据数据类"""
    exchange: str
    symbol: str
    bids: List[List[float]]  # [[price, quantity], ...]
    asks: List[List[float]]  # [[price, quantity], ...]
    timestamp: float
    
    def get_best_bid(self) -> Optional[float]:
        """获取最佳买价"""
        return self.bids[0][0] if self.bids else None
    
    def get_best_ask(self) -> Optional[float]:
        """获取最佳卖价"""
        return self.asks[0][0] if self.asks else None
    
    def get_depth_analysis(self, target_amount: float) -> Dict[str, float]:
        """获取深度分析"""
        bid_depth = sum(qty for price, qty in self.bids if qty > 0)
        ask_depth = sum(qty for price, qty in self.asks if qty > 0)
        
        return {
            "bid_depth": bid_depth,
            "ask_depth": ask_depth,
            "sufficient_bid_depth": bid_depth >= target_amount,
            "sufficient_ask_depth": ask_depth >= target_amount
        }
```

## 🔧 6. 系统监控数据类

### 6.1 性能统计

```python
@dataclass
class PerformanceStats:
    """性能统计数据类"""
    total_opportunities: int = 0
    successful_executions: int = 0
    failed_executions: int = 0
    total_profit: float = 0.0
    total_execution_time_ms: float = 0.0
    average_execution_time_ms: float = 0.0
    success_rate: float = 0.0
    last_updated: float = field(default_factory=time.time)
    
    def update_stats(self, result: ArbitrageExecutionResult) -> None:
        """更新统计数据"""
        self.total_opportunities += 1
        
        if result.success:
            self.successful_executions += 1
            self.total_profit += result.estimated_profit
        else:
            self.failed_executions += 1
        
        self.total_execution_time_ms += result.total_execution_time_ms
        self.average_execution_time_ms = self.total_execution_time_ms / self.total_opportunities
        self.success_rate = self.successful_executions / self.total_opportunities
        self.last_updated = time.time()

@dataclass
class SystemStatus:
    """系统状态数据类"""
    arbitrage_status: ArbitrageStatus
    websocket_connections: Dict[str, ConnectionStatus]
    active_positions: int
    total_balance_usd: float
    last_opportunity_time: Optional[float] = None
    uptime_seconds: float = 0.0
    error_count: int = 0
    
    def is_healthy(self) -> bool:
        """检查系统是否健康"""
        all_connected = all(
            status == ConnectionStatus.CONNECTED 
            for status in self.websocket_connections.values()
        )
        return all_connected and self.error_count < 10
```

## ✅ 数据验证和序列化

### 6.1 数据验证工具

```python
import re
import math

class DataValidator:
    """数据验证工具类"""
    
    @staticmethod
    def validate_symbol(symbol: str) -> bool:
        """验证交易对格式"""
        return bool(re.match(r'^[A-Z]+-[A-Z]+$', symbol))
    
    @staticmethod
    def validate_amount(amount: float, min_amount: float = 0.0) -> bool:
        """验证金额"""
        return amount > min_amount and not math.isnan(amount) and math.isfinite(amount)
    
    @staticmethod
    def validate_price(price: float) -> bool:
        """验证价格"""
        return price > 0 and not math.isnan(price) and math.isfinite(price)
    
    @staticmethod
    def validate_hedge_quality(hedge_quality: float) -> bool:
        """验证对冲质量"""
        return 0.0 <= hedge_quality <= 1.0
    
    @staticmethod
    def validate_execution_result(result: ArbitrageExecutionResult) -> List[str]:
        """验证执行结果"""
        errors = []
        
        if not result.opportunity_id:
            errors.append("缺少机会ID")
        
        if result.success and not result.both_orders_successful:
            errors.append("成功标记与订单结果不一致")
        
        if result.hedge_quality_achieved < 0 or result.hedge_quality_achieved > 1:
            errors.append("对冲质量超出有效范围")
        
        return errors
```

### 6.2 序列化工具

```python
import json
from typing import Any

class DataSerializer:
    """数据序列化工具类"""
    
    @staticmethod
    def to_json(obj: Any) -> str:
        """转换为JSON字符串"""
        if hasattr(obj, 'to_dict'):
            return json.dumps(obj.to_dict(), ensure_ascii=False, indent=2)
        elif hasattr(obj, '__dict__'):
            return json.dumps(obj.__dict__, ensure_ascii=False, indent=2)
        else:
            return json.dumps(obj, ensure_ascii=False, indent=2)
    
    @staticmethod
    def from_json(json_str: str, target_class: type) -> Any:
        """从JSON字符串创建对象"""
        data = json.loads(json_str)
        return target_class(**data)
    
    @staticmethod
    def serialize_list(obj_list: List[Any]) -> str:
        """序列化对象列表"""
        serialized = []
        for obj in obj_list:
            if hasattr(obj, 'to_dict'):
                serialized.append(obj.to_dict())
            elif hasattr(obj, '__dict__'):
                serialized.append(obj.__dict__)
            else:
                serialized.append(obj)
        
        return json.dumps(serialized, ensure_ascii=False, indent=2)
```

## 🎯 使用示例

### 6.1 创建和验证数据

```python
# 创建套利机会
opportunity = ArbitrageOpportunity(
    id="arb_001",
    spot_exchange="gate",
    futures_exchange="bybit",
    symbol="BTC-USDT",
    spot_price=50000.0,
    futures_price=50100.0,
    spread=100.0,
    spread_percentage=0.002,
    spot_volume=1.0,
    futures_volume=1.0,
    estimated_profit=95.0,
    confidence_score=0.95,
    timestamp=time.time()
)

# 验证数据
if opportunity.is_valid():
    print("套利机会有效")

# 序列化
json_str = DataSerializer.to_json(opportunity)
print(json_str)
```

---

**📝 注意**: 本文档定义的数据结构为系统核心数据规范，确保数据传递的类型安全和一致性。
