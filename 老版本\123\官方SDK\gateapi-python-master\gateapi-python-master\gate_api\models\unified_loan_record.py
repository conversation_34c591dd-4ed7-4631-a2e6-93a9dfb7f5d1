# coding: utf-8

"""
    Gate API v4

    Welcome to Gate.io API  APIv4 provides spot, margin and futures trading operations. There are public APIs to retrieve the real-time market statistics, and private APIs which needs authentication to trade on user's behalf.  # noqa: E501

    Contact: <EMAIL>
    Generated by: https://openapi-generator.tech
"""


import pprint
import re  # noqa: F401

import six

from gate_api.configuration import Configuration


class UnifiedLoanRecord(object):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        'id': 'int',
        'type': 'str',
        'repayment_type': 'str',
        'borrow_type': 'str',
        'currency_pair': 'str',
        'currency': 'str',
        'amount': 'str',
        'create_time': 'int'
    }

    attribute_map = {
        'id': 'id',
        'type': 'type',
        'repayment_type': 'repayment_type',
        'borrow_type': 'borrow_type',
        'currency_pair': 'currency_pair',
        'currency': 'currency',
        'amount': 'amount',
        'create_time': 'create_time'
    }

    def __init__(self, id=None, type=None, repayment_type=None, borrow_type=None, currency_pair=None, currency=None, amount=None, create_time=None, local_vars_configuration=None):  # noqa: E501
        # type: (int, str, str, str, str, str, str, int, Configuration) -> None
        """UnifiedLoanRecord - a model defined in OpenAPI"""  # noqa: E501
        if local_vars_configuration is None:
            local_vars_configuration = Configuration()
        self.local_vars_configuration = local_vars_configuration

        self._id = None
        self._type = None
        self._repayment_type = None
        self._borrow_type = None
        self._currency_pair = None
        self._currency = None
        self._amount = None
        self._create_time = None
        self.discriminator = None

        if id is not None:
            self.id = id
        if type is not None:
            self.type = type
        if repayment_type is not None:
            self.repayment_type = repayment_type
        if borrow_type is not None:
            self.borrow_type = borrow_type
        if currency_pair is not None:
            self.currency_pair = currency_pair
        if currency is not None:
            self.currency = currency
        if amount is not None:
            self.amount = amount
        if create_time is not None:
            self.create_time = create_time

    @property
    def id(self):
        """Gets the id of this UnifiedLoanRecord.  # noqa: E501

        id  # noqa: E501

        :return: The id of this UnifiedLoanRecord.  # noqa: E501
        :rtype: int
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this UnifiedLoanRecord.

        id  # noqa: E501

        :param id: The id of this UnifiedLoanRecord.  # noqa: E501
        :type: int
        """

        self._id = id

    @property
    def type(self):
        """Gets the type of this UnifiedLoanRecord.  # noqa: E501

        type: borrow - borrow, repay - repay  # noqa: E501

        :return: The type of this UnifiedLoanRecord.  # noqa: E501
        :rtype: str
        """
        return self._type

    @type.setter
    def type(self, type):
        """Sets the type of this UnifiedLoanRecord.

        type: borrow - borrow, repay - repay  # noqa: E501

        :param type: The type of this UnifiedLoanRecord.  # noqa: E501
        :type: str
        """

        self._type = type

    @property
    def repayment_type(self):
        """Gets the repayment_type of this UnifiedLoanRecord.  # noqa: E501

        Repayment type, none - No repayment type, manual_repay - Manual repayment, auto_repay - Automatic repayment, cancel_auto_repay - Automatic repayment after withdrawal, different_currencies_repayment - Different currency repayment  # noqa: E501

        :return: The repayment_type of this UnifiedLoanRecord.  # noqa: E501
        :rtype: str
        """
        return self._repayment_type

    @repayment_type.setter
    def repayment_type(self, repayment_type):
        """Sets the repayment_type of this UnifiedLoanRecord.

        Repayment type, none - No repayment type, manual_repay - Manual repayment, auto_repay - Automatic repayment, cancel_auto_repay - Automatic repayment after withdrawal, different_currencies_repayment - Different currency repayment  # noqa: E501

        :param repayment_type: The repayment_type of this UnifiedLoanRecord.  # noqa: E501
        :type: str
        """

        self._repayment_type = repayment_type

    @property
    def borrow_type(self):
        """Gets the borrow_type of this UnifiedLoanRecord.  # noqa: E501

        Loan type, returned when querying loan records. manual_borrow - Manual repayment , auto_borrow - Automatic repayment  # noqa: E501

        :return: The borrow_type of this UnifiedLoanRecord.  # noqa: E501
        :rtype: str
        """
        return self._borrow_type

    @borrow_type.setter
    def borrow_type(self, borrow_type):
        """Sets the borrow_type of this UnifiedLoanRecord.

        Loan type, returned when querying loan records. manual_borrow - Manual repayment , auto_borrow - Automatic repayment  # noqa: E501

        :param borrow_type: The borrow_type of this UnifiedLoanRecord.  # noqa: E501
        :type: str
        """

        self._borrow_type = borrow_type

    @property
    def currency_pair(self):
        """Gets the currency_pair of this UnifiedLoanRecord.  # noqa: E501

        Currency pair  # noqa: E501

        :return: The currency_pair of this UnifiedLoanRecord.  # noqa: E501
        :rtype: str
        """
        return self._currency_pair

    @currency_pair.setter
    def currency_pair(self, currency_pair):
        """Sets the currency_pair of this UnifiedLoanRecord.

        Currency pair  # noqa: E501

        :param currency_pair: The currency_pair of this UnifiedLoanRecord.  # noqa: E501
        :type: str
        """

        self._currency_pair = currency_pair

    @property
    def currency(self):
        """Gets the currency of this UnifiedLoanRecord.  # noqa: E501

        Currency  # noqa: E501

        :return: The currency of this UnifiedLoanRecord.  # noqa: E501
        :rtype: str
        """
        return self._currency

    @currency.setter
    def currency(self, currency):
        """Sets the currency of this UnifiedLoanRecord.

        Currency  # noqa: E501

        :param currency: The currency of this UnifiedLoanRecord.  # noqa: E501
        :type: str
        """

        self._currency = currency

    @property
    def amount(self):
        """Gets the amount of this UnifiedLoanRecord.  # noqa: E501

        The amount of lending or repaying  # noqa: E501

        :return: The amount of this UnifiedLoanRecord.  # noqa: E501
        :rtype: str
        """
        return self._amount

    @amount.setter
    def amount(self, amount):
        """Sets the amount of this UnifiedLoanRecord.

        The amount of lending or repaying  # noqa: E501

        :param amount: The amount of this UnifiedLoanRecord.  # noqa: E501
        :type: str
        """

        self._amount = amount

    @property
    def create_time(self):
        """Gets the create_time of this UnifiedLoanRecord.  # noqa: E501

        Created time  # noqa: E501

        :return: The create_time of this UnifiedLoanRecord.  # noqa: E501
        :rtype: int
        """
        return self._create_time

    @create_time.setter
    def create_time(self, create_time):
        """Sets the create_time of this UnifiedLoanRecord.

        Created time  # noqa: E501

        :param create_time: The create_time of this UnifiedLoanRecord.  # noqa: E501
        :type: int
        """

        self._create_time = create_time

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.openapi_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, UnifiedLoanRecord):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, UnifiedLoanRecord):
            return True

        return self.to_dict() != other.to_dict()
