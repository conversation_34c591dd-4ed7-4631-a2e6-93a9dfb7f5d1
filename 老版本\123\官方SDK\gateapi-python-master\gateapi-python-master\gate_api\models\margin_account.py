# coding: utf-8

"""
    Gate API v4

    Welcome to Gate.io API  APIv4 provides spot, margin and futures trading operations. There are public APIs to retrieve the real-time market statistics, and private APIs which needs authentication to trade on user's behalf.  # noqa: E501

    Contact: <EMAIL>
    Generated by: https://openapi-generator.tech
"""


import pprint
import re  # noqa: F401

import six

from gate_api.configuration import Configuration


class MarginAccount(object):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        'currency_pair': 'str',
        'account_type': 'str',
        'leverage': 'str',
        'locked': 'bool',
        'risk': 'str',
        'mmr': 'str',
        'base': 'MarginAccountCurrency',
        'quote': 'MarginAccountCurrency'
    }

    attribute_map = {
        'currency_pair': 'currency_pair',
        'account_type': 'account_type',
        'leverage': 'leverage',
        'locked': 'locked',
        'risk': 'risk',
        'mmr': 'mmr',
        'base': 'base',
        'quote': 'quote'
    }

    def __init__(self, currency_pair=None, account_type=None, leverage=None, locked=None, risk=None, mmr=None, base=None, quote=None, local_vars_configuration=None):  # noqa: E501
        # type: (str, str, str, bool, str, str, MarginAccountCurrency, MarginAccountCurrency, Configuration) -> None
        """MarginAccount - a model defined in OpenAPI"""  # noqa: E501
        if local_vars_configuration is None:
            local_vars_configuration = Configuration()
        self.local_vars_configuration = local_vars_configuration

        self._currency_pair = None
        self._account_type = None
        self._leverage = None
        self._locked = None
        self._risk = None
        self._mmr = None
        self._base = None
        self._quote = None
        self.discriminator = None

        if currency_pair is not None:
            self.currency_pair = currency_pair
        if account_type is not None:
            self.account_type = account_type
        if leverage is not None:
            self.leverage = leverage
        if locked is not None:
            self.locked = locked
        if risk is not None:
            self.risk = risk
        if mmr is not None:
            self.mmr = mmr
        if base is not None:
            self.base = base
        if quote is not None:
            self.quote = quote

    @property
    def currency_pair(self):
        """Gets the currency_pair of this MarginAccount.  # noqa: E501

        Currency pair  # noqa: E501

        :return: The currency_pair of this MarginAccount.  # noqa: E501
        :rtype: str
        """
        return self._currency_pair

    @currency_pair.setter
    def currency_pair(self, currency_pair):
        """Sets the currency_pair of this MarginAccount.

        Currency pair  # noqa: E501

        :param currency_pair: The currency_pair of this MarginAccount.  # noqa: E501
        :type: str
        """

        self._currency_pair = currency_pair

    @property
    def account_type(self):
        """Gets the account_type of this MarginAccount.  # noqa: E501

        Account type, risk - risk rate account, mmr - maintenance margin rate account, inactive - market not activated  # noqa: E501

        :return: The account_type of this MarginAccount.  # noqa: E501
        :rtype: str
        """
        return self._account_type

    @account_type.setter
    def account_type(self, account_type):
        """Sets the account_type of this MarginAccount.

        Account type, risk - risk rate account, mmr - maintenance margin rate account, inactive - market not activated  # noqa: E501

        :param account_type: The account_type of this MarginAccount.  # noqa: E501
        :type: str
        """

        self._account_type = account_type

    @property
    def leverage(self):
        """Gets the leverage of this MarginAccount.  # noqa: E501

        User current market leverage multiple  # noqa: E501

        :return: The leverage of this MarginAccount.  # noqa: E501
        :rtype: str
        """
        return self._leverage

    @leverage.setter
    def leverage(self, leverage):
        """Sets the leverage of this MarginAccount.

        User current market leverage multiple  # noqa: E501

        :param leverage: The leverage of this MarginAccount.  # noqa: E501
        :type: str
        """

        self._leverage = leverage

    @property
    def locked(self):
        """Gets the locked of this MarginAccount.  # noqa: E501

        Whether account is locked  # noqa: E501

        :return: The locked of this MarginAccount.  # noqa: E501
        :rtype: bool
        """
        return self._locked

    @locked.setter
    def locked(self, locked):
        """Sets the locked of this MarginAccount.

        Whether account is locked  # noqa: E501

        :param locked: The locked of this MarginAccount.  # noqa: E501
        :type: bool
        """

        self._locked = locked

    @property
    def risk(self):
        """Gets the risk of this MarginAccount.  # noqa: E501

        Leveraged Account Current Risk Rate (Returned when the Account is a Risk Rate Account)  # noqa: E501

        :return: The risk of this MarginAccount.  # noqa: E501
        :rtype: str
        """
        return self._risk

    @risk.setter
    def risk(self, risk):
        """Sets the risk of this MarginAccount.

        Leveraged Account Current Risk Rate (Returned when the Account is a Risk Rate Account)  # noqa: E501

        :param risk: The risk of this MarginAccount.  # noqa: E501
        :type: str
        """

        self._risk = risk

    @property
    def mmr(self):
        """Gets the mmr of this MarginAccount.  # noqa: E501

        Leveraged Account Current Maintenance Margin Rate (returned when the Account is a Maintenance Margin Rate Account)  # noqa: E501

        :return: The mmr of this MarginAccount.  # noqa: E501
        :rtype: str
        """
        return self._mmr

    @mmr.setter
    def mmr(self, mmr):
        """Sets the mmr of this MarginAccount.

        Leveraged Account Current Maintenance Margin Rate (returned when the Account is a Maintenance Margin Rate Account)  # noqa: E501

        :param mmr: The mmr of this MarginAccount.  # noqa: E501
        :type: str
        """

        self._mmr = mmr

    @property
    def base(self):
        """Gets the base of this MarginAccount.  # noqa: E501


        :return: The base of this MarginAccount.  # noqa: E501
        :rtype: MarginAccountCurrency
        """
        return self._base

    @base.setter
    def base(self, base):
        """Sets the base of this MarginAccount.


        :param base: The base of this MarginAccount.  # noqa: E501
        :type: MarginAccountCurrency
        """

        self._base = base

    @property
    def quote(self):
        """Gets the quote of this MarginAccount.  # noqa: E501


        :return: The quote of this MarginAccount.  # noqa: E501
        :rtype: MarginAccountCurrency
        """
        return self._quote

    @quote.setter
    def quote(self, quote):
        """Sets the quote of this MarginAccount.


        :param quote: The quote of this MarginAccount.  # noqa: E501
        :type: MarginAccountCurrency
        """

        self._quote = quote

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.openapi_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, MarginAccount):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, MarginAccount):
            return True

        return self.to_dict() != other.to_dict()
