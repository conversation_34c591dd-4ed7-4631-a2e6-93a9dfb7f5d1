# coding: utf-8

"""
    Gate API v4

    Welcome to Gate.io API  APIv4 provides spot, margin and futures trading operations. There are public APIs to retrieve the real-time market statistics, and private APIs which needs authentication to trade on user's behalf.  # noqa: E501

    Contact: <EMAIL>
    Generated by: https://openapi-generator.tech
"""


import pprint
import re  # noqa: F401

import six

from gate_api.configuration import Configuration


class Currency(object):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        'currency': 'str',
        'name': 'str',
        'delisted': 'bool',
        'withdraw_disabled': 'bool',
        'withdraw_delayed': 'bool',
        'deposit_disabled': 'bool',
        'trade_disabled': 'bool',
        'fixed_rate': 'str',
        'chain': 'str',
        'chains': 'list[Spot<PERSON>urrency<PERSON>hain]'
    }

    attribute_map = {
        'currency': 'currency',
        'name': 'name',
        'delisted': 'delisted',
        'withdraw_disabled': 'withdraw_disabled',
        'withdraw_delayed': 'withdraw_delayed',
        'deposit_disabled': 'deposit_disabled',
        'trade_disabled': 'trade_disabled',
        'fixed_rate': 'fixed_rate',
        'chain': 'chain',
        'chains': 'chains'
    }

    def __init__(self, currency=None, name=None, delisted=None, withdraw_disabled=None, withdraw_delayed=None, deposit_disabled=None, trade_disabled=None, fixed_rate=None, chain=None, chains=None, local_vars_configuration=None):  # noqa: E501
        # type: (str, str, bool, bool, bool, bool, bool, str, str, list[SpotCurrencyChain], Configuration) -> None
        """Currency - a model defined in OpenAPI"""  # noqa: E501
        if local_vars_configuration is None:
            local_vars_configuration = Configuration()
        self.local_vars_configuration = local_vars_configuration

        self._currency = None
        self._name = None
        self._delisted = None
        self._withdraw_disabled = None
        self._withdraw_delayed = None
        self._deposit_disabled = None
        self._trade_disabled = None
        self._fixed_rate = None
        self._chain = None
        self._chains = None
        self.discriminator = None

        if currency is not None:
            self.currency = currency
        if name is not None:
            self.name = name
        if delisted is not None:
            self.delisted = delisted
        if withdraw_disabled is not None:
            self.withdraw_disabled = withdraw_disabled
        if withdraw_delayed is not None:
            self.withdraw_delayed = withdraw_delayed
        if deposit_disabled is not None:
            self.deposit_disabled = deposit_disabled
        if trade_disabled is not None:
            self.trade_disabled = trade_disabled
        if fixed_rate is not None:
            self.fixed_rate = fixed_rate
        if chain is not None:
            self.chain = chain
        if chains is not None:
            self.chains = chains

    @property
    def currency(self):
        """Gets the currency of this Currency.  # noqa: E501

        Currency symbol  # noqa: E501

        :return: The currency of this Currency.  # noqa: E501
        :rtype: str
        """
        return self._currency

    @currency.setter
    def currency(self, currency):
        """Sets the currency of this Currency.

        Currency symbol  # noqa: E501

        :param currency: The currency of this Currency.  # noqa: E501
        :type: str
        """

        self._currency = currency

    @property
    def name(self):
        """Gets the name of this Currency.  # noqa: E501

        Currency name  # noqa: E501

        :return: The name of this Currency.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this Currency.

        Currency name  # noqa: E501

        :param name: The name of this Currency.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def delisted(self):
        """Gets the delisted of this Currency.  # noqa: E501

        Whether currency is de-listed  # noqa: E501

        :return: The delisted of this Currency.  # noqa: E501
        :rtype: bool
        """
        return self._delisted

    @delisted.setter
    def delisted(self, delisted):
        """Sets the delisted of this Currency.

        Whether currency is de-listed  # noqa: E501

        :param delisted: The delisted of this Currency.  # noqa: E501
        :type: bool
        """

        self._delisted = delisted

    @property
    def withdraw_disabled(self):
        """Gets the withdraw_disabled of this Currency.  # noqa: E501

        Whether currency's withdrawal is disabled (deprecated)  # noqa: E501

        :return: The withdraw_disabled of this Currency.  # noqa: E501
        :rtype: bool
        """
        return self._withdraw_disabled

    @withdraw_disabled.setter
    def withdraw_disabled(self, withdraw_disabled):
        """Sets the withdraw_disabled of this Currency.

        Whether currency's withdrawal is disabled (deprecated)  # noqa: E501

        :param withdraw_disabled: The withdraw_disabled of this Currency.  # noqa: E501
        :type: bool
        """

        self._withdraw_disabled = withdraw_disabled

    @property
    def withdraw_delayed(self):
        """Gets the withdraw_delayed of this Currency.  # noqa: E501

        Whether currency's withdrawal is delayed (deprecated)  # noqa: E501

        :return: The withdraw_delayed of this Currency.  # noqa: E501
        :rtype: bool
        """
        return self._withdraw_delayed

    @withdraw_delayed.setter
    def withdraw_delayed(self, withdraw_delayed):
        """Sets the withdraw_delayed of this Currency.

        Whether currency's withdrawal is delayed (deprecated)  # noqa: E501

        :param withdraw_delayed: The withdraw_delayed of this Currency.  # noqa: E501
        :type: bool
        """

        self._withdraw_delayed = withdraw_delayed

    @property
    def deposit_disabled(self):
        """Gets the deposit_disabled of this Currency.  # noqa: E501

        Whether currency's deposit is disabled (deprecated)  # noqa: E501

        :return: The deposit_disabled of this Currency.  # noqa: E501
        :rtype: bool
        """
        return self._deposit_disabled

    @deposit_disabled.setter
    def deposit_disabled(self, deposit_disabled):
        """Sets the deposit_disabled of this Currency.

        Whether currency's deposit is disabled (deprecated)  # noqa: E501

        :param deposit_disabled: The deposit_disabled of this Currency.  # noqa: E501
        :type: bool
        """

        self._deposit_disabled = deposit_disabled

    @property
    def trade_disabled(self):
        """Gets the trade_disabled of this Currency.  # noqa: E501

        Whether currency's trading is disabled  # noqa: E501

        :return: The trade_disabled of this Currency.  # noqa: E501
        :rtype: bool
        """
        return self._trade_disabled

    @trade_disabled.setter
    def trade_disabled(self, trade_disabled):
        """Sets the trade_disabled of this Currency.

        Whether currency's trading is disabled  # noqa: E501

        :param trade_disabled: The trade_disabled of this Currency.  # noqa: E501
        :type: bool
        """

        self._trade_disabled = trade_disabled

    @property
    def fixed_rate(self):
        """Gets the fixed_rate of this Currency.  # noqa: E501

        Fixed fee rate. Only for fixed rate currencies, not valid for normal currencies  # noqa: E501

        :return: The fixed_rate of this Currency.  # noqa: E501
        :rtype: str
        """
        return self._fixed_rate

    @fixed_rate.setter
    def fixed_rate(self, fixed_rate):
        """Sets the fixed_rate of this Currency.

        Fixed fee rate. Only for fixed rate currencies, not valid for normal currencies  # noqa: E501

        :param fixed_rate: The fixed_rate of this Currency.  # noqa: E501
        :type: str
        """

        self._fixed_rate = fixed_rate

    @property
    def chain(self):
        """Gets the chain of this Currency.  # noqa: E501

        The main chain corresponding to the coin  # noqa: E501

        :return: The chain of this Currency.  # noqa: E501
        :rtype: str
        """
        return self._chain

    @chain.setter
    def chain(self, chain):
        """Sets the chain of this Currency.

        The main chain corresponding to the coin  # noqa: E501

        :param chain: The chain of this Currency.  # noqa: E501
        :type: str
        """

        self._chain = chain

    @property
    def chains(self):
        """Gets the chains of this Currency.  # noqa: E501

        All links corresponding to coins  # noqa: E501

        :return: The chains of this Currency.  # noqa: E501
        :rtype: list[SpotCurrencyChain]
        """
        return self._chains

    @chains.setter
    def chains(self, chains):
        """Sets the chains of this Currency.

        All links corresponding to coins  # noqa: E501

        :param chains: The chains of this Currency.  # noqa: E501
        :type: list[SpotCurrencyChain]
        """

        self._chains = chains

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.openapi_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, Currency):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, Currency):
            return True

        return self.to_dict() != other.to_dict()
