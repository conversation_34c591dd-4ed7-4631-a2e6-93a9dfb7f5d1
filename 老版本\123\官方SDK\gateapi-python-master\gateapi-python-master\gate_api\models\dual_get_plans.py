# coding: utf-8

"""
    Gate API v4

    Welcome to Gate.io API  APIv4 provides spot, margin and futures trading operations. There are public APIs to retrieve the real-time market statistics, and private APIs which needs authentication to trade on user's behalf.  # noqa: E501

    Contact: <EMAIL>
    Generated by: https://openapi-generator.tech
"""


import pprint
import re  # noqa: F401

import six

from gate_api.configuration import Configuration


class DualGetPlans(object):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        'id': 'int',
        'instrument_name': 'str',
        'invest_currency': 'str',
        'exercise_currency': 'str',
        'exercise_price': 'float',
        'delivery_time': 'int',
        'min_copies': 'int',
        'max_copies': 'int',
        'per_value': 'str',
        'apy_display': 'str',
        'start_time': 'int',
        'end_time': 'int',
        'status': 'str'
    }

    attribute_map = {
        'id': 'id',
        'instrument_name': 'instrument_name',
        'invest_currency': 'invest_currency',
        'exercise_currency': 'exercise_currency',
        'exercise_price': 'exercise_price',
        'delivery_time': 'delivery_time',
        'min_copies': 'min_copies',
        'max_copies': 'max_copies',
        'per_value': 'per_value',
        'apy_display': 'apy_display',
        'start_time': 'start_time',
        'end_time': 'end_time',
        'status': 'status'
    }

    def __init__(self, id=None, instrument_name=None, invest_currency=None, exercise_currency=None, exercise_price=None, delivery_time=None, min_copies=None, max_copies=None, per_value=None, apy_display=None, start_time=None, end_time=None, status=None, local_vars_configuration=None):  # noqa: E501
        # type: (int, str, str, str, float, int, int, int, str, str, int, int, str, Configuration) -> None
        """DualGetPlans - a model defined in OpenAPI"""  # noqa: E501
        if local_vars_configuration is None:
            local_vars_configuration = Configuration()
        self.local_vars_configuration = local_vars_configuration

        self._id = None
        self._instrument_name = None
        self._invest_currency = None
        self._exercise_currency = None
        self._exercise_price = None
        self._delivery_time = None
        self._min_copies = None
        self._max_copies = None
        self._per_value = None
        self._apy_display = None
        self._start_time = None
        self._end_time = None
        self._status = None
        self.discriminator = None

        if id is not None:
            self.id = id
        if instrument_name is not None:
            self.instrument_name = instrument_name
        if invest_currency is not None:
            self.invest_currency = invest_currency
        if exercise_currency is not None:
            self.exercise_currency = exercise_currency
        if exercise_price is not None:
            self.exercise_price = exercise_price
        if delivery_time is not None:
            self.delivery_time = delivery_time
        if min_copies is not None:
            self.min_copies = min_copies
        if max_copies is not None:
            self.max_copies = max_copies
        if per_value is not None:
            self.per_value = per_value
        if apy_display is not None:
            self.apy_display = apy_display
        if start_time is not None:
            self.start_time = start_time
        if end_time is not None:
            self.end_time = end_time
        if status is not None:
            self.status = status

    @property
    def id(self):
        """Gets the id of this DualGetPlans.  # noqa: E501

        Plan ID  # noqa: E501

        :return: The id of this DualGetPlans.  # noqa: E501
        :rtype: int
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this DualGetPlans.

        Plan ID  # noqa: E501

        :param id: The id of this DualGetPlans.  # noqa: E501
        :type: int
        """

        self._id = id

    @property
    def instrument_name(self):
        """Gets the instrument_name of this DualGetPlans.  # noqa: E501

        Instrument Name  # noqa: E501

        :return: The instrument_name of this DualGetPlans.  # noqa: E501
        :rtype: str
        """
        return self._instrument_name

    @instrument_name.setter
    def instrument_name(self, instrument_name):
        """Sets the instrument_name of this DualGetPlans.

        Instrument Name  # noqa: E501

        :param instrument_name: The instrument_name of this DualGetPlans.  # noqa: E501
        :type: str
        """

        self._instrument_name = instrument_name

    @property
    def invest_currency(self):
        """Gets the invest_currency of this DualGetPlans.  # noqa: E501

        Investment Currency  # noqa: E501

        :return: The invest_currency of this DualGetPlans.  # noqa: E501
        :rtype: str
        """
        return self._invest_currency

    @invest_currency.setter
    def invest_currency(self, invest_currency):
        """Sets the invest_currency of this DualGetPlans.

        Investment Currency  # noqa: E501

        :param invest_currency: The invest_currency of this DualGetPlans.  # noqa: E501
        :type: str
        """

        self._invest_currency = invest_currency

    @property
    def exercise_currency(self):
        """Gets the exercise_currency of this DualGetPlans.  # noqa: E501

        Strike Currency  # noqa: E501

        :return: The exercise_currency of this DualGetPlans.  # noqa: E501
        :rtype: str
        """
        return self._exercise_currency

    @exercise_currency.setter
    def exercise_currency(self, exercise_currency):
        """Sets the exercise_currency of this DualGetPlans.

        Strike Currency  # noqa: E501

        :param exercise_currency: The exercise_currency of this DualGetPlans.  # noqa: E501
        :type: str
        """

        self._exercise_currency = exercise_currency

    @property
    def exercise_price(self):
        """Gets the exercise_price of this DualGetPlans.  # noqa: E501

        Strike price  # noqa: E501

        :return: The exercise_price of this DualGetPlans.  # noqa: E501
        :rtype: float
        """
        return self._exercise_price

    @exercise_price.setter
    def exercise_price(self, exercise_price):
        """Sets the exercise_price of this DualGetPlans.

        Strike price  # noqa: E501

        :param exercise_price: The exercise_price of this DualGetPlans.  # noqa: E501
        :type: float
        """

        self._exercise_price = exercise_price

    @property
    def delivery_time(self):
        """Gets the delivery_time of this DualGetPlans.  # noqa: E501

        Settlement time  # noqa: E501

        :return: The delivery_time of this DualGetPlans.  # noqa: E501
        :rtype: int
        """
        return self._delivery_time

    @delivery_time.setter
    def delivery_time(self, delivery_time):
        """Sets the delivery_time of this DualGetPlans.

        Settlement time  # noqa: E501

        :param delivery_time: The delivery_time of this DualGetPlans.  # noqa: E501
        :type: int
        """

        self._delivery_time = delivery_time

    @property
    def min_copies(self):
        """Gets the min_copies of this DualGetPlans.  # noqa: E501

        Minimum Copies  # noqa: E501

        :return: The min_copies of this DualGetPlans.  # noqa: E501
        :rtype: int
        """
        return self._min_copies

    @min_copies.setter
    def min_copies(self, min_copies):
        """Sets the min_copies of this DualGetPlans.

        Minimum Copies  # noqa: E501

        :param min_copies: The min_copies of this DualGetPlans.  # noqa: E501
        :type: int
        """

        self._min_copies = min_copies

    @property
    def max_copies(self):
        """Gets the max_copies of this DualGetPlans.  # noqa: E501

        Maximum Copies  # noqa: E501

        :return: The max_copies of this DualGetPlans.  # noqa: E501
        :rtype: int
        """
        return self._max_copies

    @max_copies.setter
    def max_copies(self, max_copies):
        """Sets the max_copies of this DualGetPlans.

        Maximum Copies  # noqa: E501

        :param max_copies: The max_copies of this DualGetPlans.  # noqa: E501
        :type: int
        """

        self._max_copies = max_copies

    @property
    def per_value(self):
        """Gets the per_value of this DualGetPlans.  # noqa: E501

        Per Unit Value  # noqa: E501

        :return: The per_value of this DualGetPlans.  # noqa: E501
        :rtype: str
        """
        return self._per_value

    @per_value.setter
    def per_value(self, per_value):
        """Sets the per_value of this DualGetPlans.

        Per Unit Value  # noqa: E501

        :param per_value: The per_value of this DualGetPlans.  # noqa: E501
        :type: str
        """

        self._per_value = per_value

    @property
    def apy_display(self):
        """Gets the apy_display of this DualGetPlans.  # noqa: E501

        APY  # noqa: E501

        :return: The apy_display of this DualGetPlans.  # noqa: E501
        :rtype: str
        """
        return self._apy_display

    @apy_display.setter
    def apy_display(self, apy_display):
        """Sets the apy_display of this DualGetPlans.

        APY  # noqa: E501

        :param apy_display: The apy_display of this DualGetPlans.  # noqa: E501
        :type: str
        """

        self._apy_display = apy_display

    @property
    def start_time(self):
        """Gets the start_time of this DualGetPlans.  # noqa: E501

        start time  # noqa: E501

        :return: The start_time of this DualGetPlans.  # noqa: E501
        :rtype: int
        """
        return self._start_time

    @start_time.setter
    def start_time(self, start_time):
        """Sets the start_time of this DualGetPlans.

        start time  # noqa: E501

        :param start_time: The start_time of this DualGetPlans.  # noqa: E501
        :type: int
        """

        self._start_time = start_time

    @property
    def end_time(self):
        """Gets the end_time of this DualGetPlans.  # noqa: E501

        Finished time  # noqa: E501

        :return: The end_time of this DualGetPlans.  # noqa: E501
        :rtype: int
        """
        return self._end_time

    @end_time.setter
    def end_time(self, end_time):
        """Sets the end_time of this DualGetPlans.

        Finished time  # noqa: E501

        :param end_time: The end_time of this DualGetPlans.  # noqa: E501
        :type: int
        """

        self._end_time = end_time

    @property
    def status(self):
        """Gets the status of this DualGetPlans.  # noqa: E501

        Status:   `NOTSTARTED`-not started  `ONGOING`-ongoing  `ENDED`-ended  # noqa: E501

        :return: The status of this DualGetPlans.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this DualGetPlans.

        Status:   `NOTSTARTED`-not started  `ONGOING`-ongoing  `ENDED`-ended  # noqa: E501

        :param status: The status of this DualGetPlans.  # noqa: E501
        :type: str
        """

        self._status = status

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.openapi_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DualGetPlans):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DualGetPlans):
            return True

        return self.to_dict() != other.to_dict()
