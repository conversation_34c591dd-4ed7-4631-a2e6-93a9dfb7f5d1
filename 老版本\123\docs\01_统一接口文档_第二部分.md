# 🔥 通用期货溢价套利系统 - 统一接口文档（第二部分：核心引擎和缓存）

## 🧠 3. 核心引擎接口

### 3.1 套利引擎 (ArbitrageEngine)

```python
# 文件位置: core/arbitrage_engine.py

class ArbitrageEngine:
    """套利引擎 - 核心控制器"""

    async def start(self) -> None:
        """启动套利系统 - 实际方法名"""
        pass

    async def stop(self) -> None:
        """停止套利系统 - 实际方法名"""
        pass

    async def update_balance_cache(self) -> None:
        """更新余额缓存 - 实际方法名"""
        pass
    
    def get_arbitrage_stats(self) -> Dict[str, Any]:
        """获取套利统计信息"""
        pass
```

### 3.2 执行引擎 (ExecutionEngine)

```python
# 文件位置: core/execution_engine.py

class ExecutionEngine:
    """执行引擎 - 极速执行逻辑"""

    async def execute_arbitrage(
        self,
        opportunity: ArbitrageOpportunity
    ) -> ExecutionResult:
        """
        执行套利交易 - 核心方法

        Args:
            opportunity: 套利机会对象

        Returns:
            ExecutionResult: 执行结果
        """
        pass

    async def _execute_parallel_trading(
        self,
        opportunity: ArbitrageOpportunity
    ) -> bool:
        """
        极速并行执行交易 - 内部方法

        Args:
            opportunity: 套利机会对象

        Returns:
            bool: 执行是否成功
        """
        pass
    
    async def _intelligent_amount_coordination(
        self,
        spot_amount_raw: float,
        futures_amount_raw: float,
        spot_exchange: str,
        futures_exchange: str,
        symbol: str,
        rules_preloader
    ) -> Tuple[Optional[float], Optional[float]]:
        """
        智能跨交易所数量协调
        
        Args:
            spot_amount_raw: 原始现货数量
            futures_amount_raw: 原始期货数量
            spot_exchange: 现货交易所
            futures_exchange: 期货交易所
            symbol: 交易对
            rules_preloader: 规则预加载器
            
        Returns:
            Tuple[Optional[float], Optional[float]]: (协调后现货数量, 协调后期货数量)
        """
        pass
    
    async def _pre_check_hedge_quality(
        self,
        spot_amount: float,
        futures_amount: float,
        spot_price: float,
        futures_price: float
    ) -> bool:
        """
        预检查对冲质量
        
        Args:
            spot_amount: 现货数量
            futures_amount: 期货数量
            spot_price: 现货价格
            futures_price: 期货价格
            
        Returns:
            bool: 对冲质量是否达标 (≥98%)
        """
        pass
```

## 🔄 4. 缓存系统接口

### 4.1 交易规则预加载器 (TradingRulesPreloader)

```python
# 文件位置: core/trading_rules_preloader.py

class TradingRulesPreloader:
    """交易规则预加载器 - 5大缓存系统核心（管理其中3个）"""

    def format_amount_unified(
        self,
        amount: float,
        exchange: str,
        symbol: str,
        market_type: str = "spot"
    ) -> str:
        """
        统一金额格式化接口
        
        Args:
            amount: 原始金额
            exchange: 交易所名称
            symbol: 交易对
            market_type: 市场类型
            
        Returns:
            str: 格式化后的金额字符串
        """
        pass
    
    def get_hedge_quality_cached(
        self,
        spot_exchange: str,
        futures_exchange: str,
        symbol: str,
        spot_amount: float,
        futures_amount: float,
        spot_price: float,
        futures_price: float
    ) -> Dict[str, Any]:
        """
        获取缓存的对冲质量
        
        Args:
            spot_exchange: 现货交易所
            futures_exchange: 期货交易所
            symbol: 交易对
            spot_amount: 现货数量
            futures_amount: 期货数量
            spot_price: 现货价格
            futures_price: 期货价格
            
        Returns:
            Dict[str, Any]: 对冲质量信息，包含hedge_ratio字段
        """
        pass
    
    def clear_cache(self, cache_type: str = "all") -> None:
        """
        清理缓存
        
        Args:
            cache_type: 缓存类型 ("all"/"precision"/"hedge"/"rules")
        """
        pass
```

## 📡 5. WebSocket接口

### 5.1 WebSocket管理器 (WsManager)

```python
# 文件位置: websocket/ws_manager.py

class WsManager:
    """WebSocket管理器 - 统一WebSocket连接管理"""
    
    async def start_all_connections(self) -> None:
        """启动所有WebSocket连接"""
        pass
    
    async def stop_all_connections(self) -> None:
        """停止所有WebSocket连接"""
        pass
    
    def get_connection_status(self) -> Dict[str, bool]:
        """
        获取连接状态
        
        Returns:
            Dict[str, bool]: 各交易所连接状态
            {
                "gate": True,
                "bybit": True,
                "okx": False
            }
        """
        pass
```

## 🛠️ 6. 工具类接口

### 6.1 货币适配器 (CurrencyAdapter)

```python
# 文件位置: exchanges/currency_adapter.py

class CurrencyAdapter:
    """货币适配器 - 统一符号转换"""
    
    def get_exchange_symbol(
        self,
        symbol: str,
        exchange_name: str,
        market_type: str = "spot"
    ) -> str:
        """
        获取交易所特定格式的交易对
        
        Args:
            symbol: 标准格式交易对 (BTC-USDT)
            exchange_name: 交易所名称
            market_type: 市场类型
            
        Returns:
            str: 交易所格式的交易对
        """
        pass
    
    def normalize_symbol(self, symbol: str) -> str:
        """
        标准化交易对格式
        
        Args:
            symbol: 任意格式的交易对
            
        Returns:
            str: 标准格式交易对 (BTC-USDT)
        """
        pass
    
    def extract_base_currency(self, symbol: str) -> str:
        """
        提取基础币种
        
        Args:
            symbol: 交易对
            
        Returns:
            str: 基础币种 (如: BTC)
        """
        pass
```

### 6.2 对冲计算器 (HedgeCalculator)

```python
# 文件位置: utils/hedge_calculator.py

class HedgeCalculator:
    """对冲计算器 - 精确对冲质量计算"""

    @staticmethod
    def calculate_hedge_quality(spot_value: float, futures_value: float) -> float:
        """计算对冲质量，返回0.0-1.0的分数"""
        pass

    @staticmethod
    def is_hedge_acceptable(hedge_quality: float, threshold: float = 0.98) -> bool:
        """检查对冲质量是否可接受"""
        return hedge_quality >= threshold
```

## 🎯 7. 接口使用示例

### 7.1 完整交易流程示例

```python
async def complete_trading_example():
    """完整交易流程示例"""

    # 1. 统一开仓
    opening_manager = UnifiedOpeningManager()
    spot_result = await opening_manager.unified_market_buy(
        symbol="BTC-USDT", quantity=0.001, exchange=gate_exchange, market_type="spot"
    )

    # 2. 统一平仓
    closing_manager = UnifiedClosingManager()
    close_result = await closing_manager.close_position_unified(
        symbol="BTC-USDT", exchange=gate_exchange, market_type="spot", side="sell"
    )
```

### 7.2 缓存系统使用示例

```python
def cache_usage_example():
    """缓存系统使用示例"""

    # 1. 格式化金额
    preloader = TradingRulesPreloader()
    formatted = preloader.format_amount_unified(
        amount=0.001234, exchange="gate", symbol="BTC-USDT", market_type="spot"
    )

    # 2. 获取对冲质量
    hedge_info = preloader.get_hedge_quality_cached(
        spot_exchange="gate", futures_exchange="bybit", symbol="BTC-USDT",
        spot_amount=0.001, futures_amount=0.001, spot_price=50000.0, futures_price=50100.0
    )
```

---

**📝 注意**: 本文档定义的接口为强制性规范，所有实现必须严格遵循这些标准。
