# coding: utf-8

"""
    Gate API v4

    Welcome to Gate.io API  APIv4 provides spot, margin and futures trading operations. There are public APIs to retrieve the real-time market statistics, and private APIs which needs authentication to trade on user's behalf.  # noqa: E501

    Contact: <EMAIL>
    Generated by: https://openapi-generator.tech
"""


import pprint
import re  # noqa: F401

import six

from gate_api.configuration import Configuration


class MockOptionsPosition(object):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        'options_name': 'str',
        'size': 'str'
    }

    attribute_map = {
        'options_name': 'options_name',
        'size': 'size'
    }

    def __init__(self, options_name=None, size=None, local_vars_configuration=None):  # noqa: E501
        # type: (str, str, Configuration) -> None
        """MockOptionsPosition - a model defined in OpenAPI"""  # noqa: E501
        if local_vars_configuration is None:
            local_vars_configuration = Configuration()
        self.local_vars_configuration = local_vars_configuration

        self._options_name = None
        self._size = None
        self.discriminator = None

        self.options_name = options_name
        self.size = size

    @property
    def options_name(self):
        """Gets the options_name of this MockOptionsPosition.  # noqa: E501

        Option name, currently only supports options for BTC and ETH with USDT.  # noqa: E501

        :return: The options_name of this MockOptionsPosition.  # noqa: E501
        :rtype: str
        """
        return self._options_name

    @options_name.setter
    def options_name(self, options_name):
        """Sets the options_name of this MockOptionsPosition.

        Option name, currently only supports options for BTC and ETH with USDT.  # noqa: E501

        :param options_name: The options_name of this MockOptionsPosition.  # noqa: E501
        :type: str
        """
        if self.local_vars_configuration.client_side_validation and options_name is None:  # noqa: E501
            raise ValueError("Invalid value for `options_name`, must not be `None`")  # noqa: E501

        self._options_name = options_name

    @property
    def size(self):
        """Gets the size of this MockOptionsPosition.  # noqa: E501

        Position size, measured in contract units.  # noqa: E501

        :return: The size of this MockOptionsPosition.  # noqa: E501
        :rtype: str
        """
        return self._size

    @size.setter
    def size(self, size):
        """Sets the size of this MockOptionsPosition.

        Position size, measured in contract units.  # noqa: E501

        :param size: The size of this MockOptionsPosition.  # noqa: E501
        :type: str
        """
        if self.local_vars_configuration.client_side_validation and size is None:  # noqa: E501
            raise ValueError("Invalid value for `size`, must not be `None`")  # noqa: E501

        self._size = size

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.openapi_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, MockOptionsPosition):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, MockOptionsPosition):
            return True

        return self.to_dict() != other.to_dict()
