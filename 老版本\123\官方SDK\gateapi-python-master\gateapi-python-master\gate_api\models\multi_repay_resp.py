# coding: utf-8

"""
    Gate API v4

    Welcome to Gate.io API  APIv4 provides spot, margin and futures trading operations. There are public APIs to retrieve the real-time market statistics, and private APIs which needs authentication to trade on user's behalf.  # noqa: E501

    Contact: <EMAIL>
    Generated by: https://openapi-generator.tech
"""


import pprint
import re  # noqa: F401

import six

from gate_api.configuration import Configuration


class MultiRepayResp(object):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        'order_id': 'int',
        'repaid_currencies': 'list[RepayCurrencyRes]'
    }

    attribute_map = {
        'order_id': 'order_id',
        'repaid_currencies': 'repaid_currencies'
    }

    def __init__(self, order_id=None, repaid_currencies=None, local_vars_configuration=None):  # noqa: E501
        # type: (int, list[RepayCurrencyRes], Configuration) -> None
        """MultiRepayResp - a model defined in OpenAPI"""  # noqa: E501
        if local_vars_configuration is None:
            local_vars_configuration = Configuration()
        self.local_vars_configuration = local_vars_configuration

        self._order_id = None
        self._repaid_currencies = None
        self.discriminator = None

        if order_id is not None:
            self.order_id = order_id
        if repaid_currencies is not None:
            self.repaid_currencies = repaid_currencies

    @property
    def order_id(self):
        """Gets the order_id of this MultiRepayResp.  # noqa: E501

        Order ID  # noqa: E501

        :return: The order_id of this MultiRepayResp.  # noqa: E501
        :rtype: int
        """
        return self._order_id

    @order_id.setter
    def order_id(self, order_id):
        """Sets the order_id of this MultiRepayResp.

        Order ID  # noqa: E501

        :param order_id: The order_id of this MultiRepayResp.  # noqa: E501
        :type: int
        """

        self._order_id = order_id

    @property
    def repaid_currencies(self):
        """Gets the repaid_currencies of this MultiRepayResp.  # noqa: E501

        Repay Currency List  # noqa: E501

        :return: The repaid_currencies of this MultiRepayResp.  # noqa: E501
        :rtype: list[RepayCurrencyRes]
        """
        return self._repaid_currencies

    @repaid_currencies.setter
    def repaid_currencies(self, repaid_currencies):
        """Sets the repaid_currencies of this MultiRepayResp.

        Repay Currency List  # noqa: E501

        :param repaid_currencies: The repaid_currencies of this MultiRepayResp.  # noqa: E501
        :type: list[RepayCurrencyRes]
        """

        self._repaid_currencies = repaid_currencies

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.openapi_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, MultiRepayResp):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, MultiRepayResp):
            return True

        return self.to_dict() != other.to_dict()
