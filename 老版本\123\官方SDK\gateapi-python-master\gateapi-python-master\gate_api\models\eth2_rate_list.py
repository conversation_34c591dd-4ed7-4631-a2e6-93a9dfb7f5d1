# coding: utf-8

"""
    Gate API v4

    Welcome to Gate.io API  APIv4 provides spot, margin and futures trading operations. There are public APIs to retrieve the real-time market statistics, and private APIs which needs authentication to trade on user's behalf.  # noqa: E501

    Contact: <EMAIL>
    Generated by: https://openapi-generator.tech
"""


import pprint
import re  # noqa: F401

import six

from gate_api.configuration import Configuration


class Eth2RateList(object):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        'date_time': 'int',
        'date': 'str',
        'rate': 'str'
    }

    attribute_map = {
        'date_time': 'date_time',
        'date': 'date',
        'rate': 'rate'
    }

    def __init__(self, date_time=None, date=None, rate=None, local_vars_configuration=None):  # noqa: E501
        # type: (int, str, str, Configuration) -> None
        """Eth2RateList - a model defined in OpenAPI"""  # noqa: E501
        if local_vars_configuration is None:
            local_vars_configuration = Configuration()
        self.local_vars_configuration = local_vars_configuration

        self._date_time = None
        self._date = None
        self._rate = None
        self.discriminator = None

        if date_time is not None:
            self.date_time = date_time
        if date is not None:
            self.date = date
        if rate is not None:
            self.rate = rate

    @property
    def date_time(self):
        """Gets the date_time of this Eth2RateList.  # noqa: E501

        Date and Time Stamp  # noqa: E501

        :return: The date_time of this Eth2RateList.  # noqa: E501
        :rtype: int
        """
        return self._date_time

    @date_time.setter
    def date_time(self, date_time):
        """Sets the date_time of this Eth2RateList.

        Date and Time Stamp  # noqa: E501

        :param date_time: The date_time of this Eth2RateList.  # noqa: E501
        :type: int
        """

        self._date_time = date_time

    @property
    def date(self):
        """Gets the date of this Eth2RateList.  # noqa: E501

        Date  # noqa: E501

        :return: The date of this Eth2RateList.  # noqa: E501
        :rtype: str
        """
        return self._date

    @date.setter
    def date(self, date):
        """Sets the date of this Eth2RateList.

        Date  # noqa: E501

        :param date: The date of this Eth2RateList.  # noqa: E501
        :type: str
        """

        self._date = date

    @property
    def rate(self):
        """Gets the rate of this Eth2RateList.  # noqa: E501

        percentage  # noqa: E501

        :return: The rate of this Eth2RateList.  # noqa: E501
        :rtype: str
        """
        return self._rate

    @rate.setter
    def rate(self, rate):
        """Sets the rate of this Eth2RateList.

        percentage  # noqa: E501

        :param rate: The rate of this Eth2RateList.  # noqa: E501
        :type: str
        """

        self._rate = rate

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.openapi_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, Eth2RateList):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, Eth2RateList):
            return True

        return self.to_dict() != other.to_dict()
