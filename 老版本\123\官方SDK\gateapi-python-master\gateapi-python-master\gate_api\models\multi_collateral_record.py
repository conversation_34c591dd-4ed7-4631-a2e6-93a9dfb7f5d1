# coding: utf-8

"""
    Gate API v4

    Welcome to Gate.io API  APIv4 provides spot, margin and futures trading operations. There are public APIs to retrieve the real-time market statistics, and private APIs which needs authentication to trade on user's behalf.  # noqa: E501

    Contact: <EMAIL>
    Generated by: https://openapi-generator.tech
"""


import pprint
import re  # noqa: F401

import six

from gate_api.configuration import Configuration


class MultiCollateralRecord(object):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        'order_id': 'int',
        'record_id': 'int',
        'before_ltv': 'str',
        'after_ltv': 'str',
        'operate_time': 'int',
        'borrow_currencies': 'list[MultiCollateralRecordCurrency]',
        'collateral_currencies': 'list[MultiCollateralRecordCurrency]'
    }

    attribute_map = {
        'order_id': 'order_id',
        'record_id': 'record_id',
        'before_ltv': 'before_ltv',
        'after_ltv': 'after_ltv',
        'operate_time': 'operate_time',
        'borrow_currencies': 'borrow_currencies',
        'collateral_currencies': 'collateral_currencies'
    }

    def __init__(self, order_id=None, record_id=None, before_ltv=None, after_ltv=None, operate_time=None, borrow_currencies=None, collateral_currencies=None, local_vars_configuration=None):  # noqa: E501
        # type: (int, int, str, str, int, list[MultiCollateralRecordCurrency], list[MultiCollateralRecordCurrency], Configuration) -> None
        """MultiCollateralRecord - a model defined in OpenAPI"""  # noqa: E501
        if local_vars_configuration is None:
            local_vars_configuration = Configuration()
        self.local_vars_configuration = local_vars_configuration

        self._order_id = None
        self._record_id = None
        self._before_ltv = None
        self._after_ltv = None
        self._operate_time = None
        self._borrow_currencies = None
        self._collateral_currencies = None
        self.discriminator = None

        if order_id is not None:
            self.order_id = order_id
        if record_id is not None:
            self.record_id = record_id
        if before_ltv is not None:
            self.before_ltv = before_ltv
        if after_ltv is not None:
            self.after_ltv = after_ltv
        if operate_time is not None:
            self.operate_time = operate_time
        if borrow_currencies is not None:
            self.borrow_currencies = borrow_currencies
        if collateral_currencies is not None:
            self.collateral_currencies = collateral_currencies

    @property
    def order_id(self):
        """Gets the order_id of this MultiCollateralRecord.  # noqa: E501

        Order ID  # noqa: E501

        :return: The order_id of this MultiCollateralRecord.  # noqa: E501
        :rtype: int
        """
        return self._order_id

    @order_id.setter
    def order_id(self, order_id):
        """Sets the order_id of this MultiCollateralRecord.

        Order ID  # noqa: E501

        :param order_id: The order_id of this MultiCollateralRecord.  # noqa: E501
        :type: int
        """

        self._order_id = order_id

    @property
    def record_id(self):
        """Gets the record_id of this MultiCollateralRecord.  # noqa: E501

        Collateral record ID  # noqa: E501

        :return: The record_id of this MultiCollateralRecord.  # noqa: E501
        :rtype: int
        """
        return self._record_id

    @record_id.setter
    def record_id(self, record_id):
        """Sets the record_id of this MultiCollateralRecord.

        Collateral record ID  # noqa: E501

        :param record_id: The record_id of this MultiCollateralRecord.  # noqa: E501
        :type: int
        """

        self._record_id = record_id

    @property
    def before_ltv(self):
        """Gets the before_ltv of this MultiCollateralRecord.  # noqa: E501

        The collateral ratio before adjustment  # noqa: E501

        :return: The before_ltv of this MultiCollateralRecord.  # noqa: E501
        :rtype: str
        """
        return self._before_ltv

    @before_ltv.setter
    def before_ltv(self, before_ltv):
        """Sets the before_ltv of this MultiCollateralRecord.

        The collateral ratio before adjustment  # noqa: E501

        :param before_ltv: The before_ltv of this MultiCollateralRecord.  # noqa: E501
        :type: str
        """

        self._before_ltv = before_ltv

    @property
    def after_ltv(self):
        """Gets the after_ltv of this MultiCollateralRecord.  # noqa: E501

        The collateral ratio before adjustment  # noqa: E501

        :return: The after_ltv of this MultiCollateralRecord.  # noqa: E501
        :rtype: str
        """
        return self._after_ltv

    @after_ltv.setter
    def after_ltv(self, after_ltv):
        """Sets the after_ltv of this MultiCollateralRecord.

        The collateral ratio before adjustment  # noqa: E501

        :param after_ltv: The after_ltv of this MultiCollateralRecord.  # noqa: E501
        :type: str
        """

        self._after_ltv = after_ltv

    @property
    def operate_time(self):
        """Gets the operate_time of this MultiCollateralRecord.  # noqa: E501

        Operation time, timestamp in seconds.  # noqa: E501

        :return: The operate_time of this MultiCollateralRecord.  # noqa: E501
        :rtype: int
        """
        return self._operate_time

    @operate_time.setter
    def operate_time(self, operate_time):
        """Sets the operate_time of this MultiCollateralRecord.

        Operation time, timestamp in seconds.  # noqa: E501

        :param operate_time: The operate_time of this MultiCollateralRecord.  # noqa: E501
        :type: int
        """

        self._operate_time = operate_time

    @property
    def borrow_currencies(self):
        """Gets the borrow_currencies of this MultiCollateralRecord.  # noqa: E501

        Borrowing Currency List  # noqa: E501

        :return: The borrow_currencies of this MultiCollateralRecord.  # noqa: E501
        :rtype: list[MultiCollateralRecordCurrency]
        """
        return self._borrow_currencies

    @borrow_currencies.setter
    def borrow_currencies(self, borrow_currencies):
        """Sets the borrow_currencies of this MultiCollateralRecord.

        Borrowing Currency List  # noqa: E501

        :param borrow_currencies: The borrow_currencies of this MultiCollateralRecord.  # noqa: E501
        :type: list[MultiCollateralRecordCurrency]
        """

        self._borrow_currencies = borrow_currencies

    @property
    def collateral_currencies(self):
        """Gets the collateral_currencies of this MultiCollateralRecord.  # noqa: E501

        Collateral Currency List  # noqa: E501

        :return: The collateral_currencies of this MultiCollateralRecord.  # noqa: E501
        :rtype: list[MultiCollateralRecordCurrency]
        """
        return self._collateral_currencies

    @collateral_currencies.setter
    def collateral_currencies(self, collateral_currencies):
        """Sets the collateral_currencies of this MultiCollateralRecord.

        Collateral Currency List  # noqa: E501

        :param collateral_currencies: The collateral_currencies of this MultiCollateralRecord.  # noqa: E501
        :type: list[MultiCollateralRecordCurrency]
        """

        self._collateral_currencies = collateral_currencies

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.openapi_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, MultiCollateralRecord):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, MultiCollateralRecord):
            return True

        return self.to_dict() != other.to_dict()
