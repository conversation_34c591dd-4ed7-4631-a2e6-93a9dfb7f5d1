# coding: utf-8

"""
    Gate API v4

    Welcome to Gate.io API  APIv4 provides spot, margin and futures trading operations. There are public APIs to retrieve the real-time market statistics, and private APIs which needs authentication to trade on user's behalf.  # noqa: E501

    Contact: <EMAIL>
    Generated by: https://openapi-generator.tech
"""


import pprint
import re  # noqa: F401

import six

from gate_api.configuration import Configuration


class UnifiedPortfolioInput(object):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        'spot_balances': 'list[MockSpotBalance]',
        'spot_orders': 'list[MockSpotOrder]',
        'futures_positions': 'list[MockFuturesPosition]',
        'futures_orders': 'list[MockFuturesOrder]',
        'options_positions': 'list[MockOptionsPosition]',
        'options_orders': 'list[MockOptionsOrder]',
        'spot_hedge': 'bool'
    }

    attribute_map = {
        'spot_balances': 'spot_balances',
        'spot_orders': 'spot_orders',
        'futures_positions': 'futures_positions',
        'futures_orders': 'futures_orders',
        'options_positions': 'options_positions',
        'options_orders': 'options_orders',
        'spot_hedge': 'spot_hedge'
    }

    def __init__(self, spot_balances=None, spot_orders=None, futures_positions=None, futures_orders=None, options_positions=None, options_orders=None, spot_hedge=None, local_vars_configuration=None):  # noqa: E501
        # type: (list[MockSpotBalance], list[MockSpotOrder], list[MockFuturesPosition], list[MockFuturesOrder], list[MockOptionsPosition], list[MockOptionsOrder], bool, Configuration) -> None
        """UnifiedPortfolioInput - a model defined in OpenAPI"""  # noqa: E501
        if local_vars_configuration is None:
            local_vars_configuration = Configuration()
        self.local_vars_configuration = local_vars_configuration

        self._spot_balances = None
        self._spot_orders = None
        self._futures_positions = None
        self._futures_orders = None
        self._options_positions = None
        self._options_orders = None
        self._spot_hedge = None
        self.discriminator = None

        if spot_balances is not None:
            self.spot_balances = spot_balances
        if spot_orders is not None:
            self.spot_orders = spot_orders
        if futures_positions is not None:
            self.futures_positions = futures_positions
        if futures_orders is not None:
            self.futures_orders = futures_orders
        if options_positions is not None:
            self.options_positions = options_positions
        if options_orders is not None:
            self.options_orders = options_orders
        if spot_hedge is not None:
            self.spot_hedge = spot_hedge

    @property
    def spot_balances(self):
        """Gets the spot_balances of this UnifiedPortfolioInput.  # noqa: E501

        Spot  # noqa: E501

        :return: The spot_balances of this UnifiedPortfolioInput.  # noqa: E501
        :rtype: list[MockSpotBalance]
        """
        return self._spot_balances

    @spot_balances.setter
    def spot_balances(self, spot_balances):
        """Sets the spot_balances of this UnifiedPortfolioInput.

        Spot  # noqa: E501

        :param spot_balances: The spot_balances of this UnifiedPortfolioInput.  # noqa: E501
        :type: list[MockSpotBalance]
        """

        self._spot_balances = spot_balances

    @property
    def spot_orders(self):
        """Gets the spot_orders of this UnifiedPortfolioInput.  # noqa: E501

        Spot orders  # noqa: E501

        :return: The spot_orders of this UnifiedPortfolioInput.  # noqa: E501
        :rtype: list[MockSpotOrder]
        """
        return self._spot_orders

    @spot_orders.setter
    def spot_orders(self, spot_orders):
        """Sets the spot_orders of this UnifiedPortfolioInput.

        Spot orders  # noqa: E501

        :param spot_orders: The spot_orders of this UnifiedPortfolioInput.  # noqa: E501
        :type: list[MockSpotOrder]
        """

        self._spot_orders = spot_orders

    @property
    def futures_positions(self):
        """Gets the futures_positions of this UnifiedPortfolioInput.  # noqa: E501

        Futures positions  # noqa: E501

        :return: The futures_positions of this UnifiedPortfolioInput.  # noqa: E501
        :rtype: list[MockFuturesPosition]
        """
        return self._futures_positions

    @futures_positions.setter
    def futures_positions(self, futures_positions):
        """Sets the futures_positions of this UnifiedPortfolioInput.

        Futures positions  # noqa: E501

        :param futures_positions: The futures_positions of this UnifiedPortfolioInput.  # noqa: E501
        :type: list[MockFuturesPosition]
        """

        self._futures_positions = futures_positions

    @property
    def futures_orders(self):
        """Gets the futures_orders of this UnifiedPortfolioInput.  # noqa: E501

        Futures order  # noqa: E501

        :return: The futures_orders of this UnifiedPortfolioInput.  # noqa: E501
        :rtype: list[MockFuturesOrder]
        """
        return self._futures_orders

    @futures_orders.setter
    def futures_orders(self, futures_orders):
        """Sets the futures_orders of this UnifiedPortfolioInput.

        Futures order  # noqa: E501

        :param futures_orders: The futures_orders of this UnifiedPortfolioInput.  # noqa: E501
        :type: list[MockFuturesOrder]
        """

        self._futures_orders = futures_orders

    @property
    def options_positions(self):
        """Gets the options_positions of this UnifiedPortfolioInput.  # noqa: E501

        Options positions  # noqa: E501

        :return: The options_positions of this UnifiedPortfolioInput.  # noqa: E501
        :rtype: list[MockOptionsPosition]
        """
        return self._options_positions

    @options_positions.setter
    def options_positions(self, options_positions):
        """Sets the options_positions of this UnifiedPortfolioInput.

        Options positions  # noqa: E501

        :param options_positions: The options_positions of this UnifiedPortfolioInput.  # noqa: E501
        :type: list[MockOptionsPosition]
        """

        self._options_positions = options_positions

    @property
    def options_orders(self):
        """Gets the options_orders of this UnifiedPortfolioInput.  # noqa: E501

        Option orders  # noqa: E501

        :return: The options_orders of this UnifiedPortfolioInput.  # noqa: E501
        :rtype: list[MockOptionsOrder]
        """
        return self._options_orders

    @options_orders.setter
    def options_orders(self, options_orders):
        """Sets the options_orders of this UnifiedPortfolioInput.

        Option orders  # noqa: E501

        :param options_orders: The options_orders of this UnifiedPortfolioInput.  # noqa: E501
        :type: list[MockOptionsOrder]
        """

        self._options_orders = options_orders

    @property
    def spot_hedge(self):
        """Gets the spot_hedge of this UnifiedPortfolioInput.  # noqa: E501

        Whether to enable spot hedging.  # noqa: E501

        :return: The spot_hedge of this UnifiedPortfolioInput.  # noqa: E501
        :rtype: bool
        """
        return self._spot_hedge

    @spot_hedge.setter
    def spot_hedge(self, spot_hedge):
        """Sets the spot_hedge of this UnifiedPortfolioInput.

        Whether to enable spot hedging.  # noqa: E501

        :param spot_hedge: The spot_hedge of this UnifiedPortfolioInput.  # noqa: E501
        :type: bool
        """

        self._spot_hedge = spot_hedge

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.openapi_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, UnifiedPortfolioInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, UnifiedPortfolioInput):
            return True

        return self.to_dict() != other.to_dict()
