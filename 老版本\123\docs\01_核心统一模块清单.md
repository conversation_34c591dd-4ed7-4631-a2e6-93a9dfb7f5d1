# 核心统一模块清单

## 🔥 **系统核心定位**

### **⚠️ 重要说明：期货溢价专业套利系统**

**本系统是通用的专注期货溢价执行套利，现货溢价平仓的套利系统，支持任何代币！**

#### **🎯 核心套利逻辑**
```
期货溢价阈值达到 → 开仓锁定差价 → 等待趋同 → 现货溢价阈值达到 → 平仓获利
```

#### **📊 价差符号含义说明**
- **+0.25%** = 期货溢价差价0.25%（期货 > 现货）→ 开仓信号，锁定差价
- **-0.3%** = 现货溢价差价0.3%（现货 > 期货）→ 平仓信号，释放利润
- **+/-符号** = 表示差价类型，不是正负值概念
- **实时日志记录** → 所有价差都显示，不受阈值限制
- **阈值控制执行** → 决定是否开仓/平仓，不影响日志
- **总利润** = |期货溢价差价| + |现货溢价差价| = 0.25% + 0.3% = 0.55%

---

## 📊 **30个统一模块**

### **Core目录 (16个):**
1. **arbitrage_engine** - 套利引擎 ✅
2. **execution_engine** - 执行引擎 ✅ (已修复execution_context支持)
3. **opportunity_scanner** - 机会扫描器 ✅ (已集成Order差价计算)
4. **trading_rules_preloader** - 交易规则预加载器 ✅
5. **universal_token_system** - 通用代币系统 ✅
6. **unified_opening_manager** - 统一开仓管理器 ✅
7. **unified_closing_manager** - 统一平仓管理器 ✅
8. **unified_balance_manager** - 统一余额管理器 ✅
9. **unified_depth_analyzer** - 统一深度分析器 ✅
10. **convergence_monitor** - 价差趋同监控器 ✅ (已更新Order差价计算)
11. **execution_params_preparer** - 执行参数准备器 ✅
12. **order_pairing_manager** - 订单配对管理器 ✅
13. **trading_system_initializer** - 交易系统初始化器 ✅
14. **unified_http_session_manager** - 统一HTTP会话管理器 ✅
15. **unified_leverage_manager** - 统一杠杆管理器 ✅
16. **🔥 unified_order_spread_calculator** - 统一Order差价计算器 ✅ (新增核心模块)

### **Config目录 (1个):**
17. **unified_network_config_manager** - 统一网络配置管理器 ✅

### **Exchanges目录 (4个):**
18. **exchanges_base** - 交易所基类 ✅
19. **gate_exchange** - Gate.io交易所 ✅
20. **bybit_exchange** - Bybit交易所 ✅
21. **okx_exchange** - OKX交易所 ✅

### **WebSocket目录 (3个):**
22. **orderbook_validator** - 统一订单簿验证器 ✅
23. **unified_data_formatter** - 统一数据格式化器 ✅
24. **unified_timestamp_processor** - 统一时间戳处理器 ✅

### **Utils目录 (4个):**
25. **cache_monitor** - 统一缓存监控系统 ✅
26. **min_order_detector** - 动态最小金额检测器 ✅
27. **hedge_calculator** - 对冲计算器 ✅
28. **margin_calculator** - 保证金计算器 ✅

### **Trading目录 (2个):**
29. **spot_trader** - 现货交易器 ✅
30. **futures_trader** - 期货交易器 ✅

---

## 核心模块详情

### **unified_depth_analyzer.py**
**职责**: 统一深度分析

```python
class UnifiedDepthAnalyzer:
    def analyze_orderbook_depth(self, orderbook, required_amount, side, reference_price, exchange, symbol) -> DepthAnalysisResult
    def analyze_dual_orderbook_depth(self, spot_orderbook, futures_orderbook, spot_amount, futures_amount, spot_price, futures_price) -> Tuple[DepthAnalysisResult, DepthAnalysisResult]

def get_depth_analyzer() -> UnifiedDepthAnalyzer
```

### **unified_balance_manager.py**
**职责**: 统一余额管理

```python
class UnifiedBalanceManager:
    async def get_all_balances(self) -> Dict[str, Dict[str, float]]
    def get_exchange_balance(self, exchange_name: str, account_type: str) -> float
    async def refresh_balance(self, exchange_name: str, account_type: str = None) -> bool

def get_unified_balance_manager(exchanges: Dict) -> UnifiedBalanceManager
```

### **5大缓存系统 (从.env配置TTL)**
1. **余额缓存**: UnifiedBalanceManager管理 (BALANCE_CACHE_TTL，默认30秒)
2. **保证金缓存**: MarginCalculator管理 (MARGIN_CACHE_TTL，默认300秒)
3. **交易规则缓存**: TradingRulesPreloader管理 (TRADING_RULES_TTL，默认86400秒)
4. **对冲质量缓存**: TradingRulesPreloader管理 (HEDGE_QUALITY_TTL，默认10秒)
5. **精度缓存**: TradingRulesPreloader管理 (PRECISION_CACHE_TTL，默认3600秒)
