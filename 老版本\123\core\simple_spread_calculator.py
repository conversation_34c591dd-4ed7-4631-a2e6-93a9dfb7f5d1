#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔥 简单差价计算器
用于基础的现货期货差价计算，不依赖订单簿数据

主要用途：
1. 测试和验证
2. 快速差价计算
3. 日志记录和监控
"""

import logging
from typing import Dict, Any, Optional
from decimal import Decimal, ROUND_HALF_UP

class SimpleSpreadCalculator:
    """简单差价计算器 - 基于价格的基础计算"""
    
    def __init__(self, spot_exchange: str, futures_exchange: str):
        """
        初始化简单差价计算器
        
        Args:
            spot_exchange: 现货交易所名称
            futures_exchange: 期货交易所名称
        """
        self.spot_exchange = spot_exchange
        self.futures_exchange = futures_exchange
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
    
    def calculate_spread(self, spot_price: float, futures_price: float) -> Dict[str, Any]:
        """
        计算差价和溢价方向
        
        Args:
            spot_price: 现货价格
            futures_price: 期货价格
            
        Returns:
            Dict包含:
            - spread_percent: 差价百分比
            - premium_type: 溢价类型 ("现货溢价" | "期货溢价")
            - action: 建议操作
            - spot_price: 现货价格
            - futures_price: 期货价格
            - spread_value: 绝对差价
        """
        try:
            # 输入验证
            if spot_price <= 0 or futures_price <= 0:
                return {"error": "价格必须大于0"}
            
            if not isinstance(spot_price, (int, float)) or not isinstance(futures_price, (int, float)):
                return {"error": "价格必须是数字类型"}
            
            # 🔥 高精度计算，避免浮点数误差
            spot_decimal = Decimal(str(spot_price))
            futures_decimal = Decimal(str(futures_price))
            
            # 计算绝对差价
            spread_value = float(futures_decimal - spot_decimal)
            
            # 🔥 统一差价计算公式
            # 使用较小的价格作为分母，确保计算稳定性
            base_price = min(spot_decimal, futures_decimal)
            if base_price <= 0:
                return {"error": "基准价格无效"}

            # 🔥 修复：计算带符号的差价百分比，保持精度
            spread_decimal = (futures_decimal - spot_decimal) / base_price
            spread_percent = float(spread_decimal.quantize(Decimal('0.00000001'), rounding=ROUND_HALF_UP))
            
            # 🔥 修复：基于计算出的带符号差价判断溢价方向
            if spread_percent > 0:
                # 期货溢价：期货价格 > 现货价格
                premium_type = "期货溢价"
                action = f"买{self.spot_exchange}现货+卖{self.futures_exchange}期货"
                signed_spread_percent = spread_percent
            elif spread_percent < 0:
                # 现货溢价：现货价格 > 期货价格
                premium_type = "现货溢价"
                action = f"卖{self.spot_exchange}现货+买{self.futures_exchange}期货"
                signed_spread_percent = spread_percent  # 已经是负值
            else:
                # 无差价
                premium_type = "无差价"
                action = "无套利机会"
                signed_spread_percent = 0.0
            
            return {
                "spread_percent": signed_spread_percent,
                "spread_percent_abs": abs(spread_percent),  # 绝对值差价
                "premium_type": premium_type,
                "action": action,
                "spot_price": spot_price,
                "futures_price": futures_price,
                "spread_value": spread_value,
                "spot_exchange": self.spot_exchange,
                "futures_exchange": self.futures_exchange
            }
            
        except Exception as e:
            self.logger.error(f"差价计算异常: {e}")
            return {"error": f"计算失败: {str(e)}"}
    
    def calculate_spread_with_direction(self, spot_price: float, futures_price: float, 
                                      expected_direction: str = "auto") -> Dict[str, Any]:
        """
        带方向的差价计算
        
        Args:
            spot_price: 现货价格
            futures_price: 期货价格
            expected_direction: 期望方向 ("futures_premium" | "spot_premium" | "auto")
            
        Returns:
            差价计算结果，包含方向匹配信息
        """
        result = self.calculate_spread(spot_price, futures_price)
        
        if "error" in result:
            return result
        
        # 添加方向匹配信息
        actual_direction = "futures_premium" if result["spread_percent"] > 0 else "spot_premium"
        
        result["expected_direction"] = expected_direction
        result["actual_direction"] = actual_direction
        result["direction_match"] = (expected_direction == "auto" or 
                                   expected_direction == actual_direction)
        
        return result
    
    def is_arbitrage_opportunity(self, spot_price: float, futures_price: float, 
                               min_spread_threshold: float = 0.001) -> bool:
        """
        判断是否存在套利机会
        
        Args:
            spot_price: 现货价格
            futures_price: 期货价格
            min_spread_threshold: 最小差价阈值
            
        Returns:
            bool: 是否存在套利机会
        """
        result = self.calculate_spread(spot_price, futures_price)
        
        if "error" in result:
            return False
        
        return abs(result["spread_percent"]) >= min_spread_threshold
    
    def get_execution_prices(self, spot_price: float, futures_price: float, 
                           execution_context: str = "opening") -> Dict[str, float]:
        """
        获取执行价格（简化版，用于测试）
        
        Args:
            spot_price: 现货价格
            futures_price: 期货价格
            execution_context: 执行上下文 ("opening" | "closing")
            
        Returns:
            Dict包含执行价格信息
        """
        if execution_context == "opening":
            # 开仓：买现货（asks价格），卖期货（bids价格）
            # 简化处理：假设有微小滑点
            spot_execution_price = spot_price * 1.0001  # 0.01%滑点
            futures_execution_price = futures_price * 0.9999  # 0.01%滑点
        else:
            # 平仓：卖现货（bids价格），买期货（asks价格）
            spot_execution_price = spot_price * 0.9999  # 0.01%滑点
            futures_execution_price = futures_price * 1.0001  # 0.01%滑点
        
        return {
            "spot_execution_price": spot_execution_price,
            "futures_execution_price": futures_execution_price,
            "execution_context": execution_context
        }


# 🌟 全局实例缓存
_calculator_cache = {}

def get_simple_spread_calculator(spot_exchange: str, futures_exchange: str) -> SimpleSpreadCalculator:
    """
    获取简单差价计算器实例（带缓存）
    
    Args:
        spot_exchange: 现货交易所
        futures_exchange: 期货交易所
        
    Returns:
        SimpleSpreadCalculator实例
    """
    cache_key = f"{spot_exchange}_{futures_exchange}"
    
    if cache_key not in _calculator_cache:
        _calculator_cache[cache_key] = SimpleSpreadCalculator(spot_exchange, futures_exchange)
    
    return _calculator_cache[cache_key]

def calculate_simple_spread(spot_price: float, futures_price: float, 
                          spot_exchange: str = "SPOT", futures_exchange: str = "FUTURES") -> Dict[str, Any]:
    """
    快速差价计算函数
    
    Args:
        spot_price: 现货价格
        futures_price: 期货价格
        spot_exchange: 现货交易所名称
        futures_exchange: 期货交易所名称
        
    Returns:
        差价计算结果
    """
    calculator = get_simple_spread_calculator(spot_exchange, futures_exchange)
    return calculator.calculate_spread(spot_price, futures_price)

# 🔥 兼容性别名 - 用于测试
class UnifiedOrderSpreadCalculator:
    """兼容性类 - 用于测试中的接口统一"""
    
    def __init__(self, spot_exchange: str, futures_exchange: str):
        self.calculator = SimpleSpreadCalculator(spot_exchange, futures_exchange)
    
    def calculate_spread(self, spot_price: float, futures_price: float) -> Dict[str, Any]:
        """兼容性方法"""
        return self.calculator.calculate_spread(spot_price, futures_price)
