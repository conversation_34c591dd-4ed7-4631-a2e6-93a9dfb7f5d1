# coding: utf-8

"""
    Gate API v4

    Welcome to Gate.io API  APIv4 provides spot, margin and futures trading operations. There are public APIs to retrieve the real-time market statistics, and private APIs which needs authentication to trade on user's behalf.  # noqa: E501

    Contact: <EMAIL>
    Generated by: https://openapi-generator.tech
"""


import pprint
import re  # noqa: F401

import six

from gate_api.configuration import Configuration


class InlineResponse200(object):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        'time': 'int',
        'vaule': 'str'
    }

    attribute_map = {
        'time': 'time',
        'vaule': 'vaule'
    }

    def __init__(self, time=None, vaule=None, local_vars_configuration=None):  # noqa: E501
        # type: (int, str, Configuration) -> None
        """InlineResponse200 - a model defined in OpenAPI"""  # noqa: E501
        if local_vars_configuration is None:
            local_vars_configuration = Configuration()
        self.local_vars_configuration = local_vars_configuration

        self._time = None
        self._vaule = None
        self.discriminator = None

        if time is not None:
            self.time = time
        if vaule is not None:
            self.vaule = vaule

    @property
    def time(self):
        """Gets the time of this InlineResponse200.  # noqa: E501


        :return: The time of this InlineResponse200.  # noqa: E501
        :rtype: int
        """
        return self._time

    @time.setter
    def time(self, time):
        """Sets the time of this InlineResponse200.


        :param time: The time of this InlineResponse200.  # noqa: E501
        :type: int
        """

        self._time = time

    @property
    def vaule(self):
        """Gets the vaule of this InlineResponse200.  # noqa: E501


        :return: The vaule of this InlineResponse200.  # noqa: E501
        :rtype: str
        """
        return self._vaule

    @vaule.setter
    def vaule(self, vaule):
        """Sets the vaule of this InlineResponse200.


        :param vaule: The vaule of this InlineResponse200.  # noqa: E501
        :type: str
        """

        self._vaule = vaule

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.openapi_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, InlineResponse200):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, InlineResponse200):
            return True

        return self.to_dict() != other.to_dict()
