# coding: utf-8

"""
    Gate API v4

    Welcome to Gate.io API  APIv4 provides spot, margin and futures trading operations. There are public APIs to retrieve the real-time market statistics, and private APIs which needs authentication to trade on user's behalf.  # noqa: E501

    Contact: <EMAIL>
    Generated by: https://openapi-generator.tech
"""


import pprint
import re  # noqa: F401

import six

from gate_api.configuration import Configuration


class CreateCollateralOrder(object):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        'collateral_amount': 'str',
        'collateral_currency': 'str',
        'borrow_amount': 'str',
        'borrow_currency': 'str'
    }

    attribute_map = {
        'collateral_amount': 'collateral_amount',
        'collateral_currency': 'collateral_currency',
        'borrow_amount': 'borrow_amount',
        'borrow_currency': 'borrow_currency'
    }

    def __init__(self, collateral_amount=None, collateral_currency=None, borrow_amount=None, borrow_currency=None, local_vars_configuration=None):  # noqa: E501
        # type: (str, str, str, str, Configuration) -> None
        """CreateCollateralOrder - a model defined in OpenAPI"""  # noqa: E501
        if local_vars_configuration is None:
            local_vars_configuration = Configuration()
        self.local_vars_configuration = local_vars_configuration

        self._collateral_amount = None
        self._collateral_currency = None
        self._borrow_amount = None
        self._borrow_currency = None
        self.discriminator = None

        self.collateral_amount = collateral_amount
        self.collateral_currency = collateral_currency
        self.borrow_amount = borrow_amount
        self.borrow_currency = borrow_currency

    @property
    def collateral_amount(self):
        """Gets the collateral_amount of this CreateCollateralOrder.  # noqa: E501

        Collateral amount  # noqa: E501

        :return: The collateral_amount of this CreateCollateralOrder.  # noqa: E501
        :rtype: str
        """
        return self._collateral_amount

    @collateral_amount.setter
    def collateral_amount(self, collateral_amount):
        """Sets the collateral_amount of this CreateCollateralOrder.

        Collateral amount  # noqa: E501

        :param collateral_amount: The collateral_amount of this CreateCollateralOrder.  # noqa: E501
        :type: str
        """
        if self.local_vars_configuration.client_side_validation and collateral_amount is None:  # noqa: E501
            raise ValueError("Invalid value for `collateral_amount`, must not be `None`")  # noqa: E501

        self._collateral_amount = collateral_amount

    @property
    def collateral_currency(self):
        """Gets the collateral_currency of this CreateCollateralOrder.  # noqa: E501

        Collateral  # noqa: E501

        :return: The collateral_currency of this CreateCollateralOrder.  # noqa: E501
        :rtype: str
        """
        return self._collateral_currency

    @collateral_currency.setter
    def collateral_currency(self, collateral_currency):
        """Sets the collateral_currency of this CreateCollateralOrder.

        Collateral  # noqa: E501

        :param collateral_currency: The collateral_currency of this CreateCollateralOrder.  # noqa: E501
        :type: str
        """
        if self.local_vars_configuration.client_side_validation and collateral_currency is None:  # noqa: E501
            raise ValueError("Invalid value for `collateral_currency`, must not be `None`")  # noqa: E501

        self._collateral_currency = collateral_currency

    @property
    def borrow_amount(self):
        """Gets the borrow_amount of this CreateCollateralOrder.  # noqa: E501

        Borrowing amount  # noqa: E501

        :return: The borrow_amount of this CreateCollateralOrder.  # noqa: E501
        :rtype: str
        """
        return self._borrow_amount

    @borrow_amount.setter
    def borrow_amount(self, borrow_amount):
        """Sets the borrow_amount of this CreateCollateralOrder.

        Borrowing amount  # noqa: E501

        :param borrow_amount: The borrow_amount of this CreateCollateralOrder.  # noqa: E501
        :type: str
        """
        if self.local_vars_configuration.client_side_validation and borrow_amount is None:  # noqa: E501
            raise ValueError("Invalid value for `borrow_amount`, must not be `None`")  # noqa: E501

        self._borrow_amount = borrow_amount

    @property
    def borrow_currency(self):
        """Gets the borrow_currency of this CreateCollateralOrder.  # noqa: E501

        Borrowed currency  # noqa: E501

        :return: The borrow_currency of this CreateCollateralOrder.  # noqa: E501
        :rtype: str
        """
        return self._borrow_currency

    @borrow_currency.setter
    def borrow_currency(self, borrow_currency):
        """Sets the borrow_currency of this CreateCollateralOrder.

        Borrowed currency  # noqa: E501

        :param borrow_currency: The borrow_currency of this CreateCollateralOrder.  # noqa: E501
        :type: str
        """
        if self.local_vars_configuration.client_side_validation and borrow_currency is None:  # noqa: E501
            raise ValueError("Invalid value for `borrow_currency`, must not be `None`")  # noqa: E501

        self._borrow_currency = borrow_currency

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.openapi_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateCollateralOrder):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateCollateralOrder):
            return True

        return self.to_dict() != other.to_dict()
