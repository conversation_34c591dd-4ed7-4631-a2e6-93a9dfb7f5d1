# coding: utf-8

"""
    Gate API v4

    Welcome to Gate.io API  APIv4 provides spot, margin and futures trading operations. There are public APIs to retrieve the real-time market statistics, and private APIs which needs authentication to trade on user's behalf.  # noqa: E501

    Contact: <EMAIL>
    Generated by: https://openapi-generator.tech
"""


import pprint
import re  # noqa: F401

import six

from gate_api.configuration import Configuration


class FuturesIndexConstituents(object):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        'index': 'str',
        'constituents': 'list[IndexConstituent]'
    }

    attribute_map = {
        'index': 'index',
        'constituents': 'constituents'
    }

    def __init__(self, index=None, constituents=None, local_vars_configuration=None):  # noqa: E501
        # type: (str, list[IndexConstituent], Configuration) -> None
        """FuturesIndexConstituents - a model defined in OpenAPI"""  # noqa: E501
        if local_vars_configuration is None:
            local_vars_configuration = Configuration()
        self.local_vars_configuration = local_vars_configuration

        self._index = None
        self._constituents = None
        self.discriminator = None

        if index is not None:
            self.index = index
        if constituents is not None:
            self.constituents = constituents

    @property
    def index(self):
        """Gets the index of this FuturesIndexConstituents.  # noqa: E501

        Index name  # noqa: E501

        :return: The index of this FuturesIndexConstituents.  # noqa: E501
        :rtype: str
        """
        return self._index

    @index.setter
    def index(self, index):
        """Sets the index of this FuturesIndexConstituents.

        Index name  # noqa: E501

        :param index: The index of this FuturesIndexConstituents.  # noqa: E501
        :type: str
        """

        self._index = index

    @property
    def constituents(self):
        """Gets the constituents of this FuturesIndexConstituents.  # noqa: E501

        Constituents  # noqa: E501

        :return: The constituents of this FuturesIndexConstituents.  # noqa: E501
        :rtype: list[IndexConstituent]
        """
        return self._constituents

    @constituents.setter
    def constituents(self, constituents):
        """Sets the constituents of this FuturesIndexConstituents.

        Constituents  # noqa: E501

        :param constituents: The constituents of this FuturesIndexConstituents.  # noqa: E501
        :type: list[IndexConstituent]
        """

        self._constituents = constituents

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.openapi_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, FuturesIndexConstituents):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, FuturesIndexConstituents):
            return True

        return self.to_dict() != other.to_dict()
