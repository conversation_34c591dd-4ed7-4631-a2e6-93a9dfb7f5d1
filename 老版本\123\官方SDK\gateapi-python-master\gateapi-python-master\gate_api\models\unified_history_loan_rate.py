# coding: utf-8

"""
    Gate API v4

    Welcome to Gate.io API  APIv4 provides spot, margin and futures trading operations. There are public APIs to retrieve the real-time market statistics, and private APIs which needs authentication to trade on user's behalf.  # noqa: E501

    Contact: <EMAIL>
    Generated by: https://openapi-generator.tech
"""


import pprint
import re  # noqa: F401

import six

from gate_api.configuration import Configuration


class UnifiedHistoryLoanRate(object):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        'currency': 'str',
        'tier': 'str',
        'tier_up_rate': 'str',
        'rates': 'list[UnifiedHistoryLoanRateRates]'
    }

    attribute_map = {
        'currency': 'currency',
        'tier': 'tier',
        'tier_up_rate': 'tier_up_rate',
        'rates': 'rates'
    }

    def __init__(self, currency=None, tier=None, tier_up_rate=None, rates=None, local_vars_configuration=None):  # noqa: E501
        # type: (str, str, str, list[UnifiedHistoryLoanRateRates], Configuration) -> None
        """UnifiedHistoryLoanRate - a model defined in OpenAPI"""  # noqa: E501
        if local_vars_configuration is None:
            local_vars_configuration = Configuration()
        self.local_vars_configuration = local_vars_configuration

        self._currency = None
        self._tier = None
        self._tier_up_rate = None
        self._rates = None
        self.discriminator = None

        if currency is not None:
            self.currency = currency
        if tier is not None:
            self.tier = tier
        if tier_up_rate is not None:
            self.tier_up_rate = tier_up_rate
        if rates is not None:
            self.rates = rates

    @property
    def currency(self):
        """Gets the currency of this UnifiedHistoryLoanRate.  # noqa: E501

        Currency name  # noqa: E501

        :return: The currency of this UnifiedHistoryLoanRate.  # noqa: E501
        :rtype: str
        """
        return self._currency

    @currency.setter
    def currency(self, currency):
        """Sets the currency of this UnifiedHistoryLoanRate.

        Currency name  # noqa: E501

        :param currency: The currency of this UnifiedHistoryLoanRate.  # noqa: E501
        :type: str
        """

        self._currency = currency

    @property
    def tier(self):
        """Gets the tier of this UnifiedHistoryLoanRate.  # noqa: E501

        The VIP level of the floating rate required  # noqa: E501

        :return: The tier of this UnifiedHistoryLoanRate.  # noqa: E501
        :rtype: str
        """
        return self._tier

    @tier.setter
    def tier(self, tier):
        """Sets the tier of this UnifiedHistoryLoanRate.

        The VIP level of the floating rate required  # noqa: E501

        :param tier: The tier of this UnifiedHistoryLoanRate.  # noqa: E501
        :type: str
        """

        self._tier = tier

    @property
    def tier_up_rate(self):
        """Gets the tier_up_rate of this UnifiedHistoryLoanRate.  # noqa: E501

        VIP level corresponding floating rate  # noqa: E501

        :return: The tier_up_rate of this UnifiedHistoryLoanRate.  # noqa: E501
        :rtype: str
        """
        return self._tier_up_rate

    @tier_up_rate.setter
    def tier_up_rate(self, tier_up_rate):
        """Sets the tier_up_rate of this UnifiedHistoryLoanRate.

        VIP level corresponding floating rate  # noqa: E501

        :param tier_up_rate: The tier_up_rate of this UnifiedHistoryLoanRate.  # noqa: E501
        :type: str
        """

        self._tier_up_rate = tier_up_rate

    @property
    def rates(self):
        """Gets the rates of this UnifiedHistoryLoanRate.  # noqa: E501

        Historical interest rate information, one data per hour, the array size is determined by the page and limit parameters provided by the interface request parameters, sorted from recent to far in time  # noqa: E501

        :return: The rates of this UnifiedHistoryLoanRate.  # noqa: E501
        :rtype: list[UnifiedHistoryLoanRateRates]
        """
        return self._rates

    @rates.setter
    def rates(self, rates):
        """Sets the rates of this UnifiedHistoryLoanRate.

        Historical interest rate information, one data per hour, the array size is determined by the page and limit parameters provided by the interface request parameters, sorted from recent to far in time  # noqa: E501

        :param rates: The rates of this UnifiedHistoryLoanRate.  # noqa: E501
        :type: list[UnifiedHistoryLoanRateRates]
        """

        self._rates = rates

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.openapi_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, UnifiedHistoryLoanRate):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, UnifiedHistoryLoanRate):
            return True

        return self.to_dict() != other.to_dict()
