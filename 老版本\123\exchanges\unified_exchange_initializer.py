#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🔥 统一交易所初始化器 - 消除重复初始化逻辑
所有交易所统一使用此初始化器，避免重复代码
"""

import logging
import os
from typing import Dict, Any, Optional
from dataclasses import dataclass

@dataclass
class ExchangeConfig:
    """交易所配置数据类"""
    name: str
    default_leverage: int
    max_leverage: float
    recv_window: str = "5000"
    min_order_amount_usd: float = 90.0
    api_url: Optional[str] = None

class UnifiedExchangeInitializer:
    """🔥 统一交易所初始化器 - 消除三个交易所的重复初始化逻辑"""
    
    _instances = {}  # 单例模式，避免重复初始化
    
    def __init__(self, exchange_name: str):
        """初始化统一交易所组件"""
        self.exchange_name = exchange_name
        self.logger = logging.getLogger(f"{exchange_name}Exchange")
        
        # 🔥 核心：一次性初始化所有统一模块，避免重复
        self._init_unified_modules()
        self._init_exchange_config()
        
        self.logger.info(f"✅ {exchange_name}交易所统一初始化完成 - 🔥 零重复逻辑")

    def _init_unified_modules(self):
        """🔥 核心：统一初始化所有模块，避免在每个交易所重复"""
        try:
            from exchanges.currency_adapter import CurrencyAdapter
            from core.trading_rules_preloader import get_trading_rules_preloader
            from core.universal_token_system import get_universal_token_system
            from core.unified_opening_manager import get_opening_manager
            from core.unified_closing_manager import get_closing_manager
            
            # 🔥 一次性初始化，复用实例
            self.currency_adapter = CurrencyAdapter()
            self.rules_preloader = get_trading_rules_preloader()
            self.token_system = get_universal_token_system()
            self.opening_manager = get_opening_manager()
            self.closing_manager = get_closing_manager()
            
            self.logger.debug(f"✅ {self.exchange_name}统一模块初始化完成")
            
        except Exception as e:
            self.logger.error(f"❌ {self.exchange_name}统一模块初始化失败: {e}")
            raise

    def _init_exchange_config(self):
        """🔥 统一配置读取逻辑，避免在每个交易所重复"""
        try:
            # 🔥 统一配置读取模式
            config = self._get_unified_config()
            
            # 🔥 修复：统一使用3倍杠杆配置
            config_map = {
                "Gate.io": {
                    "leverage_key": "GATE_LEVERAGE",
                    "default_leverage": 3,
                    "max_leverage": 3.0,
                    "api_url": None
                },
                "Bybit": {
                    "leverage_key": "BYBIT_LEVERAGE",
                    "default_leverage": 3,
                    "max_leverage": 3.0,
                    "api_url": "BYBIT_API_URL"
                },
                "OKX": {
                    "leverage_key": "OKX_LEVERAGE",
                    "default_leverage": 3,
                    "max_leverage": 3.0,
                    "api_url": None
                }
            }
            
            exchange_config = config_map.get(self.exchange_name, config_map["Gate.io"])
            
            # 🔥 修复：统一的配置设置逻辑，确保类型转换
            # 获取杠杆配置并转换为数值类型
            leverage_value = config.get(exchange_config["leverage_key"], exchange_config["default_leverage"])
            max_leverage_value = config.get('MAX_LEVERAGE_RATIO', exchange_config["max_leverage"])
            
            # 确保所有值都是数值类型
            leverage_float = float(leverage_value) if isinstance(leverage_value, (int, float, str)) else float(exchange_config["default_leverage"])
            max_leverage_float = float(max_leverage_value) if isinstance(max_leverage_value, (int, float, str)) else float(exchange_config["max_leverage"])
            exchange_max_leverage_float = float(exchange_config["max_leverage"])
            
            self.config = ExchangeConfig(
                name=self.exchange_name,
                default_leverage=int(min(leverage_float, exchange_max_leverage_float)),
                max_leverage=min(max_leverage_float, exchange_max_leverage_float),
                recv_window=str(config.get("BYBIT_RECV_WINDOW", "5000")) if self.exchange_name == "Bybit" else "5000",
                min_order_amount_usd=float(config.get("MIN_ORDER_AMOUNT_USD", 90.0)),
                api_url=config.get(exchange_config["api_url"]) if exchange_config["api_url"] else None
            )
            
            self.logger.debug(f"✅ {self.exchange_name}配置加载完成: 杠杆={self.config.default_leverage}x")
            
        except Exception as e:
            self.logger.error(f"❌ {self.exchange_name}配置初始化失败: {e}")
            raise

    def _get_unified_config(self) -> Dict[str, Any]:
        """🔥 统一配置获取逻辑，优先使用配置管理器，回退到环境变量"""
        try:
            from config.settings import get_config
            settings = get_config()
            # 🔥 修复：统一使用3倍杠杆，确保返回正确的数值类型
            # 处理settings可能返回字符串的情况
            gate_leverage = settings.get("GATE_LEVERAGE", 3)
            bybit_leverage = settings.get("BYBIT_LEVERAGE", 3)
            okx_leverage = settings.get("OKX_LEVERAGE", 3)
            max_leverage_ratio = settings.get("MAX_LEVERAGE_RATIO", 3.0)
            min_order_amount = settings.get("MIN_ORDER_AMOUNT_USD", 90.0)
            
            return {
                "GATE_LEVERAGE": int(gate_leverage) if isinstance(gate_leverage, (int, float, str)) else 1,
                "BYBIT_LEVERAGE": int(bybit_leverage) if isinstance(bybit_leverage, (int, float, str)) else 1,
                "OKX_LEVERAGE": int(okx_leverage) if isinstance(okx_leverage, (int, float, str)) else 2,
                "MAX_LEVERAGE_RATIO": float(max_leverage_ratio) if isinstance(max_leverage_ratio, (int, float, str)) else 2.0,
                "BYBIT_RECV_WINDOW": str(settings.get("BYBIT_RECV_WINDOW", "5000")),
                "MIN_ORDER_AMOUNT_USD": float(min_order_amount) if isinstance(min_order_amount, (int, float, str)) else 90.0,
                "BYBIT_API_URL": settings.get("BYBIT_API_URL", "https://api.bybit.com")
            }
        except Exception as config_error:
            self.logger.warning(f"配置管理器获取失败，使用环境变量: {config_error}")
            # 🔥 修复：回退到环境变量时也使用3倍杠杆
            return {
                "GATE_LEVERAGE": int(os.getenv("GATE_LEVERAGE", "3")),
                "BYBIT_LEVERAGE": int(os.getenv("BYBIT_LEVERAGE", "3")),
                "OKX_LEVERAGE": int(os.getenv("OKX_LEVERAGE", "3")),
                "MAX_LEVERAGE_RATIO": float(os.getenv("MAX_LEVERAGE_RATIO", "3.0")),
                "BYBIT_RECV_WINDOW": os.getenv("BYBIT_RECV_WINDOW", "5000"),
                "MIN_ORDER_AMOUNT_USD": float(os.getenv("MIN_ORDER_AMOUNT_USD", "90.0")),
                "BYBIT_API_URL": os.getenv("BYBIT_API_URL", "https://api.bybit.com")
            }

    @classmethod
    def get_initializer(cls, exchange_name: str) -> 'UnifiedExchangeInitializer':
        """🔥 单例模式获取初始化器，避免重复创建"""
        if exchange_name not in cls._instances:
            cls._instances[exchange_name] = cls(exchange_name)
        return cls._instances[exchange_name]

    def get_all_modules(self) -> Dict[str, Any]:
        """获取所有初始化的统一模块"""
        return {
            "currency_adapter": self.currency_adapter,
            "rules_preloader": self.rules_preloader,
            "token_system": self.token_system,
            "opening_manager": self.opening_manager,
            "closing_manager": self.closing_manager,
            "config": self.config,
            "logger": self.logger
        }

    def setup_exchange_attributes(self, exchange_instance) -> None:
        """🔥 统一设置交易所属性 - 消除重复的属性设置代码"""
        try:
            # 🔥 核心：一次性设置所有统一模块属性，避免在每个交易所重复
            exchange_instance.currency_adapter = self.currency_adapter
            exchange_instance.trading_rules_preloader = self.rules_preloader
            exchange_instance.token_system = self.token_system
            exchange_instance.opening_manager = self.opening_manager
            exchange_instance.closing_manager = self.closing_manager
            exchange_instance.unified_config = self.config
            
            # 🔥 统一配置属性设置
            exchange_instance.default_leverage = self.config.default_leverage
            exchange_instance.max_leverage = self.config.max_leverage
            
            # 🔥 Bybit特有配置
            if self.exchange_name == "Bybit":
                exchange_instance.recv_window = self.config.recv_window
                exchange_instance.min_order_amount_usd = self.config.min_order_amount_usd
                # 🔥 API URL从统一配置获取
                if self.config.api_url:
                    exchange_instance.base_url = self.config.api_url
            
            self.logger.debug(f"✅ {self.exchange_name}统一属性设置完成 - trading_rules_preloader已正确设置")
            
        except Exception as e:
            self.logger.error(f"❌ {self.exchange_name}属性设置失败: {e}")
            raise

# 🔥 全局快速访问函数
def get_exchange_initializer(exchange_name: str) -> UnifiedExchangeInitializer:
    """快速获取交易所统一初始化器"""
    return UnifiedExchangeInitializer.get_initializer(exchange_name)

def init_exchange_modules(exchange_name: str) -> Dict[str, Any]:
    """一键初始化交易所所有模块"""
    initializer = get_exchange_initializer(exchange_name)
    return initializer.get_all_modules()

def setup_exchange_unified(exchange_name: str, exchange_instance) -> None:
    """🔥 一键设置交易所统一属性 - 最简化的交易所初始化方式"""
    initializer = get_exchange_initializer(exchange_name)
    initializer.setup_exchange_attributes(exchange_instance) 