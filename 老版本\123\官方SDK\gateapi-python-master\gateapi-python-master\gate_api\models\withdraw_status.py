# coding: utf-8

"""
    Gate API v4

    Welcome to Gate.io API  APIv4 provides spot, margin and futures trading operations. There are public APIs to retrieve the real-time market statistics, and private APIs which needs authentication to trade on user's behalf.  # noqa: E501

    Contact: <EMAIL>
    Generated by: https://openapi-generator.tech
"""


import pprint
import re  # noqa: F401

import six

from gate_api.configuration import Configuration


class WithdrawStatus(object):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        'currency': 'str',
        'name': 'str',
        'name_cn': 'str',
        'deposit': 'str',
        'withdraw_percent': 'str',
        'withdraw_fix': 'str',
        'withdraw_day_limit': 'str',
        'withdraw_amount_mini': 'str',
        'withdraw_day_limit_remain': 'str',
        'withdraw_eachtime_limit': 'str',
        'withdraw_fix_on_chains': 'dict(str, str)',
        'withdraw_percent_on_chains': 'dict(str, str)'
    }

    attribute_map = {
        'currency': 'currency',
        'name': 'name',
        'name_cn': 'name_cn',
        'deposit': 'deposit',
        'withdraw_percent': 'withdraw_percent',
        'withdraw_fix': 'withdraw_fix',
        'withdraw_day_limit': 'withdraw_day_limit',
        'withdraw_amount_mini': 'withdraw_amount_mini',
        'withdraw_day_limit_remain': 'withdraw_day_limit_remain',
        'withdraw_eachtime_limit': 'withdraw_eachtime_limit',
        'withdraw_fix_on_chains': 'withdraw_fix_on_chains',
        'withdraw_percent_on_chains': 'withdraw_percent_on_chains'
    }

    def __init__(self, currency=None, name=None, name_cn=None, deposit=None, withdraw_percent=None, withdraw_fix=None, withdraw_day_limit=None, withdraw_amount_mini=None, withdraw_day_limit_remain=None, withdraw_eachtime_limit=None, withdraw_fix_on_chains=None, withdraw_percent_on_chains=None, local_vars_configuration=None):  # noqa: E501
        # type: (str, str, str, str, str, str, str, str, str, str, dict(str, str), dict(str, str), Configuration) -> None
        """WithdrawStatus - a model defined in OpenAPI"""  # noqa: E501
        if local_vars_configuration is None:
            local_vars_configuration = Configuration()
        self.local_vars_configuration = local_vars_configuration

        self._currency = None
        self._name = None
        self._name_cn = None
        self._deposit = None
        self._withdraw_percent = None
        self._withdraw_fix = None
        self._withdraw_day_limit = None
        self._withdraw_amount_mini = None
        self._withdraw_day_limit_remain = None
        self._withdraw_eachtime_limit = None
        self._withdraw_fix_on_chains = None
        self._withdraw_percent_on_chains = None
        self.discriminator = None

        if currency is not None:
            self.currency = currency
        if name is not None:
            self.name = name
        if name_cn is not None:
            self.name_cn = name_cn
        if deposit is not None:
            self.deposit = deposit
        if withdraw_percent is not None:
            self.withdraw_percent = withdraw_percent
        if withdraw_fix is not None:
            self.withdraw_fix = withdraw_fix
        if withdraw_day_limit is not None:
            self.withdraw_day_limit = withdraw_day_limit
        if withdraw_amount_mini is not None:
            self.withdraw_amount_mini = withdraw_amount_mini
        if withdraw_day_limit_remain is not None:
            self.withdraw_day_limit_remain = withdraw_day_limit_remain
        if withdraw_eachtime_limit is not None:
            self.withdraw_eachtime_limit = withdraw_eachtime_limit
        if withdraw_fix_on_chains is not None:
            self.withdraw_fix_on_chains = withdraw_fix_on_chains
        if withdraw_percent_on_chains is not None:
            self.withdraw_percent_on_chains = withdraw_percent_on_chains

    @property
    def currency(self):
        """Gets the currency of this WithdrawStatus.  # noqa: E501

        Currency  # noqa: E501

        :return: The currency of this WithdrawStatus.  # noqa: E501
        :rtype: str
        """
        return self._currency

    @currency.setter
    def currency(self, currency):
        """Sets the currency of this WithdrawStatus.

        Currency  # noqa: E501

        :param currency: The currency of this WithdrawStatus.  # noqa: E501
        :type: str
        """

        self._currency = currency

    @property
    def name(self):
        """Gets the name of this WithdrawStatus.  # noqa: E501

        Currency name  # noqa: E501

        :return: The name of this WithdrawStatus.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this WithdrawStatus.

        Currency name  # noqa: E501

        :param name: The name of this WithdrawStatus.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def name_cn(self):
        """Gets the name_cn of this WithdrawStatus.  # noqa: E501

        Currency Chinese name  # noqa: E501

        :return: The name_cn of this WithdrawStatus.  # noqa: E501
        :rtype: str
        """
        return self._name_cn

    @name_cn.setter
    def name_cn(self, name_cn):
        """Sets the name_cn of this WithdrawStatus.

        Currency Chinese name  # noqa: E501

        :param name_cn: The name_cn of this WithdrawStatus.  # noqa: E501
        :type: str
        """

        self._name_cn = name_cn

    @property
    def deposit(self):
        """Gets the deposit of this WithdrawStatus.  # noqa: E501

        Deposits fee  # noqa: E501

        :return: The deposit of this WithdrawStatus.  # noqa: E501
        :rtype: str
        """
        return self._deposit

    @deposit.setter
    def deposit(self, deposit):
        """Sets the deposit of this WithdrawStatus.

        Deposits fee  # noqa: E501

        :param deposit: The deposit of this WithdrawStatus.  # noqa: E501
        :type: str
        """

        self._deposit = deposit

    @property
    def withdraw_percent(self):
        """Gets the withdraw_percent of this WithdrawStatus.  # noqa: E501

        Withdrawal fee rate percentage  # noqa: E501

        :return: The withdraw_percent of this WithdrawStatus.  # noqa: E501
        :rtype: str
        """
        return self._withdraw_percent

    @withdraw_percent.setter
    def withdraw_percent(self, withdraw_percent):
        """Sets the withdraw_percent of this WithdrawStatus.

        Withdrawal fee rate percentage  # noqa: E501

        :param withdraw_percent: The withdraw_percent of this WithdrawStatus.  # noqa: E501
        :type: str
        """

        self._withdraw_percent = withdraw_percent

    @property
    def withdraw_fix(self):
        """Gets the withdraw_fix of this WithdrawStatus.  # noqa: E501

        Fixed withdrawal fee  # noqa: E501

        :return: The withdraw_fix of this WithdrawStatus.  # noqa: E501
        :rtype: str
        """
        return self._withdraw_fix

    @withdraw_fix.setter
    def withdraw_fix(self, withdraw_fix):
        """Sets the withdraw_fix of this WithdrawStatus.

        Fixed withdrawal fee  # noqa: E501

        :param withdraw_fix: The withdraw_fix of this WithdrawStatus.  # noqa: E501
        :type: str
        """

        self._withdraw_fix = withdraw_fix

    @property
    def withdraw_day_limit(self):
        """Gets the withdraw_day_limit of this WithdrawStatus.  # noqa: E501

        Daily allowed withdrawal amount  # noqa: E501

        :return: The withdraw_day_limit of this WithdrawStatus.  # noqa: E501
        :rtype: str
        """
        return self._withdraw_day_limit

    @withdraw_day_limit.setter
    def withdraw_day_limit(self, withdraw_day_limit):
        """Sets the withdraw_day_limit of this WithdrawStatus.

        Daily allowed withdrawal amount  # noqa: E501

        :param withdraw_day_limit: The withdraw_day_limit of this WithdrawStatus.  # noqa: E501
        :type: str
        """

        self._withdraw_day_limit = withdraw_day_limit

    @property
    def withdraw_amount_mini(self):
        """Gets the withdraw_amount_mini of this WithdrawStatus.  # noqa: E501

        Minimum withdrawal amount  # noqa: E501

        :return: The withdraw_amount_mini of this WithdrawStatus.  # noqa: E501
        :rtype: str
        """
        return self._withdraw_amount_mini

    @withdraw_amount_mini.setter
    def withdraw_amount_mini(self, withdraw_amount_mini):
        """Sets the withdraw_amount_mini of this WithdrawStatus.

        Minimum withdrawal amount  # noqa: E501

        :param withdraw_amount_mini: The withdraw_amount_mini of this WithdrawStatus.  # noqa: E501
        :type: str
        """

        self._withdraw_amount_mini = withdraw_amount_mini

    @property
    def withdraw_day_limit_remain(self):
        """Gets the withdraw_day_limit_remain of this WithdrawStatus.  # noqa: E501

        Daily withdrawal amount left  # noqa: E501

        :return: The withdraw_day_limit_remain of this WithdrawStatus.  # noqa: E501
        :rtype: str
        """
        return self._withdraw_day_limit_remain

    @withdraw_day_limit_remain.setter
    def withdraw_day_limit_remain(self, withdraw_day_limit_remain):
        """Sets the withdraw_day_limit_remain of this WithdrawStatus.

        Daily withdrawal amount left  # noqa: E501

        :param withdraw_day_limit_remain: The withdraw_day_limit_remain of this WithdrawStatus.  # noqa: E501
        :type: str
        """

        self._withdraw_day_limit_remain = withdraw_day_limit_remain

    @property
    def withdraw_eachtime_limit(self):
        """Gets the withdraw_eachtime_limit of this WithdrawStatus.  # noqa: E501

        Maximum amount for each withdrawal  # noqa: E501

        :return: The withdraw_eachtime_limit of this WithdrawStatus.  # noqa: E501
        :rtype: str
        """
        return self._withdraw_eachtime_limit

    @withdraw_eachtime_limit.setter
    def withdraw_eachtime_limit(self, withdraw_eachtime_limit):
        """Sets the withdraw_eachtime_limit of this WithdrawStatus.

        Maximum amount for each withdrawal  # noqa: E501

        :param withdraw_eachtime_limit: The withdraw_eachtime_limit of this WithdrawStatus.  # noqa: E501
        :type: str
        """

        self._withdraw_eachtime_limit = withdraw_eachtime_limit

    @property
    def withdraw_fix_on_chains(self):
        """Gets the withdraw_fix_on_chains of this WithdrawStatus.  # noqa: E501

        Fixed withdrawal fee on multiple chains  # noqa: E501

        :return: The withdraw_fix_on_chains of this WithdrawStatus.  # noqa: E501
        :rtype: dict(str, str)
        """
        return self._withdraw_fix_on_chains

    @withdraw_fix_on_chains.setter
    def withdraw_fix_on_chains(self, withdraw_fix_on_chains):
        """Sets the withdraw_fix_on_chains of this WithdrawStatus.

        Fixed withdrawal fee on multiple chains  # noqa: E501

        :param withdraw_fix_on_chains: The withdraw_fix_on_chains of this WithdrawStatus.  # noqa: E501
        :type: dict(str, str)
        """

        self._withdraw_fix_on_chains = withdraw_fix_on_chains

    @property
    def withdraw_percent_on_chains(self):
        """Gets the withdraw_percent_on_chains of this WithdrawStatus.  # noqa: E501

        Percentage withdrawal fee on multiple chains  # noqa: E501

        :return: The withdraw_percent_on_chains of this WithdrawStatus.  # noqa: E501
        :rtype: dict(str, str)
        """
        return self._withdraw_percent_on_chains

    @withdraw_percent_on_chains.setter
    def withdraw_percent_on_chains(self, withdraw_percent_on_chains):
        """Sets the withdraw_percent_on_chains of this WithdrawStatus.

        Percentage withdrawal fee on multiple chains  # noqa: E501

        :param withdraw_percent_on_chains: The withdraw_percent_on_chains of this WithdrawStatus.  # noqa: E501
        :type: dict(str, str)
        """

        self._withdraw_percent_on_chains = withdraw_percent_on_chains

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.openapi_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, WithdrawStatus):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, WithdrawStatus):
            return True

        return self.to_dict() != other.to_dict()
